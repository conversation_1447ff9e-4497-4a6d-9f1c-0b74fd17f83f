package com.oasis.callblocker.data.local.database

import android.content.Context
import androidx.room.Room
import androidx.test.core.app.ApplicationProvider
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.oasis.callblocker.data.local.database.dao.BlockNumberDao
import com.oasis.callblocker.data.local.database.dao.UserDao
import com.oasis.callblocker.data.local.database.entities.BlockNumberEntity
import com.oasis.callblocker.data.local.database.entities.UserEntity
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.test.runTest
import org.junit.After
import org.junit.Assert.*
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith

/**
 * 数据库集成测试
 */
@RunWith(AndroidJUnit4::class)
class OasisDatabaseTest {
    
    private lateinit var database: OasisDatabase
    private lateinit var blockNumberDao: BlockNumberDao
    private lateinit var userDao: UserDao
    
    @Before
    fun setup() {
        val context = ApplicationProvider.getApplicationContext<Context>()
        database = Room.inMemoryDatabaseBuilder(
            context,
            OasisDatabase::class.java
        ).build()
        
        blockNumberDao = database.blockNumberDao()
        userDao = database.userDao()
    }
    
    @After
    fun tearDown() {
        database.close()
    }
    
    @Test
    fun testInsertAndGetBlockNumber() = runTest {
        // Given
        val blockNumber = BlockNumberEntity(
            phoneNumber = "13812345678",
            blockType = 0,
            todayCount = 1,
            createdAt = System.currentTimeMillis(),
            updatedAt = System.currentTimeMillis()
        )
        
        // When
        blockNumberDao.insertBlockNumber(blockNumber)
        val retrieved = blockNumberDao.getBlockNumber("13812345678")
        
        // Then
        assertNotNull(retrieved)
        assertEquals(blockNumber.phoneNumber, retrieved?.phoneNumber)
        assertEquals(blockNumber.blockType, retrieved?.blockType)
        assertEquals(blockNumber.todayCount, retrieved?.todayCount)
    }
    
    @Test
    fun testGetAllBlockNumbers() = runTest {
        // Given
        val blockNumbers = listOf(
            BlockNumberEntity("13812345678", 0, 1),
            BlockNumberEntity("13987654321", 1, 2),
            BlockNumberEntity("15612345678", 0, 0)
        )
        
        // When
        blockNumbers.forEach { blockNumberDao.insertBlockNumber(it) }
        val allBlockNumbers = blockNumberDao.getAllBlockNumbers().first()
        
        // Then
        assertEquals(3, allBlockNumbers.size)
        assertTrue(allBlockNumbers.any { it.phoneNumber == "13812345678" })
        assertTrue(allBlockNumbers.any { it.phoneNumber == "13987654321" })
        assertTrue(allBlockNumbers.any { it.phoneNumber == "15612345678" })
    }
    
    @Test
    fun testGetBlockNumbersByType() = runTest {
        // Given
        val blockNumbers = listOf(
            BlockNumberEntity("13812345678", 0, 1), // 特定号码
            BlockNumberEntity("13987654321", 1, 2), // 前缀
            BlockNumberEntity("15612345678", 0, 0)  // 特定号码
        )
        
        // When
        blockNumbers.forEach { blockNumberDao.insertBlockNumber(it) }
        val specificNumbers = blockNumberDao.getBlockNumbersByType(0).first()
        val prefixNumbers = blockNumberDao.getBlockNumbersByType(1).first()
        
        // Then
        assertEquals(2, specificNumbers.size)
        assertEquals(1, prefixNumbers.size)
        assertTrue(specificNumbers.any { it.phoneNumber == "13812345678" })
        assertTrue(specificNumbers.any { it.phoneNumber == "15612345678" })
        assertTrue(prefixNumbers.any { it.phoneNumber == "13987654321" })
    }
    
    @Test
    fun testUpdateBlockNumber() = runTest {
        // Given
        val blockNumber = BlockNumberEntity("13812345678", 0, 1)
        blockNumberDao.insertBlockNumber(blockNumber)
        
        // When
        val updatedBlockNumber = blockNumber.copy(todayCount = 5)
        blockNumberDao.updateBlockNumber(updatedBlockNumber)
        val retrieved = blockNumberDao.getBlockNumber("13812345678")
        
        // Then
        assertNotNull(retrieved)
        assertEquals(5, retrieved?.todayCount)
    }
    
    @Test
    fun testDeleteBlockNumber() = runTest {
        // Given
        val blockNumber = BlockNumberEntity("13812345678", 0, 1)
        blockNumberDao.insertBlockNumber(blockNumber)
        
        // When
        blockNumberDao.deleteBlockNumber("13812345678")
        val retrieved = blockNumberDao.getBlockNumber("13812345678")
        
        // Then
        assertNull(retrieved)
    }
    
    @Test
    fun testResetTodayCount() = runTest {
        // Given
        val blockNumbers = listOf(
            BlockNumberEntity("13812345678", 0, 5),
            BlockNumberEntity("13987654321", 1, 3),
            BlockNumberEntity("15612345678", 0, 2)
        )
        blockNumbers.forEach { blockNumberDao.insertBlockNumber(it) }
        
        // When
        blockNumberDao.resetTodayCount()
        val allBlockNumbers = blockNumberDao.getAllBlockNumbers().first()
        
        // Then
        assertTrue(allBlockNumbers.all { it.todayCount == 0 })
    }
    
    @Test
    fun testInsertAndGetUser() = runTest {
        // Given
        val user = UserEntity(
            userId = "user123",
            userEmail = "<EMAIL>",
            userCompany = "Test Company",
            deviceToken = "device_token",
            authToken = "auth_token",
            licenseEndDate = "2024-12-31",
            remainQueryCount = 100,
            createdAt = System.currentTimeMillis(),
            updatedAt = System.currentTimeMillis()
        )
        
        // When
        userDao.insertUser(user)
        val retrieved = userDao.getUser("user123")
        
        // Then
        assertNotNull(retrieved)
        assertEquals(user.userId, retrieved?.userId)
        assertEquals(user.userEmail, retrieved?.userEmail)
        assertEquals(user.userCompany, retrieved?.userCompany)
        assertEquals(user.remainQueryCount, retrieved?.remainQueryCount)
    }
    
    @Test
    fun testGetCurrentUser() = runTest {
        // Given
        val user = UserEntity(
            userId = "user123",
            userEmail = "<EMAIL>",
            userCompany = "Test Company"
        )
        
        // When
        userDao.insertUser(user)
        val currentUser = userDao.getCurrentUser()
        
        // Then
        assertNotNull(currentUser)
        assertEquals(user.userId, currentUser?.userId)
    }
    
    @Test
    fun testUpdateQueryCount() = runTest {
        // Given
        val user = UserEntity(
            userId = "user123",
            userEmail = "<EMAIL>",
            remainQueryCount = 100
        )
        userDao.insertUser(user)
        
        // When
        userDao.updateQueryCount("user123", 50)
        val updated = userDao.getUser("user123")
        
        // Then
        assertNotNull(updated)
        assertEquals(50, updated?.remainQueryCount)
    }
    
    @Test
    fun testUserReplaceOnConflict() = runTest {
        // Given
        val user1 = UserEntity(
            userId = "user123",
            userEmail = "<EMAIL>",
            userCompany = "Company A",
            remainQueryCount = 100
        )
        val user2 = UserEntity(
            userId = "user123", // 相同ID
            userEmail = "<EMAIL>",
            userCompany = "Company B", // 不同公司
            remainQueryCount = 200 // 不同查询次数
        )
        
        // When
        userDao.insertUser(user1)
        userDao.insertUser(user2) // 应该替换user1
        val retrieved = userDao.getUser("user123")
        
        // Then
        assertNotNull(retrieved)
        assertEquals("Company B", retrieved?.userCompany)
        assertEquals(200, retrieved?.remainQueryCount)
    }
}
