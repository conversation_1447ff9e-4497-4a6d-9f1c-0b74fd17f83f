<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:padding="16dp"
    android:background="@drawable/floating_window_normal_bg"
    android:elevation="8dp">

    <TextView
        android:id="@+id/tv_contact_name"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="联系人姓名"
        android:textSize="16sp"
        android:textStyle="bold"
        android:textColor="@android:color/white"
        android:maxLines="1"
        android:ellipsize="end" />

    <TextView
        android:id="@+id/tv_phone_number"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="电话号码"
        android:textSize="14sp"
        android:textColor="@android:color/white"
        android:layout_marginTop="4dp"
        android:maxLines="1" />

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:layout_marginTop="8dp">

        <TextView
            android:id="@+id/tv_call_status"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="状态"
            android:textSize="12sp"
            android:textColor="@android:color/white"
            android:background="@drawable/status_bg"
            android:padding="4dp"
            android:layout_marginEnd="8dp" />

        <TextView
            android:id="@+id/tv_call_state"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="来电中"
            android:textSize="12sp"
            android:textColor="@android:color/white" />

    </LinearLayout>

</LinearLayout>
