package com.oasis.callblocker.receiver

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.util.Log
import com.oasis.callblocker.service.background.BackgroundSyncService
import dagger.hilt.android.AndroidEntryPoint

/**
 * 开机启动接收器
 * 对应原来的BootReceiver.java
 */
@AndroidEntryPoint
class BootReceiver : BroadcastReceiver() {
    
    companion object {
        private const val TAG = "BootReceiver"
    }
    
    override fun onReceive(context: Context, intent: Intent) {
        when (intent.action) {
            Intent.ACTION_BOOT_COMPLETED,
            Intent.ACTION_MY_PACKAGE_REPLACED,
            Intent.ACTION_PACKAGE_REPLACED -> {
                Log.d(TAG, "收到系统启动或应用更新广播: ${intent.action}")
                startBackgroundService(context)
            }
        }
    }
    
    /**
     * 启动后台服务
     */
    private fun startBackgroundService(context: Context) {
        try {
            val serviceIntent = Intent(context, BackgroundSyncService::class.java).apply {
                action = BackgroundSyncService.ACTION_START_SYNC
            }
            context.startForegroundService(serviceIntent)
            Log.d(TAG, "后台服务启动成功")
        } catch (e: Exception) {
            Log.e(TAG, "启动后台服务失败", e)
        }
    }
}
