package com.oasis.callblocker.data.local.database.entities

import androidx.room.Entity
import androidx.room.PrimaryKey

/**
 * 搜索结果数据库实体
 * 对应原来的SearchResultData.java
 */
@Entity(tableName = "search_results")
data class SearchResultEntity(
    @PrimaryKey
    val phoneNumber: String,
    val ownerName: String? = null,
    val location: String? = null,
    val carrier: String? = null, // 运营商
    val numberType: String? = null, // 号码类型（手机、固话等）
    val riskLevel: Int = 0, // 风险等级：0=安全, 1=可疑, 2=危险
    val reportCount: Int = 0, // 举报次数
    val lastSearchTime: Long = System.currentTimeMillis(),
    val cacheExpireTime: Long = System.currentTimeMillis() + 24 * 60 * 60 * 1000 // 缓存24小时
) {
    companion object {
        const val RISK_LEVEL_SAFE = 0      // 安全
        const val RISK_LEVEL_SUSPICIOUS = 1 // 可疑
        const val RISK_LEVEL_DANGEROUS = 2  // 危险
    }
}
