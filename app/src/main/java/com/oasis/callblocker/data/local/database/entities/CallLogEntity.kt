package com.oasis.callblocker.data.local.database.entities

import androidx.room.Entity
import androidx.room.PrimaryKey

/**
 * 通话记录数据库实体
 * 对应原来的RecentCallData.java和RecentIncomeInfo.java
 */
@Entity(tableName = "call_logs")
data class CallLogEntity(
    @PrimaryKey(autoGenerate = true)
    val id: Long = 0,
    val phoneNumber: String,
    val contactName: String? = null,
    val callType: Int, // 通话类型：0=来电, 1=去电, 2=未接
    val duration: Long = 0, // 通话时长（秒）
    val timestamp: Long = System.currentTimeMillis(),
    val isBlocked: Boolean = false // 是否被拦截
) {
    companion object {
        const val CALL_TYPE_INCOMING = 0  // 来电
        const val CALL_TYPE_OUTGOING = 1  // 去电
        const val CALL_TYPE_MISSED = 2    // 未接
    }
}
