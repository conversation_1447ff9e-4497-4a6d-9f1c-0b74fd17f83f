package com.oasis.callblocker.data.remote.interceptors

import android.util.Log
import okhttp3.Interceptor
import okhttp3.Response
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 日志拦截器
 * 记录网络请求和响应信息
 */
@Singleton
class LoggingInterceptor @Inject constructor() : Interceptor {
    
    companion object {
        private const val TAG = "OasisNetwork"
    }
    
    override fun intercept(chain: Interceptor.Chain): Response {
        val request = chain.request()
        val startTime = System.currentTimeMillis()
        
        Log.d(TAG, "发送请求: ${request.method} ${request.url}")
        
        val response = chain.proceed(request)
        val endTime = System.currentTimeMillis()
        
        Log.d(TAG, "收到响应: ${response.code} ${request.url} (${endTime - startTime}ms)")
        
        return response
    }
}
