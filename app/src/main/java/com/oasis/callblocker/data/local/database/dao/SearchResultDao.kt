package com.oasis.callblocker.data.local.database.dao

import androidx.room.*
import com.oasis.callblocker.data.local.database.entities.SearchResultEntity
import kotlinx.coroutines.flow.Flow

/**
 * 搜索结果数据访问对象
 */
@Dao
interface SearchResultDao {
    
    @Query("SELECT * FROM search_results ORDER BY lastSearchTime DESC LIMIT :limit")
    fun getRecentSearchResults(limit: Int = 50): Flow<List<SearchResultEntity>>
    
    @Query("SELECT * FROM search_results WHERE phoneNumber = :phoneNumber")
    suspend fun getSearchResult(phoneNumber: String): SearchResultEntity?
    
    @Query("SELECT * FROM search_results WHERE cacheExpireTime > :currentTime")
    suspend fun getValidCachedResults(currentTime: Long = System.currentTimeMillis()): List<SearchResultEntity>
    
    @Query("SELECT * FROM search_results WHERE riskLevel = :riskLevel ORDER BY lastSearchTime DESC")
    fun getSearchResultsByRiskLevel(riskLevel: Int): Flow<List<SearchResultEntity>>
    
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertSearchResult(searchResult: SearchResultEntity)
    
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertSearchResults(searchResults: List<SearchResultEntity>)
    
    @Update
    suspend fun updateSearchResult(searchResult: SearchResultEntity)
    
    @Query("UPDATE search_results SET lastSearchTime = :timestamp WHERE phoneNumber = :phoneNumber")
    suspend fun updateLastSearchTime(phoneNumber: String, timestamp: Long = System.currentTimeMillis())
    
    @Delete
    suspend fun deleteSearchResult(searchResult: SearchResultEntity)
    
    @Query("DELETE FROM search_results WHERE phoneNumber = :phoneNumber")
    suspend fun deleteSearchResult(phoneNumber: String)
    
    @Query("DELETE FROM search_results WHERE cacheExpireTime < :currentTime")
    suspend fun deleteExpiredResults(currentTime: Long = System.currentTimeMillis())
    
    @Query("DELETE FROM search_results")
    suspend fun deleteAllSearchResults()
}
