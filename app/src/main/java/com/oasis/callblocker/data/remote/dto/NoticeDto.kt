package com.oasis.callblocker.data.remote.dto

import com.google.gson.annotations.SerializedName

/**
 * 通知数据传输对象
 */
data class NoticeDto(
    @SerializedName("noticeId")
    val noticeId: String,
    
    @SerializedName("title")
    val title: String,
    
    @SerializedName("content")
    val content: String,
    
    @SerializedName("noticeType")
    val noticeType: Int = 0,
    
    @SerializedName("publishTime")
    val publishTime: Long,
    
    @SerializedName("isImportant")
    val isImportant: Boolean = false
)

/**
 * 通知列表响应数据传输对象
 */
data class NoticeListResponseDto(
    @SerializedName("success")
    val success: Boolean,
    
    @SerializedName("message")
    val message: String? = null,
    
    @SerializedName("notices")
    val notices: List<NoticeDto>? = null,
    
    @SerializedName("hasNewNotice")
    val hasNewNotice: Boolean = false
)

/**
 * 检查新通知响应数据传输对象
 */
data class CheckNoticeResponseDto(
    @SerializedName("success")
    val success: Boolean,
    
    @SerializedName("hasNewNotice")
    val hasNewNotice: Boolean = false,
    
    @SerializedName("newNoticeCount")
    val newNoticeCount: Int = 0
)
