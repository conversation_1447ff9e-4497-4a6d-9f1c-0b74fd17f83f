package com.oasis.callblocker.data.local.database.entities

import androidx.room.Entity
import androidx.room.PrimaryKey

/**
 * 通知信息数据库实体
 * 对应原来的NoticeInfo.java
 */
@Entity(tableName = "notices")
data class NoticeEntity(
    @PrimaryKey
    val noticeId: String,
    val title: String,
    val content: String,
    val noticeType: Int = 0, // 通知类型：0=普通, 1=重要, 2=紧急
    val isRead: Boolean = false,
    val publishTime: Long,
    val createdAt: Long = System.currentTimeMillis()
) {
    companion object {
        const val NOTICE_TYPE_NORMAL = 0    // 普通通知
        const val NOTICE_TYPE_IMPORTANT = 1 // 重要通知
        const val NOTICE_TYPE_URGENT = 2    // 紧急通知
    }
}
