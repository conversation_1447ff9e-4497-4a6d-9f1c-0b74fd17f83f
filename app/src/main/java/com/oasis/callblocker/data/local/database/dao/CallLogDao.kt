package com.oasis.callblocker.data.local.database.dao

import androidx.room.*
import com.oasis.callblocker.data.local.database.entities.CallLogEntity
import kotlinx.coroutines.flow.Flow

/**
 * 通话记录数据访问对象
 */
@Dao
interface CallLogDao {
    
    @Query("SELECT * FROM call_logs ORDER BY timestamp DESC LIMIT :limit")
    fun getRecentCallLogs(limit: Int = 50): Flow<List<CallLogEntity>>
    
    @Query("SELECT * FROM call_logs WHERE callType = :callType ORDER BY timestamp DESC LIMIT :limit")
    fun getCallLogsByType(callType: Int, limit: Int = 50): Flow<List<CallLogEntity>>
    
    @Query("SELECT * FROM call_logs WHERE isBlocked = 1 ORDER BY timestamp DESC LIMIT :limit")
    fun getBlockedCallLogs(limit: Int = 50): Flow<List<CallLogEntity>>
    
    @Query("SELECT * FROM call_logs WHERE phoneNumber = :phoneNumber ORDER BY timestamp DESC")
    fun getCallLogsByNumber(phoneNumber: String): Flow<List<CallLogEntity>>
    
    @Query("SELECT * FROM call_logs WHERE timestamp >= :startTime AND timestamp <= :endTime ORDER BY timestamp DESC")
    fun getCallLogsByTimeRange(startTime: Long, endTime: Long): Flow<List<CallLogEntity>>
    
    @Query("SELECT COUNT(*) FROM call_logs WHERE phoneNumber = :phoneNumber AND timestamp >= :startTime")
    suspend fun getCallCountSince(phoneNumber: String, startTime: Long): Int
    
    @Insert
    suspend fun insertCallLog(callLog: CallLogEntity)
    
    @Insert
    suspend fun insertCallLogs(callLogs: List<CallLogEntity>)
    
    @Update
    suspend fun updateCallLog(callLog: CallLogEntity)
    
    @Delete
    suspend fun deleteCallLog(callLog: CallLogEntity)
    
    @Query("DELETE FROM call_logs WHERE id = :id")
    suspend fun deleteCallLog(id: Long)
    
    @Query("DELETE FROM call_logs WHERE timestamp < :beforeTime")
    suspend fun deleteOldCallLogs(beforeTime: Long)
    
    @Query("DELETE FROM call_logs")
    suspend fun deleteAllCallLogs()
}
