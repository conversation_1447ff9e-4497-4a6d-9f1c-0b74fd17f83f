package com.oasis.callblocker.data.local.database.entities

import androidx.room.Entity
import androidx.room.PrimaryKey

/**
 * 拦截号码数据库实体
 * 对应原来的BlockNumberData.java
 */
@Entity(tableName = "block_numbers")
data class BlockNumberEntity(
    @PrimaryKey
    val phoneNumber: String,
    val blockType: Int, // 拦截类型：0=特定号码, 1=前缀, 2=呼叫爆炸
    val todayCount: Int = 0, // 今日拦截次数
    val createdAt: Long = System.currentTimeMillis(),
    val updatedAt: Long = System.currentTimeMillis()
) {
    companion object {
        const val BLOCK_TYPE_SPECIFIC = 0  // 特定号码
        const val BLOCK_TYPE_PREFIX = 1    // 前缀拦截
        const val BLOCK_TYPE_EXPLOSION = 2 // 呼叫爆炸
    }
}
