package com.oasis.callblocker.data.local.database.entities

import androidx.room.Entity
import androidx.room.PrimaryKey

/**
 * 用户信息数据库实体
 * 对应原来的UserInfo.java
 */
@Entity(tableName = "users")
data class UserEntity(
    @PrimaryKey
    val userId: String,
    val userEmail: String,
    val userCompany: String? = null,
    val deviceToken: String? = null,
    val authToken: String? = null,
    val licenseEndDate: String? = null,
    val remainQueryCount: Int = 0,
    val createdAt: Long = System.currentTimeMillis(),
    val updatedAt: Long = System.currentTimeMillis()
)
