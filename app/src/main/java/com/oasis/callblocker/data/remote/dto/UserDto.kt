package com.oasis.callblocker.data.remote.dto

import com.google.gson.annotations.SerializedName

/**
 * 用户数据传输对象
 */
data class UserDto(
    @SerializedName("userId")
    val userId: String,
    
    @SerializedName("userEmail")
    val userEmail: String,
    
    @SerializedName("userCompany")
    val userCompany: String? = null,
    
    @SerializedName("deviceToken")
    val deviceToken: String? = null,
    
    @SerializedName("authToken")
    val authToken: String? = null,
    
    @SerializedName("licenseEndDate")
    val licenseEndDate: String? = null,
    
    @SerializedName("remainQueryCount")
    val remainQueryCount: Int = 0
)

/**
 * 登录请求数据传输对象
 */
data class LoginRequestDto(
    @SerializedName("userID")
    val userEmail: String,
    
    @SerializedName("userPass")
    val userPassword: String,
    
    @SerializedName("devToken")
    val deviceToken: String
)

/**
 * 登录响应数据传输对象
 */
data class LoginResponseDto(
    @SerializedName("success")
    val success: Bo<PERSON>an,
    
    @SerializedName("message")
    val message: String? = null,
    
    @SerializedName("user")
    val user: UserDto? = null,
    
    @SerializedName("token")
    val token: String? = null
)
