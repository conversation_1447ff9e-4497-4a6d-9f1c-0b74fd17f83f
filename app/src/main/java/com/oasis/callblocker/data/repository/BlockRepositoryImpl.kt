package com.oasis.callblocker.data.repository

import com.oasis.callblocker.data.local.database.dao.BlockNumberDao
import com.oasis.callblocker.data.local.database.dao.BlockHistoryDao
import com.oasis.callblocker.data.local.preferences.OasisPreferences
import com.oasis.callblocker.data.mapper.toBlockNumber
import com.oasis.callblocker.data.mapper.toBlockNumberEntity
import com.oasis.callblocker.data.mapper.toBlockHistory
import com.oasis.callblocker.data.mapper.toBlockHistoryEntity
import com.oasis.callblocker.domain.model.*
import com.oasis.callblocker.domain.repository.BlockRepository
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.map
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 拦截功能仓库实现
 */
@Singleton
class BlockRepositoryImpl @Inject constructor(
    private val blockNumberDao: BlockNumberDao,
    private val blockHistoryDao: BlockHistoryDao,
    private val preferences: OasisPreferences
) : BlockRepository {
    
    override fun getAllBlockNumbers(): Flow<List<BlockNumber>> {
        return blockNumberDao.getAllBlockNumbers().map { entities ->
            entities.map { it.toBlockNumber() }
        }
    }
    
    override fun getBlockNumbersByType(blockType: Int): Flow<List<BlockNumber>> {
        return blockNumberDao.getBlockNumbersByType(blockType).map { entities ->
            entities.map { it.toBlockNumber() }
        }
    }
    
    override suspend fun getBlockNumber(phoneNumber: String): BlockNumber? {
        return blockNumberDao.getBlockNumber(phoneNumber)?.toBlockNumber()
    }
    
    override suspend fun addBlockNumber(blockNumber: BlockNumber) {
        blockNumberDao.insertBlockNumber(blockNumber.toBlockNumberEntity())
    }
    
    override suspend fun removeBlockNumber(phoneNumber: String) {
        blockNumberDao.deleteBlockNumber(phoneNumber)
    }
    
    override suspend fun updateBlockNumber(blockNumber: BlockNumber) {
        blockNumberDao.updateBlockNumber(blockNumber.toBlockNumberEntity())
    }
    
    override fun getAllBlockHistory(): Flow<List<BlockHistory>> {
        return blockHistoryDao.getAllBlockHistory().map { entities ->
            entities.map { it.toBlockHistory() }
        }
    }
    
    override fun getBlockHistoryByType(blockType: Int): Flow<List<BlockHistory>> {
        return blockHistoryDao.getBlockHistoryByType(blockType).map { entities ->
            entities.map { it.toBlockHistory() }
        }
    }
    
    override fun getBlockHistoryByNumber(phoneNumber: String): Flow<List<BlockHistory>> {
        return blockHistoryDao.getBlockHistoryByNumber(phoneNumber).map { entities ->
            entities.map { it.toBlockHistory() }
        }
    }
    
    override suspend fun addBlockHistory(blockHistory: BlockHistory) {
        blockHistoryDao.insertBlockHistory(blockHistory.toBlockHistoryEntity())
    }
    
    override suspend fun getBlockCountSince(phoneNumber: String, startTime: Long): Int {
        return blockHistoryDao.getBlockCountSince(phoneNumber, startTime)
    }
    
    override suspend fun clearOldBlockHistory(beforeTime: Long) {
        blockHistoryDao.deleteOldBlockHistory(beforeTime)
    }
    
    override suspend fun getBlockSettings(): BlockSettings {
        return BlockSettings(
            isBlockUnknown = preferences.isBlockUnknown,
            isBlockSpecificNumbers = preferences.isBlockSpecificNumbers,
            isBlockPrefix = preferences.isBlockPrefix,
            isBlockTodayCall = preferences.isBlockTodayCall,
            isBlockAll = preferences.isBlockAll,
            isBlockCallExplosion = preferences.isBlockCallExplosion,
            limitTodayCall = preferences.limitTodayCall,
            callExplosionCount = preferences.callExplosionCount
        )
    }
    
    override suspend fun updateBlockSettings(settings: BlockSettings) {
        preferences.isBlockUnknown = settings.isBlockUnknown
        preferences.isBlockSpecificNumbers = settings.isBlockSpecificNumbers
        preferences.isBlockPrefix = settings.isBlockPrefix
        preferences.isBlockTodayCall = settings.isBlockTodayCall
        preferences.isBlockAll = settings.isBlockAll
        preferences.isBlockCallExplosion = settings.isBlockCallExplosion
        preferences.limitTodayCall = settings.limitTodayCall
        preferences.callExplosionCount = settings.callExplosionCount
    }
    
    override suspend fun shouldBlockCall(phoneNumber: String): Boolean {
        val settings = getBlockSettings()
        
        // 如果拦截所有来电
        if (settings.isBlockAll) return true
        
        // 检查特定号码拦截
        if (settings.isBlockSpecificNumbers) {
            val blockNumber = getBlockNumber(phoneNumber)
            if (blockNumber != null && blockNumber.blockType == BlockType.SPECIFIC) {
                return true
            }
        }
        
        // 检查前缀拦截
        if (settings.isBlockPrefix) {
            val prefixNumbers = blockNumberDao.getBlockNumbersByPrefix(BlockType.PREFIX.value, phoneNumber.take(3))
            if (prefixNumbers.isNotEmpty()) return true
        }
        
        // 检查今日通话限制
        if (settings.isBlockTodayCall) {
            val todayStart = getTodayStartTime()
            val todayCount = getBlockCountSince(phoneNumber, todayStart)
            if (todayCount >= settings.limitTodayCall) return true
        }
        
        // 检查呼叫爆炸
        if (settings.isBlockCallExplosion) {
            val recentTime = System.currentTimeMillis() - settings.explosionTimeWindow
            val recentCount = getBlockCountSince(phoneNumber, recentTime)
            if (recentCount >= settings.callExplosionCount) return true
        }
        
        return false
    }
    
    override suspend fun processIncomingCall(phoneNumber: String, contactName: String?): Boolean {
        val shouldBlock = shouldBlockCall(phoneNumber)
        
        if (shouldBlock) {
            // 记录拦截历史
            val blockType = determineBlockType(phoneNumber)
            val blockHistory = BlockHistory(
                phoneNumber = phoneNumber,
                blockType = blockType,
                contactName = contactName
            )
            addBlockHistory(blockHistory)
            
            // 更新今日拦截次数
            val blockNumber = getBlockNumber(phoneNumber)
            if (blockNumber != null) {
                val updatedBlockNumber = blockNumber.copy(
                    todayCount = blockNumber.todayCount + 1,
                    updatedAt = System.currentTimeMillis()
                )
                updateBlockNumber(updatedBlockNumber)
            }
        }
        
        return shouldBlock
    }
    
    override suspend fun resetDailyCounters() {
        blockNumberDao.resetTodayCount()
    }
    
    private suspend fun determineBlockType(phoneNumber: String): BlockHistoryType {
        val settings = getBlockSettings()
        
        return when {
            settings.isBlockAll -> BlockHistoryType.ALL
            getBlockNumber(phoneNumber)?.blockType == BlockType.SPECIFIC -> BlockHistoryType.SPECIFIC
            settings.isBlockPrefix -> BlockHistoryType.PREFIX
            settings.isBlockTodayCall -> BlockHistoryType.TODAY_LIMIT
            settings.isBlockCallExplosion -> BlockHistoryType.EXPLOSION
            else -> BlockHistoryType.UNKNOWN
        }
    }
    
    private fun getTodayStartTime(): Long {
        val calendar = java.util.Calendar.getInstance()
        calendar.set(java.util.Calendar.HOUR_OF_DAY, 0)
        calendar.set(java.util.Calendar.MINUTE, 0)
        calendar.set(java.util.Calendar.SECOND, 0)
        calendar.set(java.util.Calendar.MILLISECOND, 0)
        return calendar.timeInMillis
    }
}
