package com.oasis.callblocker.data.local.database.dao

import androidx.room.*
import com.oasis.callblocker.data.local.database.entities.BlockHistoryEntity
import kotlinx.coroutines.flow.Flow

/**
 * 拦截历史数据访问对象
 */
@Dao
interface BlockHistoryDao {
    
    @Query("SELECT * FROM block_history ORDER BY timestamp DESC")
    fun getAllBlockHistory(): Flow<List<BlockHistoryEntity>>
    
    @Query("SELECT * FROM block_history WHERE blockType = :blockType ORDER BY timestamp DESC")
    fun getBlockHistoryByType(blockType: Int): Flow<List<BlockHistoryEntity>>
    
    @Query("SELECT * FROM block_history WHERE phoneNumber = :phoneNumber ORDER BY timestamp DESC")
    fun getBlockHistoryByNumber(phoneNumber: String): Flow<List<BlockHistoryEntity>>
    
    @Query("SELECT * FROM block_history WHERE timestamp >= :startTime AND timestamp <= :endTime ORDER BY timestamp DESC")
    fun getBlockHistoryByTimeRange(startTime: Long, endTime: Long): Flow<List<BlockHistoryEntity>>
    
    @Query("SELECT COUNT(*) FROM block_history WHERE phoneNumber = :phoneNumber AND timestamp >= :startTime")
    suspend fun getBlockCountSince(phoneNumber: String, startTime: Long): Int
    
    @Query("SELECT COUNT(*) FROM block_history WHERE timestamp >= :startTime")
    suspend fun getTotalBlockCountSince(startTime: Long): Int
    
    @Insert
    suspend fun insertBlockHistory(blockHistory: BlockHistoryEntity)
    
    @Insert
    suspend fun insertBlockHistories(blockHistories: List<BlockHistoryEntity>)
    
    @Delete
    suspend fun deleteBlockHistory(blockHistory: BlockHistoryEntity)
    
    @Query("DELETE FROM block_history WHERE id = :id")
    suspend fun deleteBlockHistory(id: Long)
    
    @Query("DELETE FROM block_history WHERE timestamp < :beforeTime")
    suspend fun deleteOldBlockHistory(beforeTime: Long)
    
    @Query("DELETE FROM block_history")
    suspend fun deleteAllBlockHistory()
}
