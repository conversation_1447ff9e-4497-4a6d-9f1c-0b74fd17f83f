package com.oasis.callblocker.data.local.database.dao

import androidx.room.*
import com.oasis.callblocker.data.local.database.entities.NoticeEntity
import kotlinx.coroutines.flow.Flow

/**
 * 通知数据访问对象
 */
@Dao
interface NoticeDao {
    
    @Query("SELECT * FROM notices ORDER BY publishTime DESC")
    fun getAllNotices(): Flow<List<NoticeEntity>>
    
    @Query("SELECT * FROM notices WHERE isRead = 0 ORDER BY publishTime DESC")
    fun getUnreadNotices(): Flow<List<NoticeEntity>>
    
    @Query("SELECT * FROM notices WHERE noticeType = :noticeType ORDER BY publishTime DESC")
    fun getNoticesByType(noticeType: Int): Flow<List<NoticeEntity>>
    
    @Query("SELECT * FROM notices WHERE noticeId = :noticeId")
    suspend fun getNotice(noticeId: String): NoticeEntity?
    
    @Query("SELECT COUNT(*) FROM notices WHERE isRead = 0")
    fun getUnreadNoticeCount(): Flow<Int>
    
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertNotice(notice: NoticeEntity)
    
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertNotices(notices: List<NoticeEntity>)
    
    @Update
    suspend fun updateNotice(notice: NoticeEntity)
    
    @Query("UPDATE notices SET isRead = 1 WHERE noticeId = :noticeId")
    suspend fun markAsRead(noticeId: String)
    
    @Query("UPDATE notices SET isRead = 1")
    suspend fun markAllAsRead()
    
    @Delete
    suspend fun deleteNotice(notice: NoticeEntity)
    
    @Query("DELETE FROM notices WHERE noticeId = :noticeId")
    suspend fun deleteNotice(noticeId: String)
    
    @Query("DELETE FROM notices WHERE publishTime < :beforeTime")
    suspend fun deleteOldNotices(beforeTime: Long)
    
    @Query("DELETE FROM notices")
    suspend fun deleteAllNotices()
}
