package com.oasis.callblocker.data.remote.api

import com.oasis.callblocker.data.remote.dto.*
import retrofit2.Response
import retrofit2.http.*

/**
 * OASIS API服务接口
 */
interface OasisApiService {
    
    /**
     * 用户登录认证
     */
    @POST("Authenticate")
    suspend fun authenticate(
        @Body loginRequest: LoginRequestDto
    ): Response<LoginResponseDto>
    
    /**
     * 查询电话号码信息
     */
    @POST("QueryPhoneNumber")
    suspend fun queryPhoneNumber(
        @Body searchRequest: PhoneSearchRequestDto
    ): Response<PhoneSearchResponseDto>
    
    /**
     * 获取通知列表
     */
    @GET("GetAnnouncementList")
    suspend fun getAnnouncementList(
        @Query("userId") userId: String
    ): Response<NoticeListResponseDto>
    
    /**
     * 检查新通知
     */
    @GET("CheckNewNotice")
    suspend fun checkNewNotice(
        @Query("userId") userId: String,
        @Query("lastCheckTime") lastCheckTime: Long
    ): Response<CheckNoticeResponseDto>
    
    /**
     * 获取查询限制次数
     */
    @GET("GetQueryLimitCount")
    suspend fun getQueryLimitCount(
        @Query("userId") userId: String
    ): Response<QueryLimitResponseDto>
    
    /**
     * 上报通话结果
     */
    @POST("ReportPhoneCallResult")
    suspend fun reportPhoneCallResult(
        @Body callResult: CallResultDto
    ): Response<BaseResponseDto>
    
    /**
     * 上报拦截配置
     */
    @POST("ReportBlockAllFlag")
    suspend fun reportBlockAllFlag(
        @Body blockConfig: BlockConfigDto
    ): Response<BaseResponseDto>
    
    /**
     * 更新联系人信息
     */
    @POST("UpdatePhoneNumber")
    suspend fun updatePhoneNumber(
        @Body contactUpdate: ContactUpdateDto
    ): Response<BaseResponseDto>
    
    /**
     * 检查应用更新
     */
    @GET("CheckApkUpdate")
    suspend fun checkApkUpdate(
        @Query("currentVersion") currentVersion: String
    ): Response<UpdateCheckResponseDto>
}

/**
 * 基础响应数据传输对象
 */
data class BaseResponseDto(
    @SerializedName("success")
    val success: Boolean,
    
    @SerializedName("message")
    val message: String? = null
)

/**
 * 查询限制响应数据传输对象
 */
data class QueryLimitResponseDto(
    @SerializedName("success")
    val success: Boolean,
    
    @SerializedName("remainCount")
    val remainCount: Int = 0
)

/**
 * 通话结果数据传输对象
 */
data class CallResultDto(
    @SerializedName("userId")
    val userId: String,
    
    @SerializedName("phoneNumber")
    val phoneNumber: String,
    
    @SerializedName("callType")
    val callType: Int,
    
    @SerializedName("isBlocked")
    val isBlocked: Boolean,
    
    @SerializedName("timestamp")
    val timestamp: Long
)

/**
 * 拦截配置数据传输对象
 */
data class BlockConfigDto(
    @SerializedName("userId")
    val userId: String,
    
    @SerializedName("isBlockAll")
    val isBlockAll: Boolean,
    
    @SerializedName("blockSettings")
    val blockSettings: Map<String, Any>
)

/**
 * 联系人更新数据传输对象
 */
data class ContactUpdateDto(
    @SerializedName("userId")
    val userId: String,
    
    @SerializedName("contacts")
    val contacts: List<ContactDto>
)

/**
 * 联系人数据传输对象
 */
data class ContactDto(
    @SerializedName("name")
    val name: String,
    
    @SerializedName("phoneNumber")
    val phoneNumber: String
)

/**
 * 更新检查响应数据传输对象
 */
data class UpdateCheckResponseDto(
    @SerializedName("success")
    val success: Boolean,
    
    @SerializedName("hasUpdate")
    val hasUpdate: Boolean = false,
    
    @SerializedName("latestVersion")
    val latestVersion: String? = null,
    
    @SerializedName("downloadUrl")
    val downloadUrl: String? = null,
    
    @SerializedName("updateMessage")
    val updateMessage: String? = null
)
