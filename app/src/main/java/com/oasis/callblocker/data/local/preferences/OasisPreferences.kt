package com.oasis.callblocker.data.local.preferences

import android.content.Context
import android.content.SharedPreferences
import androidx.security.crypto.EncryptedSharedPreferences
import androidx.security.crypto.MasterKey
import dagger.hilt.android.qualifiers.ApplicationContext
import javax.inject.Inject
import javax.inject.Singleton

/**
 * OASIS应用偏好设置管理器
 * 使用加密的SharedPreferences保护敏感数据
 */
@Singleton
class OasisPreferences @Inject constructor(
    @ApplicationContext private val context: Context
) {
    
    private val masterKey = MasterKey.Builder(context)
        .setKeyScheme(MasterKey.KeyScheme.AES256_GCM)
        .build()
    
    private val encryptedPrefs: SharedPreferences = EncryptedSharedPreferences.create(
        context,
        "oasis_encrypted_prefs",
        masterKey,
        EncryptedSharedPreferences.PrefKeyEncryptionScheme.AES256_SIV,
        EncryptedSharedPreferences.PrefValueEncryptionScheme.AES256_GCM
    )
    
    private val normalPrefs: SharedPreferences = context.getSharedPreferences(
        "oasis_prefs", 
        Context.MODE_PRIVATE
    )
    
    // 用户认证相关（加密存储）
    var userToken: String?
        get() = encryptedPrefs.getString(KEY_USER_TOKEN, null)
        set(value) = encryptedPrefs.edit().putString(KEY_USER_TOKEN, value).apply()
    
    var userEmail: String?
        get() = encryptedPrefs.getString(KEY_USER_EMAIL, null)
        set(value) = encryptedPrefs.edit().putString(KEY_USER_EMAIL, value).apply()
    
    var userPassword: String?
        get() = encryptedPrefs.getString(KEY_USER_PASSWORD, null)
        set(value) = encryptedPrefs.edit().putString(KEY_USER_PASSWORD, value).apply()
    
    var deviceToken: String?
        get() = encryptedPrefs.getString(KEY_DEVICE_TOKEN, null)
        set(value) = encryptedPrefs.edit().putString(KEY_DEVICE_TOKEN, value).apply()
    
    // 拦截设置（普通存储）
    var isBlockUnknown: Boolean
        get() = normalPrefs.getBoolean(KEY_BLOCK_UNKNOWN, false)
        set(value) = normalPrefs.edit().putBoolean(KEY_BLOCK_UNKNOWN, value).apply()
    
    var isBlockSpecificNumbers: Boolean
        get() = normalPrefs.getBoolean(KEY_BLOCK_SPECIFIC, false)
        set(value) = normalPrefs.edit().putBoolean(KEY_BLOCK_SPECIFIC, value).apply()
    
    var isBlockPrefix: Boolean
        get() = normalPrefs.getBoolean(KEY_BLOCK_PREFIX, false)
        set(value) = normalPrefs.edit().putBoolean(KEY_BLOCK_PREFIX, value).apply()
    
    var isBlockTodayCall: Boolean
        get() = normalPrefs.getBoolean(KEY_BLOCK_TODAY_CALL, false)
        set(value) = normalPrefs.edit().putBoolean(KEY_BLOCK_TODAY_CALL, value).apply()
    
    var isBlockAll: Boolean
        get() = normalPrefs.getBoolean(KEY_BLOCK_ALL, false)
        set(value) = normalPrefs.edit().putBoolean(KEY_BLOCK_ALL, value).apply()
    
    var isBlockCallExplosion: Boolean
        get() = normalPrefs.getBoolean(KEY_BLOCK_CALL_EXPLOSION, false)
        set(value) = normalPrefs.edit().putBoolean(KEY_BLOCK_CALL_EXPLOSION, value).apply()
    
    var limitTodayCall: Int
        get() = normalPrefs.getInt(KEY_LIMIT_TODAY_CALL, 3)
        set(value) = normalPrefs.edit().putInt(KEY_LIMIT_TODAY_CALL, value).apply()
    
    var callExplosionCount: Int
        get() = normalPrefs.getInt(KEY_CALL_EXPLOSION_COUNT, 5)
        set(value) = normalPrefs.edit().putInt(KEY_CALL_EXPLOSION_COUNT, value).apply()
    
    // UI设置
    var popupPosition: Int
        get() = normalPrefs.getInt(KEY_POPUP_POSITION, 0)
        set(value) = normalPrefs.edit().putInt(KEY_POPUP_POSITION, value).apply()
    
    var showPopupRemain: Boolean
        get() = normalPrefs.getBoolean(KEY_POPUP_REMAIN, true)
        set(value) = normalPrefs.edit().putBoolean(KEY_POPUP_REMAIN, value).apply()
    
    var showTodayCall: Boolean
        get() = normalPrefs.getBoolean(KEY_SHOW_TODAY_CALL, true)
        set(value) = normalPrefs.edit().putBoolean(KEY_SHOW_TODAY_CALL, value).apply()
    
    // 其他设置
    var lastContactUpdateTime: Long
        get() = normalPrefs.getLong(KEY_LAST_CONTACT_UPDATE, 0)
        set(value) = normalPrefs.edit().putLong(KEY_LAST_CONTACT_UPDATE, value).apply()
    
    var lastNoticeCheckTime: Long
        get() = normalPrefs.getLong(KEY_LAST_NOTICE_CHECK, 0)
        set(value) = normalPrefs.edit().putLong(KEY_LAST_NOTICE_CHECK, value).apply()
    
    var isNewNotice: Boolean
        get() = normalPrefs.getBoolean(KEY_IS_NEW_NOTICE, false)
        set(value) = normalPrefs.edit().putBoolean(KEY_IS_NEW_NOTICE, value).apply()
    
    fun clearUserData() {
        encryptedPrefs.edit().clear().apply()
    }
    
    fun clearAllData() {
        encryptedPrefs.edit().clear().apply()
        normalPrefs.edit().clear().apply()
    }
    
    companion object {
        // 加密存储的键
        private const val KEY_USER_TOKEN = "user_token"
        private const val KEY_USER_EMAIL = "user_email"
        private const val KEY_USER_PASSWORD = "user_password"
        private const val KEY_DEVICE_TOKEN = "device_token"
        
        // 普通存储的键
        private const val KEY_BLOCK_UNKNOWN = "block_unknown"
        private const val KEY_BLOCK_SPECIFIC = "block_specific"
        private const val KEY_BLOCK_PREFIX = "block_prefix"
        private const val KEY_BLOCK_TODAY_CALL = "block_today_call"
        private const val KEY_BLOCK_ALL = "block_all"
        private const val KEY_BLOCK_CALL_EXPLOSION = "block_call_explosion"
        private const val KEY_LIMIT_TODAY_CALL = "limit_today_call"
        private const val KEY_CALL_EXPLOSION_COUNT = "call_explosion_count"
        private const val KEY_POPUP_POSITION = "popup_position"
        private const val KEY_POPUP_REMAIN = "popup_remain"
        private const val KEY_SHOW_TODAY_CALL = "show_today_call"
        private const val KEY_LAST_CONTACT_UPDATE = "last_contact_update"
        private const val KEY_LAST_NOTICE_CHECK = "last_notice_check"
        private const val KEY_IS_NEW_NOTICE = "is_new_notice"
    }
}
