package com.oasis.callblocker.data.local.database

import androidx.room.Database
import androidx.room.Room
import androidx.room.RoomDatabase
import androidx.room.migration.Migration
import androidx.sqlite.db.SupportSQLiteDatabase
import android.content.Context
import com.oasis.callblocker.data.local.database.dao.*
import com.oasis.callblocker.data.local.database.entities.*

/**
 * OASIS应用主数据库
 */
@Database(
    entities = [
        BlockNumberEntity::class,
        BlockHistoryEntity::class,
        UserEntity::class,
        CallLogEntity::class,
        NoticeEntity::class,
        SearchResultEntity::class
    ],
    version = 1,
    exportSchema = true
)
abstract class OasisDatabase : RoomDatabase() {
    
    abstract fun blockNumberDao(): BlockNumberDao
    abstract fun blockHistoryDao(): BlockHistoryDao
    abstract fun userDao(): UserDao
    abstract fun callLogDao(): CallLogDao
    abstract fun noticeDao(): NoticeDao
    abstract fun searchResultDao(): SearchResultDao
    
    companion object {
        const val DATABASE_NAME = "oasis_database"
        
        @Volatile
        private var INSTANCE: OasisDatabase? = null
        
        fun getDatabase(context: Context): OasisDatabase {
            return INSTANCE ?: synchronized(this) {
                val instance = Room.databaseBuilder(
                    context.applicationContext,
                    OasisDatabase::class.java,
                    DATABASE_NAME
                )
                    .addCallback(DatabaseCallback())
                    .build()
                INSTANCE = instance
                instance
            }
        }
        
        private class DatabaseCallback : RoomDatabase.Callback() {
            override fun onCreate(db: SupportSQLiteDatabase) {
                super.onCreate(db)
                // 数据库创建时的初始化操作
            }
            
            override fun onOpen(db: SupportSQLiteDatabase) {
                super.onOpen(db)
                // 数据库打开时的操作
            }
        }
    }
}
