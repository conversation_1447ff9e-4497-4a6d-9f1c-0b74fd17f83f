package com.oasis.callblocker.data.mapper

import com.oasis.callblocker.data.local.database.entities.CallLogEntity
import com.oasis.callblocker.domain.model.CallLog
import com.oasis.callblocker.domain.model.CallType

/**
 * 通话记录实体转换为领域模型
 */
fun CallLogEntity.toCallLog(): CallLog {
    return CallLog(
        id = id,
        phoneNumber = phoneNumber,
        contactName = contactName,
        callType = CallType.fromValue(callType),
        duration = duration,
        timestamp = timestamp,
        isBlocked = isBlocked
    )
}

/**
 * 通话记录领域模型转换为实体
 */
fun CallLog.toCallLogEntity(): CallLogEntity {
    return CallLogEntity(
        id = id,
        phoneNumber = phoneNumber,
        contactName = contactName,
        callType = callType.value,
        duration = duration,
        timestamp = timestamp,
        isBlocked = isBlocked
    )
}
