package com.oasis.callblocker.data.mapper

import com.oasis.callblocker.data.local.database.entities.BlockNumberEntity
import com.oasis.callblocker.data.local.database.entities.BlockHistoryEntity
import com.oasis.callblocker.domain.model.BlockNumber
import com.oasis.callblocker.domain.model.BlockHistory
import com.oasis.callblocker.domain.model.BlockType
import com.oasis.callblocker.domain.model.BlockHistoryType

/**
 * 拦截号码实体转换为领域模型
 */
fun BlockNumberEntity.toBlockNumber(): BlockNumber {
    return BlockNumber(
        phoneNumber = phoneNumber,
        blockType = BlockType.fromValue(blockType),
        todayCount = todayCount,
        createdAt = createdAt,
        updatedAt = updatedAt
    )
}

/**
 * 拦截号码领域模型转换为实体
 */
fun BlockNumber.toBlockNumberEntity(): BlockNumberEntity {
    return BlockNumberEntity(
        phoneNumber = phoneNumber,
        blockType = blockType.value,
        todayCount = todayCount,
        createdAt = createdAt,
        updatedAt = updatedAt
    )
}

/**
 * 拦截历史实体转换为领域模型
 */
fun BlockHistoryEntity.toBlockHistory(): BlockHistory {
    return BlockHistory(
        id = id,
        phoneNumber = phoneNumber,
        blockType = BlockHistoryType.fromValue(blockType),
        timestamp = timestamp,
        contactName = contactName
    )
}

/**
 * 拦截历史领域模型转换为实体
 */
fun BlockHistory.toBlockHistoryEntity(): BlockHistoryEntity {
    return BlockHistoryEntity(
        id = id,
        phoneNumber = phoneNumber,
        blockType = blockType.value,
        timestamp = timestamp,
        contactName = contactName
    )
}
