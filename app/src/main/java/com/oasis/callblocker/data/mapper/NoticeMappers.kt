package com.oasis.callblocker.data.mapper

import com.oasis.callblocker.data.local.database.entities.NoticeEntity
import com.oasis.callblocker.data.remote.dto.NoticeDto
import com.oasis.callblocker.domain.model.Notice
import com.oasis.callblocker.domain.model.NoticeType

/**
 * 通知实体转换为领域模型
 */
fun NoticeEntity.toNotice(): Notice {
    return Notice(
        noticeId = noticeId,
        title = title,
        content = content,
        noticeType = NoticeType.fromValue(noticeType),
        isRead = isRead,
        publishTime = publishTime,
        createdAt = createdAt
    )
}

/**
 * 通知领域模型转换为实体
 */
fun Notice.toNoticeEntity(): NoticeEntity {
    return NoticeEntity(
        noticeId = noticeId,
        title = title,
        content = content,
        noticeType = noticeType.value,
        isRead = isRead,
        publishTime = publishTime,
        createdAt = createdAt
    )
}

/**
 * 通知DTO转换为领域模型
 */
fun NoticeDto.toNotice(): Notice {
    return Notice(
        noticeId = noticeId,
        title = title,
        content = content,
        noticeType = if (isImportant) NoticeType.IMPORTANT else NoticeType.fromValue(noticeType),
        publishTime = publishTime
    )
}
