package com.oasis.callblocker.data.local.database.entities

import androidx.room.Entity
import androidx.room.PrimaryKey

/**
 * 拦截历史数据库实体
 * 对应原来的BlockNumberHistory.java
 */
@Entity(tableName = "block_history")
data class BlockHistoryEntity(
    @PrimaryKey(autoGenerate = true)
    val id: Long = 0,
    val phoneNumber: String,
    val blockType: Int, // 拦截类型
    val timestamp: Long = System.currentTimeMillis(),
    val contactName: String? = null // 联系人姓名（如果有）
) {
    companion object {
        const val BLOCK_TYPE_UNKNOWN = 0      // 未知号码
        const val BLOCK_TYPE_TODAY_LIMIT = 1  // 今日通话限制
        const val BLOCK_TYPE_SPECIFIC = 2     // 特定号码
        const val BLOCK_TYPE_PREFIX = 3       // 前缀
        const val BLOCK_TYPE_ALL = 4          // 全部拦截
        const val BLOCK_TYPE_EXPLOSION = 5    // 呼叫爆炸
    }
}
