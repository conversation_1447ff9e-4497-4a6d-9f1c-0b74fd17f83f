package com.oasis.callblocker.data.mapper

import com.oasis.callblocker.data.local.database.entities.SearchResultEntity
import com.oasis.callblocker.data.remote.dto.PhoneInfoDto
import com.oasis.callblocker.domain.model.PhoneSearchResult
import com.oasis.callblocker.domain.model.RiskLevel

/**
 * 搜索结果实体转换为领域模型
 */
fun SearchResultEntity.toPhoneSearchResult(): PhoneSearchResult {
    return PhoneSearchResult(
        phoneNumber = phoneNumber,
        ownerName = ownerName,
        location = location,
        carrier = carrier,
        numberType = numberType,
        riskLevel = RiskLevel.fromValue(riskLevel),
        reportCount = reportCount,
        lastSearchTime = lastSearchTime,
        cacheExpireTime = cacheExpireTime
    )
}

/**
 * 搜索结果领域模型转换为实体
 */
fun PhoneSearchResult.toSearchResultEntity(): SearchResultEntity {
    return SearchResultEntity(
        phoneNumber = phoneNumber,
        ownerName = ownerName,
        location = location,
        carrier = carrier,
        numberType = numberType,
        riskLevel = riskLevel.value,
        reportCount = reportCount,
        lastSearchTime = lastSearchTime,
        cacheExpireTime = cacheExpireTime
    )
}

/**
 * 电话信息DTO转换为搜索结果领域模型
 */
fun PhoneInfoDto.toPhoneSearchResult(): PhoneSearchResult {
    return PhoneSearchResult(
        phoneNumber = phoneNumber,
        ownerName = ownerName,
        location = location,
        carrier = carrier,
        numberType = numberType,
        riskLevel = RiskLevel.fromValue(riskLevel),
        reportCount = reportCount,
        tags = tags ?: emptyList()
    )
}
