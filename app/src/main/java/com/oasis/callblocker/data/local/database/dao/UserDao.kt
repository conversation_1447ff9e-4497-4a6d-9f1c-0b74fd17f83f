package com.oasis.callblocker.data.local.database.dao

import androidx.room.*
import com.oasis.callblocker.data.local.database.entities.UserEntity
import kotlinx.coroutines.flow.Flow

/**
 * 用户数据访问对象
 */
@Dao
interface UserDao {
    
    @Query("SELECT * FROM users WHERE userId = :userId")
    suspend fun getUser(userId: String): UserEntity?
    
    @Query("SELECT * FROM users WHERE userId = :userId")
    fun getUserFlow(userId: String): Flow<UserEntity?>
    
    @Query("SELECT * FROM users LIMIT 1")
    suspend fun getCurrentUser(): UserEntity?
    
    @Query("SELECT * FROM users LIMIT 1")
    fun getCurrentUserFlow(): Flow<UserEntity?>
    
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertUser(user: UserEntity)
    
    @Update
    suspend fun updateUser(user: UserEntity)
    
    @Query("UPDATE users SET authToken = :token, updatedAt = :timestamp WHERE userId = :userId")
    suspend fun updateAuthToken(userId: String, token: String?, timestamp: Long = System.currentTimeMillis())
    
    @Query("UPDATE users SET remainQueryCount = :count, updatedAt = :timestamp WHERE userId = :userId")
    suspend fun updateQueryCount(userId: String, count: Int, timestamp: Long = System.currentTimeMillis())
    
    @Delete
    suspend fun deleteUser(user: UserEntity)
    
    @Query("DELETE FROM users WHERE userId = :userId")
    suspend fun deleteUser(userId: String)
    
    @Query("DELETE FROM users")
    suspend fun deleteAllUsers()
}
