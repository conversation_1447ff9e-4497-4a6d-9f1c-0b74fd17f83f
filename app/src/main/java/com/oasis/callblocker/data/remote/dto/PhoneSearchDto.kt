package com.oasis.callblocker.data.remote.dto

import com.google.gson.annotations.SerializedName

/**
 * 电话号码搜索请求数据传输对象
 */
data class PhoneSearchRequestDto(
    @SerializedName("phoneNumber")
    val phoneNumber: String,
    
    @SerializedName("userId")
    val userId: String
)

/**
 * 电话号码搜索响应数据传输对象
 */
data class PhoneSearchResponseDto(
    @SerializedName("success")
    val success: Boolean,
    
    @SerializedName("message")
    val message: String? = null,
    
    @SerializedName("data")
    val data: PhoneInfoDto? = null
)

/**
 * 电话号码信息数据传输对象
 */
data class PhoneInfoDto(
    @SerializedName("phoneNumber")
    val phoneNumber: String,
    
    @SerializedName("ownerName")
    val ownerName: String? = null,
    
    @SerializedName("location")
    val location: String? = null,
    
    @SerializedName("carrier")
    val carrier: String? = null,
    
    @SerializedName("numberType")
    val numberType: String? = null,
    
    @SerializedName("riskLevel")
    val riskLevel: Int = 0,
    
    @SerializedName("reportCount")
    val reportCount: Int = 0,
    
    @SerializedName("tags")
    val tags: List<String>? = null
)
