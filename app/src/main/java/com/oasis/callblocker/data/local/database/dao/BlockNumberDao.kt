package com.oasis.callblocker.data.local.database.dao

import androidx.room.*
import com.oasis.callblocker.data.local.database.entities.BlockNumberEntity
import kotlinx.coroutines.flow.Flow

/**
 * 拦截号码数据访问对象
 */
@Dao
interface BlockNumberDao {
    
    @Query("SELECT * FROM block_numbers ORDER BY createdAt DESC")
    fun getAllBlockNumbers(): Flow<List<BlockNumberEntity>>
    
    @Query("SELECT * FROM block_numbers WHERE blockType = :blockType ORDER BY createdAt DESC")
    fun getBlockNumbersByType(blockType: Int): Flow<List<BlockNumberEntity>>
    
    @Query("SELECT * FROM block_numbers WHERE phoneNumber = :phoneNumber")
    suspend fun getBlockNumber(phoneNumber: String): BlockNumberEntity?
    
    @Query("SELECT * FROM block_numbers WHERE blockType = :blockType AND phoneNumber LIKE :prefix || '%'")
    suspend fun getBlockNumbersByPrefix(blockType: Int, prefix: String): List<BlockNumberEntity>
    
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertBlockNumber(blockNumber: BlockNumberEntity)
    
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertBlockNumbers(blockNumbers: List<BlockNumberEntity>)
    
    @Update
    suspend fun updateBlockNumber(blockNumber: BlockNumberEntity)
    
    @Query("UPDATE block_numbers SET todayCount = :count, updatedAt = :timestamp WHERE phoneNumber = :phoneNumber")
    suspend fun updateTodayCount(phoneNumber: String, count: Int, timestamp: Long = System.currentTimeMillis())
    
    @Delete
    suspend fun deleteBlockNumber(blockNumber: BlockNumberEntity)
    
    @Query("DELETE FROM block_numbers WHERE phoneNumber = :phoneNumber")
    suspend fun deleteBlockNumber(phoneNumber: String)
    
    @Query("DELETE FROM block_numbers WHERE blockType = :blockType")
    suspend fun deleteBlockNumbersByType(blockType: Int)
    
    @Query("DELETE FROM block_numbers")
    suspend fun deleteAllBlockNumbers()
    
    @Query("UPDATE block_numbers SET todayCount = 0")
    suspend fun resetTodayCount()
}
