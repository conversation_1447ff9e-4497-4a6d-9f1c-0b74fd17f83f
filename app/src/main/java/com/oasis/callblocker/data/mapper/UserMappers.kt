package com.oasis.callblocker.data.mapper

import com.oasis.callblocker.data.local.database.entities.UserEntity
import com.oasis.callblocker.data.remote.dto.UserDto
import com.oasis.callblocker.domain.model.User

/**
 * 用户实体转换为领域模型
 */
fun UserEntity.toUser(): User {
    return User(
        userId = userId,
        userEmail = userEmail,
        userCompany = userCompany,
        deviceToken = deviceToken,
        authToken = authToken,
        licenseEndDate = licenseEndDate,
        remainQueryCount = remainQueryCount,
        createdAt = createdAt,
        updatedAt = updatedAt
    )
}

/**
 * 用户领域模型转换为实体
 */
fun User.toUserEntity(): UserEntity {
    return UserEntity(
        userId = userId,
        userEmail = userEmail,
        userCompany = userCompany,
        deviceToken = deviceToken,
        authToken = authToken,
        licenseEndDate = licenseEndDate,
        remainQueryCount = remainQueryCount,
        createdAt = createdAt,
        updatedAt = updatedAt
    )
}

/**
 * 用户DTO转换为领域模型
 */
fun UserDto.toUser(): User {
    return User(
        userId = userId,
        userEmail = userEmail,
        userCompany = userCompany,
        deviceToken = deviceToken,
        authToken = authToken,
        licenseEndDate = licenseEndDate,
        remainQueryCount = remainQueryCount
    )
}

/**
 * 用户领域模型转换为DTO
 */
fun User.toUserDto(): UserDto {
    return UserDto(
        userId = userId,
        userEmail = userEmail,
        userCompany = userCompany,
        deviceToken = deviceToken,
        authToken = authToken,
        licenseEndDate = licenseEndDate,
        remainQueryCount = remainQueryCount
    )
}
