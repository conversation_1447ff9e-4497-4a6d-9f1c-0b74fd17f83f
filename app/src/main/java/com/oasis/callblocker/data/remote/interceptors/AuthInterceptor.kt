package com.oasis.callblocker.data.remote.interceptors

import com.oasis.callblocker.data.local.preferences.OasisPreferences
import okhttp3.Interceptor
import okhttp3.Response
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 认证拦截器
 * 自动为API请求添加认证头
 */
@Singleton
class AuthInterceptor @Inject constructor(
    private val preferences: OasisPreferences
) : Interceptor {
    
    override fun intercept(chain: Interceptor.Chain): Response {
        val originalRequest = chain.request()
        
        // 获取用户令牌
        val token = preferences.userToken
        
        // 如果没有令牌或者是登录请求，直接发送原始请求
        if (token.isNullOrEmpty() || originalRequest.url.encodedPath.contains("authenticate")) {
            return chain.proceed(originalRequest)
        }
        
        // 添加认证头
        val authenticatedRequest = originalRequest.newBuilder()
            .addHeader("Authorization", "Bearer $token")
            .addHeader("Content-Type", "application/json")
            .build()
        
        return chain.proceed(authenticatedRequest)
    }
}
