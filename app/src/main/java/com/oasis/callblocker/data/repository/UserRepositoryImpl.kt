package com.oasis.callblocker.data.repository

import android.content.Context
import android.provider.Settings
import com.oasis.callblocker.data.local.database.dao.UserDao
import com.oasis.callblocker.data.local.preferences.OasisPreferences
import com.oasis.callblocker.data.mapper.toUser
import com.oasis.callblocker.data.mapper.toUserEntity
import com.oasis.callblocker.data.remote.api.OasisApiService
import com.oasis.callblocker.data.remote.dto.LoginRequestDto
import com.oasis.callblocker.domain.model.LoginCredentials
import com.oasis.callblocker.domain.model.User
import com.oasis.callblocker.domain.repository.UserRepository
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.map
import java.util.*
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 用户仓库实现
 */
@Singleton
class UserRepositoryImpl @Inject constructor(
    @ApplicationContext private val context: Context,
    private val userDao: UserDao,
    private val apiService: OasisApiService,
    private val preferences: OasisPreferences
) : UserRepository {
    
    override suspend fun login(credentials: LoginCredentials): Result<User> {
        return try {
            val loginRequest = LoginRequestDto(
                userEmail = credentials.email,
                userPassword = credentials.password,
                deviceToken = credentials.deviceToken
            )
            
            val response = apiService.authenticate(loginRequest)
            
            if (response.isSuccessful && response.body()?.success == true) {
                val loginResponse = response.body()!!
                val user = loginResponse.user?.toUser()
                val token = loginResponse.token
                
                if (user != null && token != null) {
                    // 保存用户信息到数据库
                    userDao.insertUser(user.toUserEntity())
                    
                    // 保存认证信息到偏好设置
                    preferences.userToken = token
                    preferences.userEmail = credentials.email
                    preferences.userPassword = credentials.password
                    preferences.deviceToken = credentials.deviceToken
                    
                    Result.success(user)
                } else {
                    Result.failure(Exception("登录响应数据不完整"))
                }
            } else {
                val message = response.body()?.message ?: "登录失败"
                Result.failure(Exception(message))
            }
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    override suspend fun logout() {
        // 清除用户数据
        userDao.deleteAllUsers()
        preferences.clearUserData()
    }
    
    override suspend fun refreshToken(): Result<String> {
        return try {
            val email = preferences.userEmail
            val password = preferences.userPassword
            val deviceToken = preferences.deviceToken
            
            if (email != null && password != null && deviceToken != null) {
                val credentials = LoginCredentials(email, password, deviceToken)
                val loginResult = login(credentials)
                
                if (loginResult.isSuccess) {
                    val token = preferences.userToken
                    if (token != null) {
                        Result.success(token)
                    } else {
                        Result.failure(Exception("令牌刷新失败"))
                    }
                } else {
                    Result.failure(loginResult.exceptionOrNull() ?: Exception("令牌刷新失败"))
                }
            } else {
                Result.failure(Exception("缺少登录凭据"))
            }
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    override fun getCurrentUser(): Flow<User?> {
        return userDao.getCurrentUserFlow().map { entity ->
            entity?.toUser()
        }
    }
    
    override suspend fun updateUser(user: User) {
        userDao.updateUser(user.toUserEntity())
    }
    
    override suspend fun updateQueryCount(count: Int) {
        val currentUser = userDao.getCurrentUser()
        if (currentUser != null) {
            userDao.updateQueryCount(currentUser.userId, count)
        }
    }
    
    override suspend fun isLoggedIn(): Boolean {
        val token = preferences.userToken
        val user = userDao.getCurrentUser()
        return !token.isNullOrEmpty() && user != null
    }
    
    override suspend fun getAuthToken(): String? {
        return preferences.userToken
    }
    
    override suspend fun saveAuthToken(token: String) {
        preferences.userToken = token
    }
    
    override suspend fun clearAuthData() {
        preferences.clearUserData()
        userDao.deleteAllUsers()
    }
    
    override suspend fun getDeviceToken(): String {
        var deviceToken = preferences.deviceToken
        
        if (deviceToken.isNullOrEmpty()) {
            deviceToken = generateDeviceToken()
            preferences.deviceToken = deviceToken
        }
        
        return deviceToken
    }
    
    override suspend fun registerDevice(): Result<String> {
        return try {
            val deviceToken = getDeviceToken()
            // 这里可以添加设备注册的API调用
            Result.success(deviceToken)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    private fun generateDeviceToken(): String {
        val androidId = Settings.Secure.getString(
            context.contentResolver,
            Settings.Secure.ANDROID_ID
        )
        
        return if (androidId.isNullOrEmpty()) {
            UUID.randomUUID().toString()
        } else {
            androidId
        }
    }
}
