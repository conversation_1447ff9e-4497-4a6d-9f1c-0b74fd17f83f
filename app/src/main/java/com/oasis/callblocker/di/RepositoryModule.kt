package com.oasis.callblocker.di

import com.oasis.callblocker.data.repository.*
import com.oasis.callblocker.domain.repository.*
import dagger.Binds
import dagger.Module
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import javax.inject.Singleton

/**
 * Repository依赖注入模块
 */
@Module
@InstallIn(SingletonComponent::class)
abstract class RepositoryModule {
    
    @Binds
    @Singleton
    abstract fun bindUserRepository(
        userRepositoryImpl: UserRepositoryImpl
    ): UserRepository
    
    @Binds
    @Singleton
    abstract fun bindBlockRepository(
        blockRepositoryImpl: BlockRepositoryImpl
    ): BlockRepository
    
    @Binds
    @Singleton
    abstract fun bindCallLogRepository(
        callLogRepositoryImpl: CallLogRepositoryImpl
    ): CallLogRepository
    
    @Binds
    @Singleton
    abstract fun bindPhoneSearchRepository(
        phoneSearchRepositoryImpl: PhoneSearchRepositoryImpl
    ): PhoneSearchRepository
    
    @Binds
    @Singleton
    abstract fun bindNoticeRepository(
        noticeRepositoryImpl: NoticeRepositoryImpl
    ): NoticeRepository
}
