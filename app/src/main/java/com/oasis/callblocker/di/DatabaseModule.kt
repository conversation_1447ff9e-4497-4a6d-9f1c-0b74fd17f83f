package com.oasis.callblocker.di

import android.content.Context
import androidx.room.Room
import com.oasis.callblocker.data.local.database.OasisDatabase
import com.oasis.callblocker.data.local.database.dao.*
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.android.qualifiers.ApplicationContext
import dagger.hilt.components.SingletonComponent
import javax.inject.Singleton

/**
 * 数据库依赖注入模块
 */
@Module
@InstallIn(SingletonComponent::class)
object DatabaseModule {
    
    @Provides
    @Singleton
    fun provideOasisDatabase(@ApplicationContext context: Context): OasisDatabase {
        return Room.databaseBuilder(
            context.applicationContext,
            OasisDatabase::class.java,
            OasisDatabase.DATABASE_NAME
        ).build()
    }
    
    @Provides
    fun provideBlockNumberDao(database: OasisDatabase): BlockNumberDao {
        return database.blockNumberDao()
    }
    
    @Provides
    fun provideBlockHistoryDao(database: OasisDatabase): BlockHistoryDao {
        return database.blockHistoryDao()
    }
    
    @Provides
    fun provideUserDao(database: OasisDatabase): UserDao {
        return database.userDao()
    }
    
    @Provides
    fun provideCallLogDao(database: OasisDatabase): CallLogDao {
        return database.callLogDao()
    }
    
    @Provides
    fun provideNoticeDao(database: OasisDatabase): NoticeDao {
        return database.noticeDao()
    }
    
    @Provides
    fun provideSearchResultDao(database: OasisDatabase): SearchResultDao {
        return database.searchResultDao()
    }
}
