package com.oasis.callblocker.utils

import android.content.Context
import android.util.Log
import com.oasis.callblocker.data.local.preferences.OasisPreferences
import com.oasis.callblocker.data.remote.api.OasisApiService
import com.oasis.callblocker.data.remote.dto.ContactDto
import com.oasis.callblocker.data.remote.dto.ContactUpdateDto
import com.oasis.callblocker.domain.repository.UserRepository
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 联系人同步管理器
 * 对应原来的UtilContact.java中的同步功能
 */
@Singleton
class ContactSyncManager @Inject constructor(
    @ApplicationContext private val context: Context,
    private val contactUtils: ContactUtils,
    private val apiService: OasisApiService,
    private val userRepository: UserRepository,
    private val preferences: OasisPreferences
) {
    
    companion object {
        private const val TAG = "ContactSyncManager"
        private const val SYNC_INTERVAL = 24 * 60 * 60 * 1000L // 24小时
        private const val MAX_CONTACTS_PER_BATCH = 100 // 每批最多同步100个联系人
    }
    
    /**
     * 同步联系人到服务器
     */
    suspend fun syncContacts(): Result<Unit> = withContext(Dispatchers.IO) {
        try {
            // 检查是否需要同步
            if (!shouldSync()) {
                Log.d(TAG, "无需同步联系人")
                return@withContext Result.success(Unit)
            }
            
            // 获取当前用户
            val currentUser = userRepository.getCurrentUser()
            if (currentUser == null) {
                Log.w(TAG, "用户未登录，跳过联系人同步")
                return@withContext Result.failure(Exception("用户未登录"))
            }
            
            // 获取所有联系人
            val contacts = contactUtils.getAllContacts(context)
            if (contacts.isEmpty()) {
                Log.d(TAG, "没有联系人需要同步")
                updateLastSyncTime()
                return@withContext Result.success(Unit)
            }
            
            Log.d(TAG, "开始同步 ${contacts.size} 个联系人")
            
            // 分批同步联系人
            val batches = contacts.chunked(MAX_CONTACTS_PER_BATCH)
            var successCount = 0
            var failureCount = 0
            
            for ((index, batch) in batches.withIndex()) {
                try {
                    val result = syncContactBatch(batch)
                    if (result.isSuccess) {
                        successCount += batch.size
                        Log.d(TAG, "批次 ${index + 1}/${batches.size} 同步成功，${batch.size} 个联系人")
                    } else {
                        failureCount += batch.size
                        Log.w(TAG, "批次 ${index + 1}/${batches.size} 同步失败: ${result.exceptionOrNull()?.message}")
                    }
                } catch (e: Exception) {
                    failureCount += batch.size
                    Log.e(TAG, "批次 ${index + 1}/${batches.size} 同步异常", e)
                }
                
                // 批次间稍作延迟，避免服务器压力过大
                if (index < batches.size - 1) {
                    kotlinx.coroutines.delay(1000)
                }
            }
            
            // 更新同步时间
            updateLastSyncTime()
            
            Log.i(TAG, "联系人同步完成: 成功 $successCount 个，失败 $failureCount 个")
            
            if (failureCount == 0) {
                Result.success(Unit)
            } else {
                Result.failure(Exception("部分联系人同步失败: 成功 $successCount 个，失败 $failureCount 个"))
            }
            
        } catch (e: Exception) {
            Log.e(TAG, "联系人同步失败", e)
            Result.failure(e)
        }
    }
    
    /**
     * 同步一批联系人
     */
    private suspend fun syncContactBatch(contacts: List<Contact>): Result<Unit> {
        return try {
            // 获取当前用户ID
            val userId = getCurrentUserId() ?: return Result.failure(Exception("获取用户ID失败"))
            
            // 转换为DTO
            val contactDtos = contacts.map { contact ->
                ContactDto(
                    name = contact.name,
                    phoneNumber = contact.phoneNumber
                )
            }
            
            // 创建更新请求
            val updateRequest = ContactUpdateDto(
                userId = userId,
                contacts = contactDtos
            )
            
            // 发送到服务器
            val response = apiService.updatePhoneNumber(updateRequest)
            
            if (response.isSuccessful && response.body()?.success == true) {
                Result.success(Unit)
            } else {
                val errorMessage = response.body()?.message ?: "同步失败"
                Result.failure(Exception(errorMessage))
            }
            
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    /**
     * 检查是否需要同步
     */
    private fun shouldSync(): Boolean {
        val lastSyncTime = preferences.lastContactUpdateTime
        val currentTime = System.currentTimeMillis()
        
        return (currentTime - lastSyncTime) > SYNC_INTERVAL
    }
    
    /**
     * 更新最后同步时间
     */
    private fun updateLastSyncTime() {
        preferences.lastContactUpdateTime = System.currentTimeMillis()
    }
    
    /**
     * 获取当前用户ID
     */
    private suspend fun getCurrentUserId(): String? {
        return try {
            // 这里可以从UserRepository获取当前用户信息
            // 暂时返回一个示例实现
            "current_user_id"
        } catch (e: Exception) {
            Log.e(TAG, "获取用户ID失败", e)
            null
        }
    }
    
    /**
     * 强制同步联系人
     */
    suspend fun forceSyncContacts(): Result<Unit> {
        // 重置最后同步时间，强制执行同步
        preferences.lastContactUpdateTime = 0
        return syncContacts()
    }
    
    /**
     * 获取同步状态信息
     */
    fun getSyncStatus(): ContactSyncStatus {
        val lastSyncTime = preferences.lastContactUpdateTime
        val nextSyncTime = lastSyncTime + SYNC_INTERVAL
        val currentTime = System.currentTimeMillis()
        
        return ContactSyncStatus(
            lastSyncTime = lastSyncTime,
            nextSyncTime = nextSyncTime,
            isOverdue = currentTime > nextSyncTime,
            contactCount = try {
                contactUtils.getContactWithPhoneCount(context)
            } catch (e: Exception) {
                0
            }
        )
    }
}

/**
 * 联系人同步状态
 */
data class ContactSyncStatus(
    val lastSyncTime: Long,
    val nextSyncTime: Long,
    val isOverdue: Boolean,
    val contactCount: Int
) {
    /**
     * 获取下次同步时间的描述
     */
    fun getNextSyncDescription(): String {
        val currentTime = System.currentTimeMillis()
        val timeDiff = nextSyncTime - currentTime
        
        return when {
            isOverdue -> "需要立即同步"
            timeDiff < 60 * 60 * 1000 -> "1小时内同步"
            timeDiff < 24 * 60 * 60 * 1000 -> "${timeDiff / (60 * 60 * 1000)}小时后同步"
            else -> "${timeDiff / (24 * 60 * 60 * 1000)}天后同步"
        }
    }
}
