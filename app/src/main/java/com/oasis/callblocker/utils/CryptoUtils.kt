package com.oasis.callblocker.utils

import android.util.Base64
import android.util.Log
import java.security.MessageDigest
import javax.crypto.Cipher
import javax.crypto.spec.SecretKeySpec
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 加密工具类
 * 对应原来的RC4.java，提供现代化的加密功能
 */
@Singleton
class CryptoUtils @Inject constructor() {
    
    companion object {
        private const val TAG = "CryptoUtils"
        private const val RC4_ALGORITHM = "RC4"
        private const val AES_ALGORITHM = "AES"
        private const val AES_TRANSFORMATION = "AES/ECB/PKCS5Padding"
    }
    
    /**
     * RC4加密（保持与原系统兼容）
     */
    fun encryptRC4(data: String, key: String): String? {
        return try {
            val keyBytes = key.toByteArray(Charsets.UTF_8)
            val dataBytes = data.toByteArray(Charsets.UTF_8)
            
            val cipher = Cipher.getInstance(RC4_ALGORITHM)
            val secretKey = SecretKeySpec(keyBytes, RC4_ALGORITHM)
            cipher.init(Cipher.ENCRYPT_MODE, secretKey)
            
            val encryptedBytes = cipher.doFinal(dataBytes)
            Base64.encodeToString(encryptedBytes, Base64.DEFAULT)
        } catch (e: Exception) {
            Log.e(TAG, "RC4加密失败", e)
            null
        }
    }
    
    /**
     * RC4解密（保持与原系统兼容）
     */
    fun decryptRC4(encryptedData: String, key: String): String? {
        return try {
            val keyBytes = key.toByteArray(Charsets.UTF_8)
            val encryptedBytes = Base64.decode(encryptedData, Base64.DEFAULT)
            
            val cipher = Cipher.getInstance(RC4_ALGORITHM)
            val secretKey = SecretKeySpec(keyBytes, RC4_ALGORITHM)
            cipher.init(Cipher.DECRYPT_MODE, secretKey)
            
            val decryptedBytes = cipher.doFinal(encryptedBytes)
            String(decryptedBytes, Charsets.UTF_8)
        } catch (e: Exception) {
            Log.e(TAG, "RC4解密失败", e)
            null
        }
    }
    
    /**
     * AES加密（推荐使用）
     */
    fun encryptAES(data: String, key: String): String? {
        return try {
            val keyBytes = generateAESKey(key)
            val dataBytes = data.toByteArray(Charsets.UTF_8)
            
            val cipher = Cipher.getInstance(AES_TRANSFORMATION)
            val secretKey = SecretKeySpec(keyBytes, AES_ALGORITHM)
            cipher.init(Cipher.ENCRYPT_MODE, secretKey)
            
            val encryptedBytes = cipher.doFinal(dataBytes)
            Base64.encodeToString(encryptedBytes, Base64.DEFAULT)
        } catch (e: Exception) {
            Log.e(TAG, "AES加密失败", e)
            null
        }
    }
    
    /**
     * AES解密（推荐使用）
     */
    fun decryptAES(encryptedData: String, key: String): String? {
        return try {
            val keyBytes = generateAESKey(key)
            val encryptedBytes = Base64.decode(encryptedData, Base64.DEFAULT)
            
            val cipher = Cipher.getInstance(AES_TRANSFORMATION)
            val secretKey = SecretKeySpec(keyBytes, AES_ALGORITHM)
            cipher.init(Cipher.DECRYPT_MODE, secretKey)
            
            val decryptedBytes = cipher.doFinal(encryptedBytes)
            String(decryptedBytes, Charsets.UTF_8)
        } catch (e: Exception) {
            Log.e(TAG, "AES解密失败", e)
            null
        }
    }
    
    /**
     * 生成MD5哈希
     */
    fun md5(input: String): String? {
        return try {
            val md = MessageDigest.getInstance("MD5")
            val hashBytes = md.digest(input.toByteArray(Charsets.UTF_8))
            
            val hexString = StringBuilder()
            for (byte in hashBytes) {
                val hex = Integer.toHexString(0xff and byte.toInt())
                if (hex.length == 1) {
                    hexString.append('0')
                }
                hexString.append(hex)
            }
            hexString.toString()
        } catch (e: Exception) {
            Log.e(TAG, "MD5哈希失败", e)
            null
        }
    }
    
    /**
     * 生成SHA256哈希
     */
    fun sha256(input: String): String? {
        return try {
            val digest = MessageDigest.getInstance("SHA-256")
            val hashBytes = digest.digest(input.toByteArray(Charsets.UTF_8))
            
            val hexString = StringBuilder()
            for (byte in hashBytes) {
                val hex = Integer.toHexString(0xff and byte.toInt())
                if (hex.length == 1) {
                    hexString.append('0')
                }
                hexString.append(hex)
            }
            hexString.toString()
        } catch (e: Exception) {
            Log.e(TAG, "SHA256哈希失败", e)
            null
        }
    }
    
    /**
     * 生成随机字符串
     */
    fun generateRandomString(length: Int): String {
        val chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789"
        return (1..length)
            .map { chars.random() }
            .joinToString("")
    }
    
    /**
     * Base64编码
     */
    fun encodeBase64(data: String): String {
        return Base64.encodeToString(data.toByteArray(Charsets.UTF_8), Base64.DEFAULT)
    }
    
    /**
     * Base64解码
     */
    fun decodeBase64(encodedData: String): String? {
        return try {
            val decodedBytes = Base64.decode(encodedData, Base64.DEFAULT)
            String(decodedBytes, Charsets.UTF_8)
        } catch (e: Exception) {
            Log.e(TAG, "Base64解码失败", e)
            null
        }
    }
    
    /**
     * 生成AES密钥（16字节）
     */
    private fun generateAESKey(key: String): ByteArray {
        val keyBytes = key.toByteArray(Charsets.UTF_8)
        val aesKey = ByteArray(16)
        
        // 如果密钥长度不足16字节，用0填充；如果超过16字节，截取前16字节
        System.arraycopy(keyBytes, 0, aesKey, 0, minOf(keyBytes.size, 16))
        
        return aesKey
    }
    
    /**
     * 验证数据完整性
     */
    fun verifyIntegrity(data: String, hash: String, algorithm: String = "SHA-256"): Boolean {
        return try {
            val calculatedHash = when (algorithm.uppercase()) {
                "MD5" -> md5(data)
                "SHA-256" -> sha256(data)
                else -> null
            }
            
            calculatedHash?.equals(hash, ignoreCase = true) == true
        } catch (e: Exception) {
            Log.e(TAG, "验证数据完整性失败", e)
            false
        }
    }
    
    /**
     * 简单的字符串混淆（用于非敏感数据）
     */
    fun obfuscateString(input: String): String {
        return input.map { char ->
            (char.code xor 0x5A).toChar()
        }.joinToString("")
    }
    
    /**
     * 简单的字符串反混淆
     */
    fun deobfuscateString(obfuscated: String): String {
        return obfuscated.map { char ->
            (char.code xor 0x5A).toChar()
        }.joinToString("")
    }
}
