package com.oasis.callblocker.utils.extensions

import android.util.Patterns
import java.text.SimpleDateFormat
import java.util.*

/**
 * 字符串扩展函数
 */

/**
 * 检查字符串是否为空或null
 */
fun String?.isNullOrEmpty(): Boolean = this == null || this.isEmpty()

/**
 * 检查字符串是否为空白或null
 */
fun String?.isNullOrBlank(): Boolean = this == null || this.isBlank()

/**
 * 安全地获取字符串，null时返回空字符串
 */
fun String?.orEmpty(): String = this ?: ""

/**
 * 验证邮箱格式
 */
fun String.isValidEmail(): Boolean {
    return Patterns.EMAIL_ADDRESS.matcher(this).matches()
}

/**
 * 验证手机号码格式
 */
fun String.isValidPhoneNumber(): Boolean {
    val phonePattern = "^1[3-9]\\d{9}$".toRegex()
    return phonePattern.matches(this)
}

/**
 * 格式化电话号码
 * 移除所有非数字字符，处理国际号码格式
 */
fun String.formatPhoneNumber(): String {
    // 移除所有非数字字符
    var formatted = this.replace(Regex("[^0-9]"), "")
    
    // 处理国际号码格式
    if (formatted.startsWith("86") && formatted.length > 11) {
        formatted = formatted.substring(2)
    }
    
    return formatted
}

/**
 * 隐藏电话号码中间部分
 * 例如：13812345678 -> 138****5678
 */
fun String.maskPhoneNumber(): String {
    return when {
        this.length == 11 -> "${this.substring(0, 3)}****${this.substring(7)}"
        this.length >= 7 -> "${this.substring(0, 3)}****${this.substring(this.length - 4)}"
        else -> this
    }
}

/**
 * 将时间戳转换为格式化字符串
 */
fun Long.toFormattedDate(pattern: String = "yyyy-MM-dd HH:mm:ss"): String {
    val date = Date(this)
    val format = SimpleDateFormat(pattern, Locale.getDefault())
    return format.format(date)
}

/**
 * 将时间戳转换为相对时间描述
 */
fun Long.toRelativeTime(): String {
    val now = System.currentTimeMillis()
    val diff = now - this
    
    return when {
        diff < 60 * 1000 -> "刚刚"
        diff < 60 * 60 * 1000 -> "${diff / (60 * 1000)}分钟前"
        diff < 24 * 60 * 60 * 1000 -> "${diff / (60 * 60 * 1000)}小时前"
        diff < 7 * 24 * 60 * 60 * 1000 -> "${diff / (24 * 60 * 60 * 1000)}天前"
        else -> this.toFormattedDate("MM-dd")
    }
}

/**
 * 安全地转换为整数
 */
fun String.toIntOrDefault(default: Int = 0): Int {
    return try {
        this.toInt()
    } catch (e: NumberFormatException) {
        default
    }
}

/**
 * 安全地转换为长整数
 */
fun String.toLongOrDefault(default: Long = 0L): Long {
    return try {
        this.toLong()
    } catch (e: NumberFormatException) {
        default
    }
}

/**
 * 截取字符串到指定长度，超出部分用省略号表示
 */
fun String.truncate(maxLength: Int, ellipsis: String = "..."): String {
    return if (this.length <= maxLength) {
        this
    } else {
        this.substring(0, maxLength - ellipsis.length) + ellipsis
    }
}

/**
 * 首字母大写
 */
fun String.capitalize(): String {
    return if (this.isEmpty()) {
        this
    } else {
        this.substring(0, 1).uppercase() + this.substring(1).lowercase()
    }
}

/**
 * 移除HTML标签
 */
fun String.removeHtmlTags(): String {
    return this.replace(Regex("<[^>]*>"), "")
}

/**
 * 检查字符串是否包含中文字符
 */
fun String.containsChinese(): Boolean {
    val chinesePattern = "[\u4e00-\u9fa5]".toRegex()
    return chinesePattern.containsMatchIn(this)
}

/**
 * 获取字符串的字节长度（中文字符按2个字节计算）
 */
fun String.getByteLength(): Int {
    var length = 0
    for (char in this) {
        length += if (char.code > 127) 2 else 1
    }
    return length
}
