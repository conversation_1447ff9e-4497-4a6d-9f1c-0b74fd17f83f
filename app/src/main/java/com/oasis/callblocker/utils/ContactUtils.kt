package com.oasis.callblocker.utils

import android.Manifest
import android.content.Context
import android.database.Cursor
import android.provider.ContactsContract
import android.util.Log
import androidx.annotation.RequiresPermission
import com.oasis.callblocker.utils.extensions.hasPermission
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 联系人工具类
 * 对应原来的UtilContact.java
 */
@Singleton
class ContactUtils @Inject constructor() {
    
    companion object {
        private const val TAG = "ContactUtils"
    }
    
    /**
     * 根据电话号码获取联系人姓名
     */
    @RequiresPermission(Manifest.permission.READ_CONTACTS)
    fun getContactName(context: Context, phoneNumber: String): String? {
        if (!context.hasPermission(Manifest.permission.READ_CONTACTS)) {
            Log.w(TAG, "没有读取联系人权限")
            return null
        }
        
        return try {
            val uri = ContactsContract.PhoneLookup.CONTENT_FILTER_URI
            val lookupUri = android.net.Uri.withAppendedPath(uri, android.net.Uri.encode(phoneNumber))
            
            val projection = arrayOf(ContactsContract.PhoneLookup.DISPLAY_NAME)
            
            context.contentResolver.query(lookupUri, projection, null, null, null)?.use { cursor ->
                if (cursor.moveToFirst()) {
                    val nameIndex = cursor.getColumnIndex(ContactsContract.PhoneLookup.DISPLAY_NAME)
                    if (nameIndex >= 0) {
                        cursor.getString(nameIndex)
                    } else null
                } else null
            }
        } catch (e: Exception) {
            Log.e(TAG, "获取联系人姓名失败: $phoneNumber", e)
            null
        }
    }
    
    /**
     * 获取所有联系人
     */
    @RequiresPermission(Manifest.permission.READ_CONTACTS)
    fun getAllContacts(context: Context): List<Contact> {
        if (!context.hasPermission(Manifest.permission.READ_CONTACTS)) {
            Log.w(TAG, "没有读取联系人权限")
            return emptyList()
        }
        
        val contacts = mutableListOf<Contact>()
        
        try {
            val projection = arrayOf(
                ContactsContract.CommonDataKinds.Phone.CONTACT_ID,
                ContactsContract.CommonDataKinds.Phone.DISPLAY_NAME,
                ContactsContract.CommonDataKinds.Phone.NUMBER
            )
            
            val cursor = context.contentResolver.query(
                ContactsContract.CommonDataKinds.Phone.CONTENT_URI,
                projection,
                null,
                null,
                ContactsContract.CommonDataKinds.Phone.DISPLAY_NAME + " ASC"
            )
            
            cursor?.use {
                val contactIdIndex = it.getColumnIndex(ContactsContract.CommonDataKinds.Phone.CONTACT_ID)
                val nameIndex = it.getColumnIndex(ContactsContract.CommonDataKinds.Phone.DISPLAY_NAME)
                val numberIndex = it.getColumnIndex(ContactsContract.CommonDataKinds.Phone.NUMBER)
                
                while (it.moveToNext()) {
                    if (contactIdIndex >= 0 && nameIndex >= 0 && numberIndex >= 0) {
                        val contactId = it.getLong(contactIdIndex)
                        val name = it.getString(nameIndex) ?: continue
                        val number = it.getString(numberIndex) ?: continue
                        
                        contacts.add(Contact(contactId, name, number.replace(Regex("[^0-9]"), "")))
                    }
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "获取所有联系人失败", e)
        }
        
        return contacts
    }
    
    /**
     * 检查号码是否在联系人中
     */
    @RequiresPermission(Manifest.permission.READ_CONTACTS)
    fun isContactExists(context: Context, phoneNumber: String): Boolean {
        return getContactName(context, phoneNumber) != null
    }
    
    /**
     * 根据姓名搜索联系人
     */
    @RequiresPermission(Manifest.permission.READ_CONTACTS)
    fun searchContactsByName(context: Context, name: String): List<Contact> {
        if (!context.hasPermission(Manifest.permission.READ_CONTACTS)) {
            Log.w(TAG, "没有读取联系人权限")
            return emptyList()
        }
        
        val contacts = mutableListOf<Contact>()
        
        try {
            val projection = arrayOf(
                ContactsContract.CommonDataKinds.Phone.CONTACT_ID,
                ContactsContract.CommonDataKinds.Phone.DISPLAY_NAME,
                ContactsContract.CommonDataKinds.Phone.NUMBER
            )
            
            val selection = "${ContactsContract.CommonDataKinds.Phone.DISPLAY_NAME} LIKE ?"
            val selectionArgs = arrayOf("%$name%")
            
            val cursor = context.contentResolver.query(
                ContactsContract.CommonDataKinds.Phone.CONTENT_URI,
                projection,
                selection,
                selectionArgs,
                ContactsContract.CommonDataKinds.Phone.DISPLAY_NAME + " ASC"
            )
            
            cursor?.use {
                val contactIdIndex = it.getColumnIndex(ContactsContract.CommonDataKinds.Phone.CONTACT_ID)
                val nameIndex = it.getColumnIndex(ContactsContract.CommonDataKinds.Phone.DISPLAY_NAME)
                val numberIndex = it.getColumnIndex(ContactsContract.CommonDataKinds.Phone.NUMBER)
                
                while (it.moveToNext()) {
                    if (contactIdIndex >= 0 && nameIndex >= 0 && numberIndex >= 0) {
                        val contactId = it.getLong(contactIdIndex)
                        val contactName = it.getString(nameIndex) ?: continue
                        val number = it.getString(numberIndex) ?: continue
                        
                        contacts.add(Contact(contactId, contactName, number.replace(Regex("[^0-9]"), "")))
                    }
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "搜索联系人失败: $name", e)
        }
        
        return contacts
    }
    
    /**
     * 获取联系人数量
     */
    @RequiresPermission(Manifest.permission.READ_CONTACTS)
    fun getContactCount(context: Context): Int {
        if (!context.hasPermission(Manifest.permission.READ_CONTACTS)) {
            return 0
        }
        
        return try {
            val cursor = context.contentResolver.query(
                ContactsContract.Contacts.CONTENT_URI,
                arrayOf(ContactsContract.Contacts._ID),
                null,
                null,
                null
            )
            
            cursor?.use { it.count } ?: 0
        } catch (e: Exception) {
            Log.e(TAG, "获取联系人数量失败", e)
            0
        }
    }
    
    /**
     * 获取有电话号码的联系人数量
     */
    @RequiresPermission(Manifest.permission.READ_CONTACTS)
    fun getContactWithPhoneCount(context: Context): Int {
        if (!context.hasPermission(Manifest.permission.READ_CONTACTS)) {
            return 0
        }
        
        return try {
            val cursor = context.contentResolver.query(
                ContactsContract.CommonDataKinds.Phone.CONTENT_URI,
                arrayOf(ContactsContract.CommonDataKinds.Phone.CONTACT_ID),
                null,
                null,
                null
            )
            
            cursor?.use { 
                val contactIds = mutableSetOf<Long>()
                val contactIdIndex = it.getColumnIndex(ContactsContract.CommonDataKinds.Phone.CONTACT_ID)
                
                while (it.moveToNext()) {
                    if (contactIdIndex >= 0) {
                        contactIds.add(it.getLong(contactIdIndex))
                    }
                }
                contactIds.size
            } ?: 0
        } catch (e: Exception) {
            Log.e(TAG, "获取有电话号码的联系人数量失败", e)
            0
        }
    }
}

/**
 * 联系人数据类
 */
data class Contact(
    val id: Long,
    val name: String,
    val phoneNumber: String
) {
    /**
     * 获取格式化的电话号码
     */
    fun getFormattedPhoneNumber(): String {
        return when (phoneNumber.length) {
            11 -> "${phoneNumber.substring(0, 3)} ${phoneNumber.substring(3, 7)} ${phoneNumber.substring(7)}"
            10 -> "${phoneNumber.substring(0, 3)} ${phoneNumber.substring(3, 7)} ${phoneNumber.substring(7)}"
            else -> phoneNumber
        }
    }
}
