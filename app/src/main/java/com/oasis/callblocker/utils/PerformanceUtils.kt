package com.oasis.callblocker.utils

import android.util.Log
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import javax.inject.Inject
import javax.inject.Singleton
import kotlin.system.measureTimeMillis

/**
 * 性能优化工具类
 */
@Singleton
class PerformanceUtils @Inject constructor() {
    
    companion object {
        private const val TAG = "PerformanceUtils"
        private const val SLOW_OPERATION_THRESHOLD = 100L // 100ms
    }
    
    /**
     * 测量代码块执行时间
     */
    inline fun <T> measureTime(
        operationName: String,
        block: () -> T
    ): T {
        val result: T
        val timeMillis = measureTimeMillis {
            result = block()
        }
        
        if (timeMillis > SLOW_OPERATION_THRESHOLD) {
            Log.w(TAG, "$operationName 执行时间较长: ${timeMillis}ms")
        } else {
            Log.d(TAG, "$operationName 执行时间: ${timeMillis}ms")
        }
        
        return result
    }
    
    /**
     * 测量挂起函数执行时间
     */
    suspend inline fun <T> measureTimeSuspend(
        operationName: String,
        crossinline block: suspend () -> T
    ): T {
        val result: T
        val timeMillis = measureTimeMillis {
            result = block()
        }
        
        if (timeMillis > SLOW_OPERATION_THRESHOLD) {
            Log.w(TAG, "$operationName 执行时间较长: ${timeMillis}ms")
        } else {
            Log.d(TAG, "$operationName 执行时间: ${timeMillis}ms")
        }
        
        return result
    }
    
    /**
     * 在IO线程执行操作
     */
    suspend fun <T> executeOnIO(block: suspend () -> T): T {
        return withContext(Dispatchers.IO) {
            block()
        }
    }
    
    /**
     * 在主线程执行操作
     */
    suspend fun <T> executeOnMain(block: suspend () -> T): T {
        return withContext(Dispatchers.Main) {
            block()
        }
    }
    
    /**
     * 在计算线程执行操作
     */
    suspend fun <T> executeOnDefault(block: suspend () -> T): T {
        return withContext(Dispatchers.Default) {
            block()
        }
    }
    
    /**
     * 异步执行操作（不等待结果）
     */
    fun executeAsync(
        scope: CoroutineScope,
        block: suspend () -> Unit
    ) {
        scope.launch {
            try {
                block()
            } catch (e: Exception) {
                Log.e(TAG, "异步操作执行失败", e)
            }
        }
    }
    
    /**
     * 批量处理数据
     */
    suspend fun <T, R> processBatch(
        items: List<T>,
        batchSize: Int = 100,
        processor: suspend (List<T>) -> List<R>
    ): List<R> {
        val results = mutableListOf<R>()
        
        items.chunked(batchSize).forEach { batch ->
            val batchResults = measureTimeSuspend("批量处理 ${batch.size} 项") {
                processor(batch)
            }
            results.addAll(batchResults)
        }
        
        return results
    }
    
    /**
     * 内存使用情况监控
     */
    fun logMemoryUsage(tag: String = "MemoryUsage") {
        val runtime = Runtime.getRuntime()
        val usedMemory = runtime.totalMemory() - runtime.freeMemory()
        val maxMemory = runtime.maxMemory()
        val availableMemory = maxMemory - usedMemory
        
        Log.d(tag, """
            内存使用情况:
            已使用: ${formatBytes(usedMemory)}
            最大可用: ${formatBytes(maxMemory)}
            剩余可用: ${formatBytes(availableMemory)}
            使用率: ${(usedMemory * 100 / maxMemory)}%
        """.trimIndent())
    }
    
    /**
     * 格式化字节数
     */
    private fun formatBytes(bytes: Long): String {
        val kb = bytes / 1024.0
        val mb = kb / 1024.0
        val gb = mb / 1024.0
        
        return when {
            gb >= 1 -> String.format("%.2f GB", gb)
            mb >= 1 -> String.format("%.2f MB", mb)
            kb >= 1 -> String.format("%.2f KB", kb)
            else -> "$bytes B"
        }
    }
    
    /**
     * 垃圾回收建议
     */
    fun suggestGC() {
        val runtime = Runtime.getRuntime()
        val usedMemory = runtime.totalMemory() - runtime.freeMemory()
        val maxMemory = runtime.maxMemory()
        val usagePercentage = (usedMemory * 100 / maxMemory)
        
        if (usagePercentage > 80) {
            Log.w(TAG, "内存使用率较高 ($usagePercentage%)，建议执行垃圾回收")
            System.gc()
        }
    }
    
    /**
     * 缓存命中率统计
     */
    class CacheStats {
        private var hits = 0L
        private var misses = 0L
        
        fun recordHit() {
            hits++
        }
        
        fun recordMiss() {
            misses++
        }
        
        fun getHitRate(): Double {
            val total = hits + misses
            return if (total > 0) hits.toDouble() / total else 0.0
        }
        
        fun reset() {
            hits = 0
            misses = 0
        }
        
        fun logStats(tag: String) {
            val total = hits + misses
            val hitRate = getHitRate() * 100
            Log.d(tag, "缓存统计: 命中 $hits 次, 未命中 $misses 次, 总计 $total 次, 命中率 ${String.format("%.2f", hitRate)}%")
        }
    }
    
    /**
     * 操作频率限制器
     */
    class RateLimiter(private val maxOperationsPerSecond: Int) {
        private val operations = mutableListOf<Long>()
        
        fun canExecute(): Boolean {
            val now = System.currentTimeMillis()
            
            // 移除1秒前的操作记录
            operations.removeAll { it < now - 1000 }
            
            return if (operations.size < maxOperationsPerSecond) {
                operations.add(now)
                true
            } else {
                false
            }
        }
        
        fun getRemainingOperations(): Int {
            val now = System.currentTimeMillis()
            operations.removeAll { it < now - 1000 }
            return maxOf(0, maxOperationsPerSecond - operations.size)
        }
    }
    
    /**
     * 简单的性能计数器
     */
    class PerformanceCounter(private val name: String) {
        private var count = 0L
        private var totalTime = 0L
        private var minTime = Long.MAX_VALUE
        private var maxTime = 0L
        
        fun record(timeMillis: Long) {
            count++
            totalTime += timeMillis
            minTime = minOf(minTime, timeMillis)
            maxTime = maxOf(maxTime, timeMillis)
        }
        
        fun getAverageTime(): Double {
            return if (count > 0) totalTime.toDouble() / count else 0.0
        }
        
        fun logStats() {
            Log.d(TAG, """
                性能统计 [$name]:
                执行次数: $count
                总时间: ${totalTime}ms
                平均时间: ${String.format("%.2f", getAverageTime())}ms
                最短时间: ${if (minTime == Long.MAX_VALUE) 0 else minTime}ms
                最长时间: ${maxTime}ms
            """.trimIndent())
        }
        
        fun reset() {
            count = 0
            totalTime = 0
            minTime = Long.MAX_VALUE
            maxTime = 0
        }
    }
}
