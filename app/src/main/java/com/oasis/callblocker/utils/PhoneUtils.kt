package com.oasis.callblocker.utils

import android.Manifest
import android.content.Context
import android.content.Intent
import android.net.Uri
import android.os.Build
import android.telecom.TelecomManager
import android.telephony.TelephonyManager
import android.util.Log
import androidx.annotation.RequiresPermission
import com.oasis.callblocker.utils.extensions.hasPermission
import java.lang.reflect.Method
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 电话工具类
 * 对应原来的Utils.java中的电话相关功能
 */
@Singleton
class PhoneUtils @Inject constructor() {
    
    companion object {
        private const val TAG = "PhoneUtils"
    }
    
    /**
     * 拨打电话
     */
    @RequiresPermission(Manifest.permission.CALL_PHONE)
    fun makeCall(context: Context, phoneNumber: String): Boolean {
        return try {
            if (!context.hasPermission(Manifest.permission.CALL_PHONE)) {
                Log.w(TAG, "没有拨打电话权限")
                return false
            }
            
            val intent = Intent(Intent.ACTION_CALL).apply {
                data = Uri.parse("tel:$phoneNumber")
                flags = Intent.FLAG_ACTIVITY_NEW_TASK
            }
            context.startActivity(intent)
            true
        } catch (e: Exception) {
            Log.e(TAG, "拨打电话失败: $phoneNumber", e)
            false
        }
    }
    
    /**
     * 跳转到拨号界面
     */
    fun openDialer(context: Context, phoneNumber: String? = null): Boolean {
        return try {
            val intent = Intent(Intent.ACTION_DIAL).apply {
                if (!phoneNumber.isNullOrBlank()) {
                    data = Uri.parse("tel:$phoneNumber")
                }
                flags = Intent.FLAG_ACTIVITY_NEW_TASK
            }
            context.startActivity(intent)
            true
        } catch (e: Exception) {
            Log.e(TAG, "打开拨号界面失败", e)
            false
        }
    }
    
    /**
     * 结束通话
     */
    @RequiresPermission(anyOf = [
        Manifest.permission.ANSWER_PHONE_CALLS,
        Manifest.permission.MODIFY_PHONE_STATE
    ])
    fun endCall(context: Context): Boolean {
        return try {
            when {
                Build.VERSION.SDK_INT >= Build.VERSION_CODES.P -> {
                    endCallApi28(context)
                }
                Build.VERSION.SDK_INT >= Build.VERSION_CODES.O -> {
                    endCallApi26(context)
                }
                else -> {
                    endCallReflection()
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "结束通话失败", e)
            false
        }
    }
    
    /**
     * Android 9.0+ 结束通话
     */
    @RequiresPermission(Manifest.permission.ANSWER_PHONE_CALLS)
    private fun endCallApi28(context: Context): Boolean {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.P) {
            try {
                val telecomManager = context.getSystemService(Context.TELECOM_SERVICE) as TelecomManager
                telecomManager.endCall()
                true
            } catch (e: Exception) {
                Log.e(TAG, "API 28+ 结束通话失败", e)
                false
            }
        } else {
            false
        }
    }
    
    /**
     * Android 8.0+ 结束通话
     */
    @RequiresPermission(Manifest.permission.ANSWER_PHONE_CALLS)
    private fun endCallApi26(context: Context): Boolean {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            try {
                val telephonyManager = context.getSystemService(Context.TELEPHONY_SERVICE) as TelephonyManager
                telephonyManager.endCall()
                true
            } catch (e: Exception) {
                Log.e(TAG, "API 26+ 结束通话失败", e)
                false
            }
        } else {
            false
        }
    }
    
    /**
     * 使用反射结束通话（兼容旧版本）
     */
    private fun endCallReflection(): Boolean {
        return try {
            val telephonyClass = Class.forName("com.android.internal.telephony.ITelephony")
            val telephonyStubClass = Class.forName("com.android.internal.telephony.ITelephony\$Stub")
            val serviceManagerClass = Class.forName("android.os.ServiceManager")
            
            val getServiceMethod = serviceManagerClass.getMethod("getService", String::class.java)
            val asInterfaceMethod = telephonyStubClass.getMethod("asInterface", android.os.IBinder::class.java)
            val endCallMethod = telephonyClass.getMethod("endCall")
            
            val binder = getServiceMethod.invoke(null, "phone") as android.os.IBinder
            val telephonyService = asInterfaceMethod.invoke(null, binder)
            endCallMethod.invoke(telephonyService)
            
            true
        } catch (e: Exception) {
            Log.e(TAG, "反射结束通话失败", e)
            false
        }
    }
    
    /**
     * 接听电话
     */
    @RequiresPermission(Manifest.permission.ANSWER_PHONE_CALLS)
    fun answerCall(context: Context): Boolean {
        return try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                val telecomManager = context.getSystemService(Context.TELECOM_SERVICE) as TelecomManager
                telecomManager.acceptRingingCall()
                true
            } else {
                answerCallReflection()
            }
        } catch (e: Exception) {
            Log.e(TAG, "接听电话失败", e)
            false
        }
    }
    
    /**
     * 使用反射接听电话（兼容旧版本）
     */
    private fun answerCallReflection(): Boolean {
        return try {
            val telephonyClass = Class.forName("com.android.internal.telephony.ITelephony")
            val telephonyStubClass = Class.forName("com.android.internal.telephony.ITelephony\$Stub")
            val serviceManagerClass = Class.forName("android.os.ServiceManager")
            
            val getServiceMethod = serviceManagerClass.getMethod("getService", String::class.java)
            val asInterfaceMethod = telephonyStubClass.getMethod("asInterface", android.os.IBinder::class.java)
            val answerCallMethod = telephonyClass.getMethod("answerRingingCall")
            
            val binder = getServiceMethod.invoke(null, "phone") as android.os.IBinder
            val telephonyService = asInterfaceMethod.invoke(null, binder)
            answerCallMethod.invoke(telephonyService)
            
            true
        } catch (e: Exception) {
            Log.e(TAG, "反射接听电话失败", e)
            false
        }
    }
    
    /**
     * 获取通话状态
     */
    @RequiresPermission(Manifest.permission.READ_PHONE_STATE)
    fun getCallState(context: Context): Int {
        return try {
            val telephonyManager = context.getSystemService(Context.TELEPHONY_SERVICE) as TelephonyManager
            telephonyManager.callState
        } catch (e: Exception) {
            Log.e(TAG, "获取通话状态失败", e)
            TelephonyManager.CALL_STATE_IDLE
        }
    }
    
    /**
     * 检查是否正在通话
     */
    @RequiresPermission(Manifest.permission.READ_PHONE_STATE)
    fun isInCall(context: Context): Boolean {
        val callState = getCallState(context)
        return callState == TelephonyManager.CALL_STATE_OFFHOOK || 
               callState == TelephonyManager.CALL_STATE_RINGING
    }
    
    /**
     * 格式化电话号码显示
     */
    fun formatPhoneNumberForDisplay(phoneNumber: String): String {
        val cleaned = phoneNumber.replace(Regex("[^0-9]"), "")
        
        return when (cleaned.length) {
            11 -> {
                // 手机号码格式：138 1234 5678
                "${cleaned.substring(0, 3)} ${cleaned.substring(3, 7)} ${cleaned.substring(7)}"
            }
            10 -> {
                // 固话格式：010 1234 5678
                "${cleaned.substring(0, 3)} ${cleaned.substring(3, 7)} ${cleaned.substring(7)}"
            }
            8 -> {
                // 固话格式：1234 5678
                "${cleaned.substring(0, 4)} ${cleaned.substring(4)}"
            }
            7 -> {
                // 固话格式：123 4567
                "${cleaned.substring(0, 3)} ${cleaned.substring(3)}"
            }
            else -> phoneNumber
        }
    }
    
    /**
     * 验证电话号码格式
     */
    fun isValidPhoneNumber(phoneNumber: String): Boolean {
        val cleaned = phoneNumber.replace(Regex("[^0-9]"), "")
        
        return when {
            // 手机号码
            cleaned.length == 11 && cleaned.startsWith("1") -> {
                val secondDigit = cleaned[1]
                secondDigit in '3'..'9'
            }
            // 固定电话
            cleaned.length in 7..12 -> true
            else -> false
        }
    }
}
