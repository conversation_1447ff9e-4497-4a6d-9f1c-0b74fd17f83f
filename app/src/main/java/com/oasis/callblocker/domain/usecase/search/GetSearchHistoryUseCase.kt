package com.oasis.callblocker.domain.usecase.search

import com.oasis.callblocker.domain.model.PhoneSearchResult
import com.oasis.callblocker.domain.repository.PhoneSearchRepository
import kotlinx.coroutines.flow.Flow
import javax.inject.Inject

/**
 * 获取搜索历史用例
 */
class GetSearchHistoryUseCase @Inject constructor(
    private val phoneSearchRepository: PhoneSearchRepository
) {
    /**
     * 获取搜索历史
     * @param limit 限制返回数量
     */
    operator fun invoke(limit: Int = 50): Flow<List<PhoneSearchResult>> {
        return phoneSearchRepository.getSearchHistory(limit)
    }
    
    /**
     * 清除搜索历史
     */
    suspend fun clearHistory() {
        phoneSearchRepository.clearSearchHistory()
    }
    
    /**
     * 删除指定搜索结果
     */
    suspend fun deleteSearchResult(phoneNumber: String) {
        phoneSearchRepository.deleteSearchResult(phoneNumber)
    }
}
