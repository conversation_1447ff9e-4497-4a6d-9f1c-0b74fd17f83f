package com.oasis.callblocker.domain.usecase.block

import com.oasis.callblocker.domain.model.BlockNumber
import com.oasis.callblocker.domain.model.BlockType
import com.oasis.callblocker.domain.repository.BlockRepository
import kotlinx.coroutines.flow.Flow
import javax.inject.Inject

/**
 * 管理拦截号码用例
 */
class ManageBlockNumbersUseCase @Inject constructor(
    private val blockRepository: BlockRepository
) {
    /**
     * 获取所有拦截号码
     */
    fun getAllBlockNumbers(): Flow<List<BlockNumber>> {
        return blockRepository.getAllBlockNumbers()
    }
    
    /**
     * 根据类型获取拦截号码
     */
    fun getBlockNumbersByType(blockType: BlockType): Flow<List<BlockNumber>> {
        return blockRepository.getBlockNumbersByType(blockType.value)
    }
    
    /**
     * 添加拦截号码
     * @param phoneNumber 电话号码
     * @param blockType 拦截类型
     */
    suspend fun addBlockNumber(phoneNumber: String, blockType: BlockType) {
        // 验证电话号码格式
        val formattedNumber = validateAndFormatPhoneNumber(phoneNumber)
        
        // 检查是否已存在
        val existingNumber = blockRepository.getBlockNumber(formattedNumber)
        if (existingNumber != null) {
            throw IllegalArgumentException("该号码已在拦截列表中")
        }
        
        // 创建拦截号码
        val blockNumber = BlockNumber(
            phoneNumber = formattedNumber,
            blockType = blockType
        )
        
        blockRepository.addBlockNumber(blockNumber)
    }
    
    /**
     * 移除拦截号码
     * @param phoneNumber 电话号码
     */
    suspend fun removeBlockNumber(phoneNumber: String) {
        val formattedNumber = validateAndFormatPhoneNumber(phoneNumber)
        blockRepository.removeBlockNumber(formattedNumber)
    }
    
    /**
     * 批量添加拦截号码
     * @param phoneNumbers 电话号码列表
     * @param blockType 拦截类型
     */
    suspend fun addBlockNumbers(phoneNumbers: List<String>, blockType: BlockType) {
        phoneNumbers.forEach { phoneNumber ->
            try {
                addBlockNumber(phoneNumber, blockType)
            } catch (e: Exception) {
                // 记录错误但继续处理其他号码
                // 可以考虑返回处理结果列表
            }
        }
    }
    
    /**
     * 验证并格式化电话号码
     */
    private fun validateAndFormatPhoneNumber(phoneNumber: String): String {
        if (phoneNumber.isBlank()) {
            throw IllegalArgumentException("电话号码不能为空")
        }
        
        // 移除所有非数字字符
        var formatted = phoneNumber.replace(Regex("[^0-9]"), "")
        
        // 处理国际号码格式
        if (formatted.startsWith("86") && formatted.length > 11) {
            formatted = formatted.substring(2)
        }
        
        // 验证号码长度
        if (formatted.length < 7 || formatted.length > 11) {
            throw IllegalArgumentException("电话号码格式不正确")
        }
        
        return formatted
    }
}
