package com.oasis.callblocker.domain.model

/**
 * 通知领域模型
 */
data class Notice(
    val noticeId: String,
    val title: String,
    val content: String,
    val noticeType: NoticeType = NoticeType.NORMAL,
    val isRead: Boolean = false,
    val publishTime: Long,
    val createdAt: Long = System.currentTimeMillis()
) {
    /**
     * 格式化发布时间
     */
    fun getFormattedPublishTime(): String {
        val date = java.util.Date(publishTime)
        val format = java.text.SimpleDateFormat("yyyy-MM-dd HH:mm", java.util.Locale.getDefault())
        return format.format(date)
    }
    
    /**
     * 获取相对时间描述
     */
    fun getRelativeTimeDescription(): String {
        val now = System.currentTimeMillis()
        val diff = now - publishTime
        
        return when {
            diff < 60 * 1000 -> "刚刚"
            diff < 60 * 60 * 1000 -> "${diff / (60 * 1000)}分钟前"
            diff < 24 * 60 * 60 * 1000 -> "${diff / (60 * 60 * 1000)}小时前"
            diff < 7 * 24 * 60 * 60 * 1000 -> "${diff / (24 * 60 * 60 * 1000)}天前"
            else -> getFormattedPublishTime()
        }
    }
    
    /**
     * 获取通知摘要（截取前50个字符）
     */
    fun getSummary(): String {
        return if (content.length > 50) {
            content.take(50) + "..."
        } else {
            content
        }
    }
    
    /**
     * 是否为新通知（24小时内）
     */
    fun isNew(): Boolean {
        val oneDayAgo = System.currentTimeMillis() - 24 * 60 * 60 * 1000
        return publishTime > oneDayAgo
    }
}

/**
 * 通知类型枚举
 */
enum class NoticeType(val value: Int, val displayName: String, val priority: Int) {
    NORMAL(0, "普通", 1),
    IMPORTANT(1, "重要", 2),
    URGENT(2, "紧急", 3);
    
    companion object {
        fun fromValue(value: Int): NoticeType {
            return values().find { it.value == value } ?: NORMAL
        }
    }
}

/**
 * 通知统计信息
 */
data class NoticeStatistics(
    val totalNotices: Int = 0,
    val unreadNotices: Int = 0,
    val normalNotices: Int = 0,
    val importantNotices: Int = 0,
    val urgentNotices: Int = 0,
    val newNotices: Int = 0, // 24小时内的新通知
    val lastCheckTime: Long = 0
) {
    /**
     * 获取未读率
     */
    fun getUnreadRate(): Float {
        return if (totalNotices > 0) {
            (unreadNotices.toFloat() / totalNotices.toFloat()) * 100
        } else 0f
    }
    
    /**
     * 是否有重要未读通知
     */
    fun hasImportantUnread(): Boolean {
        return importantNotices > 0 || urgentNotices > 0
    }
}
