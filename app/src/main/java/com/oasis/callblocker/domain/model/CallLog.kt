package com.oasis.callblocker.domain.model

/**
 * 通话记录领域模型
 */
data class CallLog(
    val id: Long = 0,
    val phoneNumber: String,
    val contactName: String? = null,
    val callType: CallType,
    val duration: Long = 0, // 通话时长（秒）
    val timestamp: Long = System.currentTimeMillis(),
    val isBlocked: Boolean = false
) {
    /**
     * 格式化通话时长
     */
    fun getFormattedDuration(): String {
        if (duration <= 0) return "00:00"
        
        val minutes = duration / 60
        val seconds = duration % 60
        return String.format("%02d:%02d", minutes, seconds)
    }
    
    /**
     * 格式化时间显示
     */
    fun getFormattedTime(): String {
        val date = java.util.Date(timestamp)
        val format = java.text.SimpleDateFormat("MM-dd HH:mm", java.util.Locale.getDefault())
        return format.format(date)
    }
    
    /**
     * 获取显示名称（联系人名称或号码）
     */
    fun getDisplayName(): String {
        return contactName?.takeIf { it.isNotBlank() } ?: phoneNumber
    }
    
    /**
     * 获取通话状态描述
     */
    fun getStatusDescription(): String {
        return when {
            isBlocked -> "已拦截"
            callType == CallType.MISSED -> "未接"
            callType == CallType.INCOMING -> "来电"
            callType == CallType.OUTGOING -> "去电"
            else -> "未知"
        }
    }
}

/**
 * 通话类型枚举
 */
enum class CallType(val value: Int, val displayName: String) {
    INCOMING(0, "来电"),
    OUTGOING(1, "去电"),
    MISSED(2, "未接");
    
    companion object {
        fun fromValue(value: Int): CallType {
            return values().find { it.value == value } ?: INCOMING
        }
    }
}

/**
 * 通话统计信息
 */
data class CallStatistics(
    val totalCalls: Int = 0,
    val incomingCalls: Int = 0,
    val outgoingCalls: Int = 0,
    val missedCalls: Int = 0,
    val blockedCalls: Int = 0,
    val totalDuration: Long = 0, // 总通话时长（秒）
    val averageDuration: Long = 0, // 平均通话时长（秒）
    val todayBlockedCount: Int = 0,
    val weeklyBlockedCount: Int = 0,
    val monthlyBlockedCount: Int = 0
) {
    /**
     * 获取拦截率
     */
    fun getBlockRate(): Float {
        return if (totalCalls > 0) {
            (blockedCalls.toFloat() / totalCalls.toFloat()) * 100
        } else 0f
    }
    
    /**
     * 格式化总通话时长
     */
    fun getFormattedTotalDuration(): String {
        val hours = totalDuration / 3600
        val minutes = (totalDuration % 3600) / 60
        val seconds = totalDuration % 60
        
        return when {
            hours > 0 -> String.format("%d小时%02d分%02d秒", hours, minutes, seconds)
            minutes > 0 -> String.format("%d分%02d秒", minutes, seconds)
            else -> String.format("%d秒", seconds)
        }
    }
}
