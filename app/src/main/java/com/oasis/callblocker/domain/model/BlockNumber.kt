package com.oasis.callblocker.domain.model

/**
 * 拦截号码领域模型
 */
data class BlockNumber(
    val phoneNumber: String,
    val blockType: BlockType,
    val todayCount: Int = 0,
    val createdAt: Long = System.currentTimeMillis(),
    val updatedAt: Long = System.currentTimeMillis()
) {
    /**
     * 检查是否应该拦截此号码
     */
    fun shouldBlock(settings: BlockSettings): Boolean {
        return when (blockType) {
            BlockType.SPECIFIC -> settings.isBlockSpecificNumbers
            BlockType.PREFIX -> settings.isBlockPrefix
            BlockType.EXPLOSION -> settings.isBlockCallExplosion
        }
    }
    
    /**
     * 检查今日通话是否超限
     */
    fun isTodayLimitExceeded(limit: Int): Boolean {
        return todayCount >= limit
    }
}

/**
 * 拦截类型枚举
 */
enum class BlockType(val value: Int) {
    SPECIFIC(0),    // 特定号码
    PREFIX(1),      // 前缀拦截
    EXPLOSION(2);   // 呼叫爆炸
    
    companion object {
        fun fromValue(value: Int): BlockType {
            return values().find { it.value == value } ?: SPECIFIC
        }
    }
}

/**
 * 拦截历史领域模型
 */
data class BlockHistory(
    val id: Long = 0,
    val phoneNumber: String,
    val blockType: BlockHistoryType,
    val timestamp: Long = System.currentTimeMillis(),
    val contactName: String? = null
) {
    /**
     * 格式化时间显示
     */
    fun getFormattedTime(): String {
        val date = java.util.Date(timestamp)
        val format = java.text.SimpleDateFormat("MM-dd HH:mm", java.util.Locale.getDefault())
        return format.format(date)
    }
}

/**
 * 拦截历史类型枚举
 */
enum class BlockHistoryType(val value: Int, val displayName: String) {
    UNKNOWN(0, "未知号码"),
    TODAY_LIMIT(1, "今日通话限制"),
    SPECIFIC(2, "特定号码"),
    PREFIX(3, "前缀拦截"),
    ALL(4, "全部拦截"),
    EXPLOSION(5, "呼叫爆炸");
    
    companion object {
        fun fromValue(value: Int): BlockHistoryType {
            return values().find { it.value == value } ?: UNKNOWN
        }
    }
}

/**
 * 拦截设置领域模型
 */
data class BlockSettings(
    val isBlockUnknown: Boolean = false,
    val isBlockSpecificNumbers: Boolean = false,
    val isBlockPrefix: Boolean = false,
    val isBlockTodayCall: Boolean = false,
    val isBlockAll: Boolean = false,
    val isBlockCallExplosion: Boolean = false,
    val limitTodayCall: Int = 3,
    val callExplosionCount: Int = 5,
    val explosionTimeWindow: Long = 60 * 1000 // 1分钟内的呼叫爆炸检测窗口
) {
    /**
     * 检查是否启用了任何拦截功能
     */
    fun hasAnyBlockEnabled(): Boolean {
        return isBlockUnknown || isBlockSpecificNumbers || isBlockPrefix || 
               isBlockTodayCall || isBlockAll || isBlockCallExplosion
    }
    
    /**
     * 获取拦截策略描述
     */
    fun getBlockStrategyDescription(): String {
        val strategies = mutableListOf<String>()
        
        if (isBlockAll) {
            strategies.add("拦截所有来电")
        } else {
            if (isBlockUnknown) strategies.add("拦截未知号码")
            if (isBlockSpecificNumbers) strategies.add("拦截特定号码")
            if (isBlockPrefix) strategies.add("拦截前缀号码")
            if (isBlockTodayCall) strategies.add("限制今日通话($limitTodayCall 次)")
            if (isBlockCallExplosion) strategies.add("拦截呼叫爆炸($callExplosionCount 次)")
        }
        
        return if (strategies.isEmpty()) "未启用拦截" else strategies.joinToString("、")
    }
}
