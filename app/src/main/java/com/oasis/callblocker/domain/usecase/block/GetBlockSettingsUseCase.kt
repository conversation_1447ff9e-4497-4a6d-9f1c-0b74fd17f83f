package com.oasis.callblocker.domain.usecase.block

import com.oasis.callblocker.domain.model.BlockSettings
import com.oasis.callblocker.domain.repository.BlockRepository
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import javax.inject.Inject

/**
 * 获取拦截设置用例
 */
class GetBlockSettingsUseCase @Inject constructor(
    private val blockRepository: BlockRepository
) {
    /**
     * 获取拦截设置
     */
    suspend operator fun invoke(): BlockSettings {
        return blockRepository.getBlockSettings()
    }
    
    /**
     * 获取拦截设置流
     */
    fun asFlow(): Flow<BlockSettings> = flow {
        emit(blockRepository.getBlockSettings())
    }
}
