package com.oasis.callblocker.domain.repository

import com.oasis.callblocker.domain.model.PhoneSearchResult
import kotlinx.coroutines.flow.Flow

/**
 * 电话号码搜索仓库接口
 */
interface PhoneSearchRepository {
    
    // 号码搜索
    suspend fun searchPhoneNumber(phoneNumber: String): Result<PhoneSearchResult>
    suspend fun getCachedSearchResult(phoneNumber: String): PhoneSearchResult?
    
    // 搜索历史
    fun getSearchHistory(limit: Int = 50): Flow<List<PhoneSearchResult>>
    suspend fun addToSearchHistory(result: PhoneSearchResult)
    suspend fun clearSearchHistory()
    suspend fun deleteSearchResult(phoneNumber: String)
    
    // 缓存管理
    suspend fun clearExpiredCache()
    suspend fun updateCacheExpiration(phoneNumber: String, expireTime: Long)
    
    // 查询限制
    suspend fun getRemainQueryCount(): Int
    suspend fun updateQueryCount(count: Int)
    suspend fun canPerformSearch(): Boolean
}
