package com.oasis.callblocker.domain.model

/**
 * 用户领域模型
 */
data class User(
    val userId: String,
    val userEmail: String,
    val userCompany: String? = null,
    val deviceToken: String? = null,
    val authToken: String? = null,
    val licenseEndDate: String? = null,
    val remainQueryCount: Int = 0,
    val createdAt: Long = System.currentTimeMillis(),
    val updatedAt: Long = System.currentTimeMillis()
) {
    /**
     * 检查许可证是否过期
     */
    fun isLicenseExpired(): Boolean {
        if (licenseEndDate.isNullOrEmpty()) return true
        
        return try {
            val endDate = licenseEndDate.toLongOrNull() ?: return true
            System.currentTimeMillis() > endDate
        } catch (e: Exception) {
            true
        }
    }
    
    /**
     * 获取许可证剩余天数
     */
    fun getRemainingDays(): Int {
        if (licenseEndDate.isNullOrEmpty()) return 0
        
        return try {
            val endDate = licenseEndDate.toLongOrNull() ?: return 0
            val remainingMs = endDate - System.currentTimeMillis()
            if (remainingMs <= 0) 0 else (remainingMs / (24 * 60 * 60 * 1000)).toInt()
        } catch (e: Exception) {
            0
        }
    }
    
    /**
     * 检查是否可以进行查询
     */
    fun canPerformQuery(): Boolean {
        return remainQueryCount > 0 && !isLicenseExpired()
    }
}

/**
 * 登录凭据
 */
data class LoginCredentials(
    val email: String,
    val password: String,
    val deviceToken: String
)

/**
 * 登录结果
 */
data class LoginResult(
    val success: Boolean,
    val user: User? = null,
    val token: String? = null,
    val message: String? = null
)
