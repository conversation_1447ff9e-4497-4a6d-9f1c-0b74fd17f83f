package com.oasis.callblocker.domain.model

/**
 * 电话号码搜索结果领域模型
 */
data class PhoneSearchResult(
    val phoneNumber: String,
    val ownerName: String? = null,
    val location: String? = null,
    val carrier: String? = null, // 运营商
    val numberType: String? = null, // 号码类型（手机、固话等）
    val riskLevel: RiskLevel = RiskLevel.SAFE,
    val reportCount: Int = 0, // 举报次数
    val tags: List<String> = emptyList(), // 标签（如：骚扰电话、诈骗等）
    val lastSearchTime: Long = System.currentTimeMillis(),
    val cacheExpireTime: Long = System.currentTimeMillis() + 24 * 60 * 60 * 1000 // 缓存24小时
) {
    /**
     * 检查缓存是否过期
     */
    fun isCacheExpired(): Boolean {
        return System.currentTimeMillis() > cacheExpireTime
    }
    
    /**
     * 获取显示名称
     */
    fun getDisplayName(): String {
        return ownerName?.takeIf { it.isNotBlank() } ?: phoneNumber
    }
    
    /**
     * 获取位置信息
     */
    fun getLocationInfo(): String {
        val parts = mutableListOf<String>()
        location?.let { parts.add(it) }
        carrier?.let { parts.add(it) }
        numberType?.let { parts.add(it) }
        
        return parts.joinToString(" · ")
    }
    
    /**
     * 获取风险描述
     */
    fun getRiskDescription(): String {
        return when (riskLevel) {
            RiskLevel.SAFE -> "安全"
            RiskLevel.SUSPICIOUS -> "可疑"
            RiskLevel.DANGEROUS -> "危险"
        }
    }
    
    /**
     * 获取标签显示文本
     */
    fun getTagsText(): String {
        return tags.joinToString("、")
    }
    
    /**
     * 是否应该建议拦截
     */
    fun shouldSuggestBlock(): Boolean {
        return riskLevel == RiskLevel.DANGEROUS || 
               reportCount > 10 ||
               tags.any { it.contains("骚扰") || it.contains("诈骗") || it.contains("推销") }
    }
}

/**
 * 风险等级枚举
 */
enum class RiskLevel(val value: Int, val displayName: String, val color: String) {
    SAFE(0, "安全", "#4CAF50"),
    SUSPICIOUS(1, "可疑", "#FF9800"),
    DANGEROUS(2, "危险", "#F44336");
    
    companion object {
        fun fromValue(value: Int): RiskLevel {
            return values().find { it.value == value } ?: SAFE
        }
    }
}

/**
 * 搜索历史项
 */
data class SearchHistoryItem(
    val phoneNumber: String,
    val searchTime: Long,
    val result: PhoneSearchResult?
) {
    /**
     * 格式化搜索时间
     */
    fun getFormattedSearchTime(): String {
        val date = java.util.Date(searchTime)
        val format = java.text.SimpleDateFormat("MM-dd HH:mm", java.util.Locale.getDefault())
        return format.format(date)
    }
}
