package com.oasis.callblocker.domain.repository

import com.oasis.callblocker.domain.model.CallLog
import kotlinx.coroutines.flow.Flow

/**
 * 通话记录仓库接口
 */
interface CallLogRepository {
    
    // 通话记录查询
    fun getRecentCallLogs(limit: Int = 50): Flow<List<CallLog>>
    fun getCallLogsByType(callType: Int, limit: Int = 50): Flow<List<CallLog>>
    fun getBlockedCallLogs(limit: Int = 50): Flow<List<CallLog>>
    fun getCallLogsByNumber(phoneNumber: String): Flow<List<CallLog>>
    fun getCallLogsByTimeRange(startTime: Long, endTime: Long): Flow<List<CallLog>>
    
    // 通话记录管理
    suspend fun addCallLog(callLog: CallLog)
    suspend fun updateCallLog(callLog: CallLog)
    suspend fun deleteCallLog(id: Long)
    suspend fun clearOldCallLogs(beforeTime: Long)
    
    // 统计信息
    suspend fun getCallCountSince(phoneNumber: String, startTime: Long): Int
    suspend fun getTotalCallCount(): Int
    suspend fun getBlockedCallCount(): Int
    
    // 同步
    suspend fun syncWithSystemCallLog()
}
