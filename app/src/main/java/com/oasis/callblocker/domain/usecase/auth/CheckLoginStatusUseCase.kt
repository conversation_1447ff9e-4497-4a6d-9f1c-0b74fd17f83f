package com.oasis.callblocker.domain.usecase.auth

import com.oasis.callblocker.domain.repository.UserRepository
import javax.inject.Inject

/**
 * 检查登录状态用例
 */
class CheckLoginStatusUseCase @Inject constructor(
    private val userRepository: UserRepository
) {
    /**
     * 检查用户是否已登录
     */
    suspend operator fun invoke(): <PERSON><PERSON><PERSON> {
        return userRepository.isLoggedIn()
    }
}
