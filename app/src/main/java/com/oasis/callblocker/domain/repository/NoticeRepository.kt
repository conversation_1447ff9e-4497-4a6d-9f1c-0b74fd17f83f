package com.oasis.callblocker.domain.repository

import com.oasis.callblocker.domain.model.Notice
import kotlinx.coroutines.flow.Flow

/**
 * 通知仓库接口
 */
interface NoticeRepository {
    
    // 通知查询
    fun getAllNotices(): Flow<List<Notice>>
    fun getUnreadNotices(): Flow<List<Notice>>
    fun getNoticesByType(noticeType: Int): Flow<List<Notice>>
    fun getUnreadNoticeCount(): Flow<Int>
    suspend fun getNotice(noticeId: String): Notice?
    
    // 通知管理
    suspend fun addNotice(notice: Notice)
    suspend fun updateNotice(notice: Notice)
    suspend fun markAsRead(noticeId: String)
    suspend fun markAllAsRead()
    suspend fun deleteNotice(noticeId: String)
    suspend fun clearOldNotices(beforeTime: Long)
    
    // 同步
    suspend fun syncNoticesFromServer(): Result<List<Notice>>
    suspend fun checkNewNotices(): Result<Boolean>
    suspend fun getLastCheckTime(): Long
    suspend fun updateLastCheckTime(time: Long)
}
