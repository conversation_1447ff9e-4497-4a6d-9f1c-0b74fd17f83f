package com.oasis.callblocker.domain.repository

import com.oasis.callblocker.domain.model.User
import com.oasis.callblocker.domain.model.LoginCredentials
import kotlinx.coroutines.flow.Flow

/**
 * 用户仓库接口
 */
interface UserRepository {
    
    // 用户认证
    suspend fun login(credentials: LoginCredentials): Result<User>
    suspend fun logout()
    suspend fun refreshToken(): Result<String>
    
    // 用户信息
    fun getCurrentUser(): Flow<User?>
    suspend fun updateUser(user: User)
    suspend fun updateQueryCount(count: Int)
    
    // 认证状态
    suspend fun isLoggedIn(): Boolean
    suspend fun getAuthToken(): String?
    suspend fun saveAuthToken(token: String)
    suspend fun clearAuthData()
    
    // 设备管理
    suspend fun getDeviceToken(): String
    suspend fun registerDevice(): Result<String>
}
