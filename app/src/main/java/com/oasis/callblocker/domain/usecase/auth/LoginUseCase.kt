package com.oasis.callblocker.domain.usecase.auth

import com.oasis.callblocker.domain.model.LoginCredentials
import com.oasis.callblocker.domain.model.User
import com.oasis.callblocker.domain.repository.UserRepository
import javax.inject.Inject

/**
 * 用户登录用例
 */
class LoginUseCase @Inject constructor(
    private val userRepository: UserRepository
) {
    /**
     * 执行登录
     * @param email 用户邮箱
     * @param password 用户密码
     * @return 登录结果
     */
    suspend operator fun invoke(email: String, password: String): Result<User> {
        // 验证输入参数
        if (email.isBlank()) {
            return Result.failure(IllegalArgumentException("邮箱不能为空"))
        }
        
        if (password.isBlank()) {
            return Result.failure(IllegalArgumentException("密码不能为空"))
        }
        
        if (!isValidEmail(email)) {
            return Result.failure(IllegalArgumentException("邮箱格式不正确"))
        }
        
        // 获取设备令牌
        val deviceToken = userRepository.getDeviceToken()
        
        // 创建登录凭据
        val credentials = LoginCredentials(
            email = email.trim(),
            password = password,
            deviceToken = deviceToken
        )
        
        // 执行登录
        return userRepository.login(credentials)
    }
    
    /**
     * 验证邮箱格式
     */
    private fun isValidEmail(email: String): Boolean {
        return android.util.Patterns.EMAIL_ADDRESS.matcher(email).matches()
    }
}
