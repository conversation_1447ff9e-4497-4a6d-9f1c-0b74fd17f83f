package com.oasis.callblocker.domain.usecase.block

import com.oasis.callblocker.domain.model.CallLog
import com.oasis.callblocker.domain.model.CallType
import com.oasis.callblocker.domain.repository.BlockRepository
import com.oasis.callblocker.domain.repository.CallLogRepository
import javax.inject.Inject

/**
 * 处理来电用例
 */
class ProcessIncomingCallUseCase @Inject constructor(
    private val blockRepository: BlockRepository,
    private val callLogRepository: CallLogRepository
) {
    /**
     * 处理来电
     * @param phoneNumber 来电号码
     * @param contactName 联系人姓名（如果有）
     * @return 是否应该拦截此来电
     */
    suspend operator fun invoke(phoneNumber: String, contactName: String? = null): Boolean {
        // 格式化电话号码
        val formattedNumber = formatPhoneNumber(phoneNumber)
        
        // 检查是否应该拦截
        val shouldBlock = blockRepository.processIncomingCall(formattedNumber, contactName)
        
        // 记录通话日志
        val callLog = CallLog(
            phoneNumber = formattedNumber,
            contactName = contactName,
            callType = CallType.INCOMING,
            isBlocked = shouldBlock
        )
        callLogRepository.addCallLog(callLog)
        
        return shouldBlock
    }
    
    /**
     * 格式化电话号码
     */
    private fun formatPhoneNumber(phoneNumber: String): String {
        // 移除所有非数字字符
        var formatted = phoneNumber.replace(Regex("[^0-9]"), "")
        
        // 处理国际号码格式
        if (formatted.startsWith("86") && formatted.length > 11) {
            formatted = formatted.substring(2)
        }
        
        return formatted
    }
}
