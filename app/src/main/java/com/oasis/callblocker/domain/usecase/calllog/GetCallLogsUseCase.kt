package com.oasis.callblocker.domain.usecase.calllog

import com.oasis.callblocker.domain.model.CallLog
import com.oasis.callblocker.domain.model.CallStatistics
import com.oasis.callblocker.domain.model.CallType
import com.oasis.callblocker.domain.repository.CallLogRepository
import kotlinx.coroutines.flow.Flow
import javax.inject.Inject

/**
 * 获取通话记录用例
 */
class GetCallLogsUseCase @Inject constructor(
    private val callLogRepository: CallLogRepository
) {
    /**
     * 获取最近通话记录
     */
    fun getRecentCallLogs(limit: Int = 50): Flow<List<CallLog>> {
        return callLogRepository.getRecentCallLogs(limit)
    }
    
    /**
     * 根据类型获取通话记录
     */
    fun getCallLogsByType(callType: CallType, limit: Int = 50): Flow<List<CallLog>> {
        return callLogRepository.getCallLogsByType(callType.value, limit)
    }
    
    /**
     * 获取被拦截的通话记录
     */
    fun getBlockedCallLogs(limit: Int = 50): Flow<List<CallLog>> {
        return callLogRepository.getBlockedCallLogs(limit)
    }
    
    /**
     * 根据号码获取通话记录
     */
    fun getCallLogsByNumber(phoneNumber: String): Flow<List<CallLog>> {
        return callLogRepository.getCallLogsByNumber(phoneNumber)
    }
    
    /**
     * 根据时间范围获取通话记录
     */
    fun getCallLogsByTimeRange(startTime: Long, endTime: Long): Flow<List<CallLog>> {
        return callLogRepository.getCallLogsByTimeRange(startTime, endTime)
    }
    
    /**
     * 获取通话统计信息
     */
    suspend fun getCallStatistics(): CallStatistics {
        val totalCalls = callLogRepository.getTotalCallCount()
        val blockedCalls = callLogRepository.getBlockedCallCount()
        
        // 获取今日开始时间
        val todayStart = getTodayStartTime()
        
        return CallStatistics(
            totalCalls = totalCalls,
            blockedCalls = blockedCalls,
            todayBlockedCount = 0, // 需要实现具体逻辑
            weeklyBlockedCount = 0, // 需要实现具体逻辑
            monthlyBlockedCount = 0 // 需要实现具体逻辑
        )
    }
    
    /**
     * 同步系统通话记录
     */
    suspend fun syncWithSystemCallLog() {
        callLogRepository.syncWithSystemCallLog()
    }
    
    /**
     * 清理旧的通话记录
     */
    suspend fun clearOldCallLogs(daysToKeep: Int = 30) {
        val cutoffTime = System.currentTimeMillis() - (daysToKeep * 24 * 60 * 60 * 1000L)
        callLogRepository.clearOldCallLogs(cutoffTime)
    }
    
    private fun getTodayStartTime(): Long {
        val calendar = java.util.Calendar.getInstance()
        calendar.set(java.util.Calendar.HOUR_OF_DAY, 0)
        calendar.set(java.util.Calendar.MINUTE, 0)
        calendar.set(java.util.Calendar.SECOND, 0)
        calendar.set(java.util.Calendar.MILLISECOND, 0)
        return calendar.timeInMillis
    }
}
