package com.oasis.callblocker.domain.usecase.notice

import com.oasis.callblocker.domain.model.Notice
import com.oasis.callblocker.domain.model.NoticeStatistics
import com.oasis.callblocker.domain.model.NoticeType
import com.oasis.callblocker.domain.repository.NoticeRepository
import kotlinx.coroutines.flow.Flow
import javax.inject.Inject

/**
 * 获取通知用例
 */
class GetNoticesUseCase @Inject constructor(
    private val noticeRepository: NoticeRepository
) {
    /**
     * 获取所有通知
     */
    fun getAllNotices(): Flow<List<Notice>> {
        return noticeRepository.getAllNotices()
    }
    
    /**
     * 获取未读通知
     */
    fun getUnreadNotices(): Flow<List<Notice>> {
        return noticeRepository.getUnreadNotices()
    }
    
    /**
     * 根据类型获取通知
     */
    fun getNoticesByType(noticeType: NoticeType): Flow<List<Notice>> {
        return noticeRepository.getNoticesByType(noticeType.value)
    }
    
    /**
     * 获取未读通知数量
     */
    fun getUnreadNoticeCount(): Flow<Int> {
        return noticeRepository.getUnreadNoticeCount()
    }
    
    /**
     * 获取指定通知
     */
    suspend fun getNotice(noticeId: String): Notice? {
        return noticeRepository.getNotice(noticeId)
    }
    
    /**
     * 标记通知为已读
     */
    suspend fun markAsRead(noticeId: String) {
        noticeRepository.markAsRead(noticeId)
    }
    
    /**
     * 标记所有通知为已读
     */
    suspend fun markAllAsRead() {
        noticeRepository.markAllAsRead()
    }
    
    /**
     * 删除通知
     */
    suspend fun deleteNotice(noticeId: String) {
        noticeRepository.deleteNotice(noticeId)
    }
    
    /**
     * 同步服务器通知
     */
    suspend fun syncNoticesFromServer(): Result<List<Notice>> {
        return noticeRepository.syncNoticesFromServer()
    }
    
    /**
     * 检查新通知
     */
    suspend fun checkNewNotices(): Result<Boolean> {
        return noticeRepository.checkNewNotices()
    }
    
    /**
     * 获取通知统计信息
     */
    suspend fun getNoticeStatistics(): NoticeStatistics {
        // 这里需要实现具体的统计逻辑
        // 目前返回基本统计信息
        return NoticeStatistics(
            totalNotices = 0,
            unreadNotices = 0,
            normalNotices = 0,
            importantNotices = 0,
            urgentNotices = 0,
            newNotices = 0,
            lastCheckTime = noticeRepository.getLastCheckTime()
        )
    }
    
    /**
     * 清理旧通知
     */
    suspend fun clearOldNotices(daysToKeep: Int = 30) {
        val cutoffTime = System.currentTimeMillis() - (daysToKeep * 24 * 60 * 60 * 1000L)
        noticeRepository.clearOldNotices(cutoffTime)
    }
}
