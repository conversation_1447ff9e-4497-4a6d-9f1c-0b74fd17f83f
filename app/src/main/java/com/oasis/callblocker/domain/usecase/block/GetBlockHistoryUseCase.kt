package com.oasis.callblocker.domain.usecase.block

import com.oasis.callblocker.domain.model.BlockHistory
import com.oasis.callblocker.domain.model.BlockHistoryType
import com.oasis.callblocker.domain.repository.BlockRepository
import kotlinx.coroutines.flow.Flow
import javax.inject.Inject

/**
 * 获取拦截历史用例
 */
class GetBlockHistoryUseCase @Inject constructor(
    private val blockRepository: BlockRepository
) {
    /**
     * 获取所有拦截历史
     */
    fun getAllBlockHistory(): Flow<List<BlockHistory>> {
        return blockRepository.getAllBlockHistory()
    }
    
    /**
     * 根据类型获取拦截历史
     */
    fun getBlockHistoryByType(blockType: BlockHistoryType): Flow<List<BlockHistory>> {
        return blockRepository.getBlockHistoryByType(blockType.value)
    }
    
    /**
     * 根据号码获取拦截历史
     */
    fun getBlockHistoryByNumber(phoneNumber: String): Flow<List<BlockHistory>> {
        return blockRepository.getBlockHistoryByNumber(phoneNumber)
    }
    
    /**
     * 获取指定时间段内的拦截统计
     */
    suspend fun getBlockStatistics(startTime: Long, endTime: Long): BlockStatistics {
        // 这里可以添加更复杂的统计逻辑
        // 目前返回基本统计信息
        return BlockStatistics(
            totalBlocked = 0, // 需要从repository获取
            todayBlocked = 0,
            weeklyBlocked = 0,
            monthlyBlocked = 0
        )
    }
}

/**
 * 拦截统计信息
 */
data class BlockStatistics(
    val totalBlocked: Int,
    val todayBlocked: Int,
    val weeklyBlocked: Int,
    val monthlyBlocked: Int
)
