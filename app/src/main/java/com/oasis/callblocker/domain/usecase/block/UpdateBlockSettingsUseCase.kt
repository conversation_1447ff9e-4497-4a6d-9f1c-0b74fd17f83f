package com.oasis.callblocker.domain.usecase.block

import com.oasis.callblocker.domain.model.BlockSettings
import com.oasis.callblocker.domain.repository.BlockRepository
import javax.inject.Inject

/**
 * 更新拦截设置用例
 */
class UpdateBlockSettingsUseCase @Inject constructor(
    private val blockRepository: BlockRepository
) {
    /**
     * 更新拦截设置
     * @param settings 新的拦截设置
     */
    suspend operator fun invoke(settings: BlockSettings) {
        // 验证设置的合理性
        validateSettings(settings)
        
        // 更新设置
        blockRepository.updateBlockSettings(settings)
    }
    
    /**
     * 验证设置的合理性
     */
    private fun validateSettings(settings: BlockSettings) {
        if (settings.limitTodayCall < 0) {
            throw IllegalArgumentException("今日通话限制不能为负数")
        }
        
        if (settings.callExplosionCount < 1) {
            throw IllegalArgumentException("呼叫爆炸检测次数不能小于1")
        }
        
        if (settings.explosionTimeWindow < 1000) {
            throw IllegalArgumentException("呼叫爆炸检测时间窗口不能小于1秒")
        }
    }
}
