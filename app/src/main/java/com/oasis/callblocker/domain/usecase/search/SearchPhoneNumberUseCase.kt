package com.oasis.callblocker.domain.usecase.search

import com.oasis.callblocker.domain.model.PhoneSearchResult
import com.oasis.callblocker.domain.repository.PhoneSearchRepository
import com.oasis.callblocker.domain.repository.UserRepository
import javax.inject.Inject

/**
 * 搜索电话号码用例
 */
class SearchPhoneNumberUseCase @Inject constructor(
    private val phoneSearchRepository: PhoneSearchRepository,
    private val userRepository: UserRepository
) {
    /**
     * 搜索电话号码
     * @param phoneNumber 要搜索的电话号码
     * @return 搜索结果
     */
    suspend operator fun invoke(phoneNumber: String): Result<PhoneSearchResult> {
        // 验证输入
        val formattedNumber = validateAndFormatPhoneNumber(phoneNumber)
        
        // 检查是否可以进行搜索
        if (!phoneSearchRepository.canPerformSearch()) {
            return Result.failure(Exception("查询次数已用完或许可证已过期"))
        }
        
        // 先检查缓存
        val cachedResult = phoneSearchRepository.getCachedSearchResult(formattedNumber)
        if (cachedResult != null && !cachedResult.isCacheExpired()) {
            // 更新搜索时间
            phoneSearchRepository.addToSearchHistory(cachedResult.copy(
                lastSearchTime = System.currentTimeMillis()
            ))
            return Result.success(cachedResult)
        }
        
        // 从服务器搜索
        return phoneSearchRepository.searchPhoneNumber(formattedNumber)
    }
    
    /**
     * 验证并格式化电话号码
     */
    private fun validateAndFormatPhoneNumber(phoneNumber: String): String {
        if (phoneNumber.isBlank()) {
            throw IllegalArgumentException("电话号码不能为空")
        }
        
        // 移除所有非数字字符
        var formatted = phoneNumber.replace(Regex("[^0-9]"), "")
        
        // 处理国际号码格式
        if (formatted.startsWith("86") && formatted.length > 11) {
            formatted = formatted.substring(2)
        }
        
        // 验证号码长度
        if (formatted.length < 7 || formatted.length > 11) {
            throw IllegalArgumentException("电话号码格式不正确")
        }
        
        return formatted
    }
}
