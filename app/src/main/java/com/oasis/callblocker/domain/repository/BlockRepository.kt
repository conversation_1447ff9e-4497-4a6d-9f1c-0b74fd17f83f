package com.oasis.callblocker.domain.repository

import com.oasis.callblocker.domain.model.BlockNumber
import com.oasis.callblocker.domain.model.BlockHistory
import com.oasis.callblocker.domain.model.BlockSettings
import kotlinx.coroutines.flow.Flow

/**
 * 拦截功能仓库接口
 */
interface BlockRepository {
    
    // 拦截号码管理
    fun getAllBlockNumbers(): Flow<List<BlockNumber>>
    fun getBlockNumbersByType(blockType: Int): Flow<List<BlockNumber>>
    suspend fun getBlockNumber(phoneNumber: String): BlockNumber?
    suspend fun addBlockNumber(blockNumber: BlockNumber)
    suspend fun removeBlockNumber(phoneNumber: String)
    suspend fun updateBlockNumber(blockNumber: BlockNumber)
    
    // 拦截历史
    fun getAllBlockHistory(): Flow<List<BlockHistory>>
    fun getBlockHistoryByType(blockType: Int): Flow<List<BlockHistory>>
    fun getBlockHistoryByNumber(phoneNumber: String): Flow<List<BlockHistory>>
    suspend fun addBlockHistory(blockHistory: BlockHistory)
    suspend fun getBlockCountSince(phoneNumber: String, startTime: Long): Int
    suspend fun clearOldBlockHistory(beforeTime: Long)
    
    // 拦截设置
    suspend fun getBlockSettings(): BlockSettings
    suspend fun updateBlockSettings(settings: BlockSettings)
    
    // 拦截逻辑
    suspend fun shouldBlockCall(phoneNumber: String): Boolean
    suspend fun processIncomingCall(phoneNumber: String, contactName: String?): Boolean
    suspend fun resetDailyCounters()
}
