package com.oasis.callblocker.domain.usecase.auth

import com.oasis.callblocker.domain.model.User
import com.oasis.callblocker.domain.repository.UserRepository
import kotlinx.coroutines.flow.Flow
import javax.inject.Inject

/**
 * 获取当前用户用例
 */
class GetCurrentUserUseCase @Inject constructor(
    private val userRepository: UserRepository
) {
    /**
     * 获取当前用户信息流
     */
    operator fun invoke(): Flow<User?> {
        return userRepository.getCurrentUser()
    }
}
