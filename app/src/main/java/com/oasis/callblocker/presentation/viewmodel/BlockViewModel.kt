package com.oasis.callblocker.presentation.viewmodel

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.oasis.callblocker.domain.model.*
import com.oasis.callblocker.domain.usecase.block.*
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * 拦截管理ViewModel
 */
@HiltViewModel
class BlockViewModel @Inject constructor(
    private val getBlockSettingsUseCase: GetBlockSettingsUseCase,
    private val updateBlockSettingsUseCase: UpdateBlockSettingsUseCase,
    private val manageBlockNumbersUseCase: ManageBlockNumbersUseCase,
    private val getBlockHistoryUseCase: GetBlockHistoryUseCase
) : ViewModel() {
    
    private val _uiState = MutableStateFlow(BlockUiState())
    val uiState: StateFlow<BlockUiState> = _uiState.asStateFlow()
    
    init {
        loadData()
    }
    
    /**
     * 加载数据
     */
    private fun loadData() {
        viewModelScope.launch {
            _uiState.value = _uiState.value.copy(isLoading = true)
            
            try {
                // 组合多个数据流
                combine(
                    manageBlockNumbersUseCase.getAllBlockNumbers(),
                    getBlockHistoryUseCase.getAllBlockHistory()
                ) { blockNumbers, blockHistory ->
                    Pair(blockNumbers, blockHistory)
                }.collect { (blockNumbers, blockHistory) ->
                    _uiState.value = _uiState.value.copy(
                        isLoading = false,
                        blockNumbers = blockNumbers,
                        blockHistory = blockHistory.take(50) // 限制历史记录数量
                    )
                }
                
                // 加载设置
                val settings = getBlockSettingsUseCase()
                _uiState.value = _uiState.value.copy(blockSettings = settings)
                
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    errorMessage = e.message ?: "加载数据失败"
                )
            }
        }
    }
    
    /**
     * 更新拦截设置
     */
    fun updateBlockSettings(settings: BlockSettings) {
        viewModelScope.launch {
            try {
                updateBlockSettingsUseCase(settings)
                _uiState.value = _uiState.value.copy(
                    blockSettings = settings,
                    successMessage = "设置已保存"
                )
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    errorMessage = e.message ?: "保存设置失败"
                )
            }
        }
    }
    
    /**
     * 添加拦截号码
     */
    fun addBlockNumber(phoneNumber: String, blockType: BlockType) {
        viewModelScope.launch {
            try {
                manageBlockNumbersUseCase.addBlockNumber(phoneNumber, blockType)
                _uiState.value = _uiState.value.copy(
                    successMessage = "号码已添加到拦截列表"
                )
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    errorMessage = e.message ?: "添加号码失败"
                )
            }
        }
    }
    
    /**
     * 移除拦截号码
     */
    fun removeBlockNumber(phoneNumber: String) {
        viewModelScope.launch {
            try {
                manageBlockNumbersUseCase.removeBlockNumber(phoneNumber)
                _uiState.value = _uiState.value.copy(
                    successMessage = "号码已从拦截列表移除"
                )
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    errorMessage = e.message ?: "移除号码失败"
                )
            }
        }
    }
    
    /**
     * 批量添加拦截号码
     */
    fun addBlockNumbers(phoneNumbers: List<String>, blockType: BlockType) {
        viewModelScope.launch {
            try {
                manageBlockNumbersUseCase.addBlockNumbers(phoneNumbers, blockType)
                _uiState.value = _uiState.value.copy(
                    successMessage = "已添加 ${phoneNumbers.size} 个号码到拦截列表"
                )
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    errorMessage = e.message ?: "批量添加失败"
                )
            }
        }
    }
    
    /**
     * 根据类型获取拦截号码
     */
    fun getBlockNumbersByType(blockType: BlockType) {
        viewModelScope.launch {
            manageBlockNumbersUseCase.getBlockNumbersByType(blockType).collect { numbers ->
                _uiState.value = _uiState.value.copy(
                    filteredBlockNumbers = numbers
                )
            }
        }
    }
    
    /**
     * 清除消息
     */
    fun clearMessages() {
        _uiState.value = _uiState.value.copy(
            errorMessage = null,
            successMessage = null
        )
    }
    
    /**
     * 切换选项卡
     */
    fun switchTab(tab: BlockTab) {
        _uiState.value = _uiState.value.copy(currentTab = tab)
    }
}

/**
 * 拦截管理UI状态
 */
data class BlockUiState(
    val isLoading: Boolean = false,
    val blockSettings: BlockSettings = BlockSettings(),
    val blockNumbers: List<BlockNumber> = emptyList(),
    val filteredBlockNumbers: List<BlockNumber> = emptyList(),
    val blockHistory: List<BlockHistory> = emptyList(),
    val currentTab: BlockTab = BlockTab.SETTINGS,
    val errorMessage: String? = null,
    val successMessage: String? = null
) {
    /**
     * 根据类型获取拦截号码数量
     */
    fun getBlockNumberCountByType(blockType: BlockType): Int {
        return blockNumbers.count { it.blockType == blockType }
    }
    
    /**
     * 获取今日拦截数量
     */
    fun getTodayBlockCount(): Int {
        val todayStart = getTodayStartTime()
        return blockHistory.count { it.timestamp >= todayStart }
    }
    
    /**
     * 获取拦截策略摘要
     */
    fun getBlockStrategySummary(): String {
        return blockSettings.getBlockStrategyDescription()
    }
    
    private fun getTodayStartTime(): Long {
        val calendar = java.util.Calendar.getInstance()
        calendar.set(java.util.Calendar.HOUR_OF_DAY, 0)
        calendar.set(java.util.Calendar.MINUTE, 0)
        calendar.set(java.util.Calendar.SECOND, 0)
        calendar.set(java.util.Calendar.MILLISECOND, 0)
        return calendar.timeInMillis
    }
}

/**
 * 拦截管理选项卡
 */
enum class BlockTab(val displayName: String) {
    SETTINGS("拦截设置"),
    NUMBERS("拦截号码"),
    HISTORY("拦截历史")
}
