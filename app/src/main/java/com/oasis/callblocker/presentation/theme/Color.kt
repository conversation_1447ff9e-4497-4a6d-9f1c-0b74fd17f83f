package com.oasis.callblocker.presentation.theme

import androidx.compose.ui.graphics.Color

val Purple80 = Color(0xFFD0BCFF)
val PurpleGrey80 = Color(0xFFCCC2DC)
val Pink80 = Color(0xFFEFB8C8)

val Purple40 = Color(0xFF6650a4)
val PurpleGrey40 = Color(0xFF625b71)
val Pink40 = Color(0xFF7D5260)

// OASIS 品牌色彩
val OasisPrimary = Color(0xFF2196F3)
val OasisPrimaryVariant = Color(0xFF1976D2)
val OasisSecondary = Color(0xFF03DAC6)
val OasisSecondaryVariant = Color(0xFF018786)

// 状态色彩
val SuccessGreen = Color(0xFF4CAF50)
val WarningOrange = Color(0xFFFF9800)
val ErrorRed = Color(0xFFF44336)
val InfoBlue = Color(0xFF2196F3)

// 拦截相关色彩
val BlockedRed = Color(0xFFE53935)
val AllowedGreen = Color(0xFF43A047)
val SuspiciousOrange = Color(0xFFFF7043)

// 背景色彩
val BackgroundLight = Color(0xFFFAFAFA)
val BackgroundDark = Color(0xFF121212)
val SurfaceLight = Color(0xFFFFFFFF)
val SurfaceDark = Color(0xFF1E1E1E)
