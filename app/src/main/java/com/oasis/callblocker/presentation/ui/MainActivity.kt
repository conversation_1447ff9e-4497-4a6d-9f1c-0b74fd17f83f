package com.oasis.callblocker.presentation.ui

import android.Manifest
import android.content.Intent
import android.content.pm.PackageManager
import android.net.Uri
import android.os.Build
import android.os.Bundle
import android.provider.Settings
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.activity.result.contract.ActivityResultContracts
import androidx.activity.viewModels
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Surface
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.ui.Modifier
import androidx.core.content.ContextCompat
import androidx.navigation.compose.NavHost
import androidx.navigation.compose.composable
import androidx.navigation.compose.rememberNavController
import com.oasis.callblocker.presentation.theme.OasisTheme
import com.oasis.callblocker.presentation.ui.login.LoginScreen
import com.oasis.callblocker.presentation.ui.main.MainScreen
import com.oasis.callblocker.presentation.viewmodel.LoginViewModel
import dagger.hilt.android.AndroidEntryPoint

/**
 * 主Activity - 使用Single Activity架构
 */
@AndroidEntryPoint
class MainActivity : ComponentActivity() {
    
    private val loginViewModel: LoginViewModel by viewModels()
    
    // 权限请求启动器
    private val permissionLauncher = registerForActivityResult(
        ActivityResultContracts.RequestMultiplePermissions()
    ) { permissions ->
        val allGranted = permissions.values.all { it }
        if (!allGranted) {
            // 处理权限被拒绝的情况
            showPermissionDeniedDialog()
        }
    }
    
    // 悬浮窗权限请求启动器
    private val overlayPermissionLauncher = registerForActivityResult(
        ActivityResultContracts.StartActivityForResult()
    ) { result ->
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            if (!Settings.canDrawOverlays(this)) {
                // 悬浮窗权限被拒绝
                showOverlayPermissionDeniedDialog()
            }
        }
    }
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        // 请求必要权限
        requestPermissions()
        
        setContent {
            OasisTheme {
                Surface(
                    modifier = Modifier.fillMaxSize(),
                    color = MaterialTheme.colorScheme.background
                ) {
                    OasisApp()
                }
            }
        }
    }
    
    @Composable
    private fun OasisApp() {
        val navController = rememberNavController()
        val loginUiState by loginViewModel.uiState.collectAsState()
        
        // 根据登录状态决定起始页面
        val startDestination = if (loginUiState.isLoggedIn) "main" else "login"
        
        NavHost(
            navController = navController,
            startDestination = startDestination
        ) {
            composable("login") {
                LoginScreen(
                    onLoginSuccess = {
                        navController.navigate("main") {
                            popUpTo("login") { inclusive = true }
                        }
                    }
                )
            }
            
            composable("main") {
                MainScreen(
                    onLogout = {
                        navController.navigate("login") {
                            popUpTo("main") { inclusive = true }
                        }
                    }
                )
            }
        }
        
        // 监听登录状态变化
        LaunchedEffect(loginUiState.isLoggedIn) {
            if (loginUiState.isLoggedIn && navController.currentDestination?.route == "login") {
                navController.navigate("main") {
                    popUpTo("login") { inclusive = true }
                }
            }
        }
    }
    
    /**
     * 请求必要权限
     */
    private fun requestPermissions() {
        val permissions = mutableListOf<String>().apply {
            // 通话相关权限
            add(Manifest.permission.READ_PHONE_STATE)
            add(Manifest.permission.CALL_PHONE)
            add(Manifest.permission.READ_CALL_LOG)
            add(Manifest.permission.WRITE_CALL_LOG)
            
            // 联系人权限
            add(Manifest.permission.READ_CONTACTS)
            add(Manifest.permission.WRITE_CONTACTS)
            
            // 短信权限
            add(Manifest.permission.READ_SMS)
            add(Manifest.permission.RECEIVE_SMS)
            
            // 存储权限
            if (Build.VERSION.SDK_INT <= Build.VERSION_CODES.S_V2) {
                add(Manifest.permission.READ_EXTERNAL_STORAGE)
                add(Manifest.permission.WRITE_EXTERNAL_STORAGE)
            }
            
            // Android 8.0+ 需要的权限
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                add(Manifest.permission.ANSWER_PHONE_CALLS)
            }
            
            // Android 10+ 需要的权限
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
                add(Manifest.permission.READ_PHONE_NUMBERS)
            }
        }
        
        // 检查哪些权限还未授予
        val permissionsToRequest = permissions.filter { permission ->
            ContextCompat.checkSelfPermission(this, permission) != PackageManager.PERMISSION_GRANTED
        }
        
        if (permissionsToRequest.isNotEmpty()) {
            permissionLauncher.launch(permissionsToRequest.toTypedArray())
        }
        
        // 请求悬浮窗权限
        requestOverlayPermission()
    }
    
    /**
     * 请求悬浮窗权限
     */
    private fun requestOverlayPermission() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            if (!Settings.canDrawOverlays(this)) {
                val intent = Intent(
                    Settings.ACTION_MANAGE_OVERLAY_PERMISSION,
                    Uri.parse("package:$packageName")
                )
                overlayPermissionLauncher.launch(intent)
            }
        }
    }
    
    /**
     * 显示权限被拒绝对话框
     */
    private fun showPermissionDeniedDialog() {
        // 这里可以显示一个对话框解释为什么需要这些权限
        // 并引导用户到设置页面手动授权
    }
    
    /**
     * 显示悬浮窗权限被拒绝对话框
     */
    private fun showOverlayPermissionDeniedDialog() {
        // 这里可以显示一个对话框解释为什么需要悬浮窗权限
    }
}
