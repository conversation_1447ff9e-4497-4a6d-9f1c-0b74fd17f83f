package com.oasis.callblocker.presentation.viewmodel

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.oasis.callblocker.domain.model.CallLog
import com.oasis.callblocker.domain.model.CallStatistics
import com.oasis.callblocker.domain.model.User
import com.oasis.callblocker.domain.usecase.auth.GetCurrentUserUseCase
import com.oasis.callblocker.domain.usecase.calllog.GetCallLogsUseCase
import com.oasis.callblocker.domain.usecase.notice.GetNoticesUseCase
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * 主页ViewModel
 */
@HiltViewModel
class HomeViewModel @Inject constructor(
    private val getCurrentUserUseCase: GetCurrentUserUseCase,
    private val getCallLogsUseCase: GetCallLogsUseCase,
    private val getNoticesUseCase: GetNoticesUseCase
) : ViewModel() {
    
    private val _uiState = MutableStateFlow(HomeUiState())
    val uiState: StateFlow<HomeUiState> = _uiState.asStateFlow()
    
    init {
        loadData()
    }
    
    /**
     * 加载数据
     */
    private fun loadData() {
        viewModelScope.launch {
            _uiState.value = _uiState.value.copy(isLoading = true)
            
            try {
                // 组合多个数据流
                combine(
                    getCurrentUserUseCase(),
                    getCallLogsUseCase.getRecentCallLogs(20),
                    getNoticesUseCase.getUnreadNoticeCount()
                ) { user, callLogs, unreadCount ->
                    Triple(user, callLogs, unreadCount)
                }.collect { (user, callLogs, unreadCount) ->
                    _uiState.value = _uiState.value.copy(
                        isLoading = false,
                        user = user,
                        recentCallLogs = callLogs,
                        unreadNoticeCount = unreadCount
                    )
                }
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    errorMessage = e.message ?: "加载数据失败"
                )
            }
        }
    }
    
    /**
     * 刷新数据
     */
    fun refresh() {
        viewModelScope.launch {
            try {
                // 同步系统通话记录
                getCallLogsUseCase.syncWithSystemCallLog()
                
                // 检查新通知
                getNoticesUseCase.checkNewNotices()
                
                // 获取统计信息
                val statistics = getCallLogsUseCase.getCallStatistics()
                _uiState.value = _uiState.value.copy(
                    callStatistics = statistics,
                    lastRefreshTime = System.currentTimeMillis()
                )
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    errorMessage = e.message ?: "刷新失败"
                )
            }
        }
    }
    
    /**
     * 清除错误消息
     */
    fun clearError() {
        _uiState.value = _uiState.value.copy(errorMessage = null)
    }
    
    /**
     * 获取通话统计
     */
    fun loadCallStatistics() {
        viewModelScope.launch {
            try {
                val statistics = getCallLogsUseCase.getCallStatistics()
                _uiState.value = _uiState.value.copy(callStatistics = statistics)
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    errorMessage = e.message ?: "获取统计信息失败"
                )
            }
        }
    }
}

/**
 * 主页UI状态
 */
data class HomeUiState(
    val isLoading: Boolean = false,
    val user: User? = null,
    val recentCallLogs: List<CallLog> = emptyList(),
    val callStatistics: CallStatistics? = null,
    val unreadNoticeCount: Int = 0,
    val lastRefreshTime: Long = 0,
    val errorMessage: String? = null
) {
    /**
     * 获取欢迎消息
     */
    fun getWelcomeMessage(): String {
        val hour = java.util.Calendar.getInstance().get(java.util.Calendar.HOUR_OF_DAY)
        val greeting = when (hour) {
            in 6..11 -> "早上好"
            in 12..17 -> "下午好"
            in 18..23 -> "晚上好"
            else -> "夜深了"
        }
        
        val userName = user?.userCompany?.takeIf { it.isNotBlank() } ?: "用户"
        return "$greeting，$userName"
    }
    
    /**
     * 是否有新通知
     */
    fun hasNewNotices(): Boolean = unreadNoticeCount > 0
    
    /**
     * 获取许可证状态描述
     */
    fun getLicenseStatusDescription(): String {
        return user?.let { user ->
            if (user.isLicenseExpired()) {
                "许可证已过期"
            } else {
                val remainingDays = user.getRemainingDays()
                when {
                    remainingDays <= 7 -> "许可证将在 $remainingDays 天后过期"
                    remainingDays <= 30 -> "许可证剩余 $remainingDays 天"
                    else -> "许可证有效"
                }
            }
        } ?: "许可证状态未知"
    }
}
