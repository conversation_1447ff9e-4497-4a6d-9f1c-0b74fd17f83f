package com.oasis.callblocker.service.background

import android.app.Notification
import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.PendingIntent
import android.app.Service
import android.content.Context
import android.content.Intent
import android.os.Build
import android.os.IBinder
import androidx.core.app.NotificationCompat
import com.oasis.callblocker.R
import com.oasis.callblocker.presentation.ui.MainActivity
import dagger.hilt.android.AndroidEntryPoint

/**
 * 通知服务
 * 处理各种应用通知
 */
@AndroidEntryPoint
class NotificationService : Service() {
    
    companion object {
        private const val CHANNEL_ID_BLOCK = "oasis_block_notifications"
        private const val CHANNEL_ID_CALL = "oasis_call_notifications"
        private const val CHANNEL_ID_SYSTEM = "oasis_system_notifications"
        
        // Actions
        const val ACTION_SHOW_BLOCK_NOTIFICATION = "show_block_notification"
        const val ACTION_SHOW_CALL_NOTIFICATION = "show_call_notification"
        const val ACTION_SHOW_SYSTEM_NOTIFICATION = "show_system_notification"
        
        // Extras
        const val EXTRA_PHONE_NUMBER = "phone_number"
        const val EXTRA_CONTACT_NAME = "contact_name"
        const val EXTRA_TITLE = "title"
        const val EXTRA_MESSAGE = "message"
        const val EXTRA_NOTIFICATION_ID = "notification_id"
    }
    
    override fun onCreate() {
        super.onCreate()
        createNotificationChannels()
    }
    
    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        intent?.let { handleIntent(it) }
        return START_NOT_STICKY
    }
    
    override fun onBind(intent: Intent?): IBinder? = null
    
    /**
     * 处理Intent
     */
    private fun handleIntent(intent: Intent) {
        when (intent.action) {
            ACTION_SHOW_BLOCK_NOTIFICATION -> {
                val phoneNumber = intent.getStringExtra(EXTRA_PHONE_NUMBER) ?: return
                val contactName = intent.getStringExtra(EXTRA_CONTACT_NAME)
                showBlockNotification(phoneNumber, contactName)
            }
            ACTION_SHOW_CALL_NOTIFICATION -> {
                val phoneNumber = intent.getStringExtra(EXTRA_PHONE_NUMBER) ?: return
                val contactName = intent.getStringExtra(EXTRA_CONTACT_NAME)
                showCallNotification(phoneNumber, contactName)
            }
            ACTION_SHOW_SYSTEM_NOTIFICATION -> {
                val title = intent.getStringExtra(EXTRA_TITLE) ?: return
                val message = intent.getStringExtra(EXTRA_MESSAGE) ?: return
                val notificationId = intent.getIntExtra(EXTRA_NOTIFICATION_ID, 0)
                showSystemNotification(title, message, notificationId)
            }
        }
    }
    
    /**
     * 创建通知渠道
     */
    private fun createNotificationChannels() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val notificationManager = getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
            
            // 拦截通知渠道
            val blockChannel = NotificationChannel(
                CHANNEL_ID_BLOCK,
                "来电拦截",
                NotificationManager.IMPORTANCE_DEFAULT
            ).apply {
                description = "显示被拦截的来电信息"
            }
            
            // 通话通知渠道
            val callChannel = NotificationChannel(
                CHANNEL_ID_CALL,
                "通话信息",
                NotificationManager.IMPORTANCE_LOW
            ).apply {
                description = "显示通话相关信息"
            }
            
            // 系统通知渠道
            val systemChannel = NotificationChannel(
                CHANNEL_ID_SYSTEM,
                "系统通知",
                NotificationManager.IMPORTANCE_DEFAULT
            ).apply {
                description = "显示系统相关通知"
            }
            
            notificationManager.createNotificationChannels(
                listOf(blockChannel, callChannel, systemChannel)
            )
        }
    }
    
    /**
     * 显示拦截通知
     */
    private fun showBlockNotification(phoneNumber: String, contactName: String?) {
        val displayName = contactName ?: phoneNumber
        
        val intent = Intent(this, MainActivity::class.java).apply {
            putExtra("navigate_to", "block_history")
            flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TOP
        }
        
        val pendingIntent = PendingIntent.getActivity(
            this, 0, intent,
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )
        
        val notification = NotificationCompat.Builder(this, CHANNEL_ID_BLOCK)
            .setContentTitle("已拦截来电")
            .setContentText("来自: $displayName")
            .setSmallIcon(R.drawable.ic_block)
            .setContentIntent(pendingIntent)
            .setAutoCancel(true)
            .setPriority(NotificationCompat.PRIORITY_DEFAULT)
            .setCategory(NotificationCompat.CATEGORY_CALL)
            .addAction(
                R.drawable.ic_add,
                "加入黑名单",
                createAddToBlockListIntent(phoneNumber)
            )
            .build()
        
        val notificationManager = getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
        notificationManager.notify(System.currentTimeMillis().toInt(), notification)
    }
    
    /**
     * 显示通话通知
     */
    private fun showCallNotification(phoneNumber: String, contactName: String?) {
        val displayName = contactName ?: phoneNumber
        
        val intent = Intent(this, MainActivity::class.java).apply {
            putExtra("navigate_to", "call_logs")
            flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TOP
        }
        
        val pendingIntent = PendingIntent.getActivity(
            this, 0, intent,
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )
        
        val notification = NotificationCompat.Builder(this, CHANNEL_ID_CALL)
            .setContentTitle("通话信息")
            .setContentText("与 $displayName 的通话")
            .setSmallIcon(R.drawable.ic_call)
            .setContentIntent(pendingIntent)
            .setAutoCancel(true)
            .setPriority(NotificationCompat.PRIORITY_LOW)
            .setCategory(NotificationCompat.CATEGORY_CALL)
            .build()
        
        val notificationManager = getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
        notificationManager.notify(System.currentTimeMillis().toInt(), notification)
    }
    
    /**
     * 显示系统通知
     */
    private fun showSystemNotification(title: String, message: String, notificationId: Int) {
        val intent = Intent(this, MainActivity::class.java).apply {
            flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TOP
        }
        
        val pendingIntent = PendingIntent.getActivity(
            this, 0, intent,
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )
        
        val notification = NotificationCompat.Builder(this, CHANNEL_ID_SYSTEM)
            .setContentTitle(title)
            .setContentText(message)
            .setSmallIcon(R.drawable.ic_notification)
            .setContentIntent(pendingIntent)
            .setAutoCancel(true)
            .setPriority(NotificationCompat.PRIORITY_DEFAULT)
            .setCategory(NotificationCompat.CATEGORY_MESSAGE)
            .setStyle(NotificationCompat.BigTextStyle().bigText(message))
            .build()
        
        val notificationManager = getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
        notificationManager.notify(notificationId, notification)
    }
    
    /**
     * 创建添加到黑名单的Intent
     */
    private fun createAddToBlockListIntent(phoneNumber: String): PendingIntent {
        val intent = Intent(this, MainActivity::class.java).apply {
            putExtra("action", "add_to_block_list")
            putExtra("phone_number", phoneNumber)
            flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TOP
        }
        
        return PendingIntent.getActivity(
            this, phoneNumber.hashCode(), intent,
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )
    }
}
