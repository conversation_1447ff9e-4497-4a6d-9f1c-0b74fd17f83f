package com.oasis.callblocker.service.call

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.telephony.TelephonyManager
import android.util.Log
import com.oasis.callblocker.domain.usecase.block.ProcessIncomingCallUseCase
import com.oasis.callblocker.utils.ContactUtils
import com.oasis.callblocker.utils.PhoneUtils
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * 来电广播接收器
 * 对应原来的NewPhonecallReceiver.java
 */
@AndroidEntryPoint
class CallReceiver : BroadcastReceiver() {
    
    @Inject
    lateinit var processIncomingCallUseCase: ProcessIncomingCallUseCase
    
    @Inject
    lateinit var contactUtils: ContactUtils
    
    @Inject
    lateinit var phoneUtils: PhoneUtils
    
    private val scope = CoroutineScope(Dispatchers.IO + SupervisorJob())
    
    companion object {
        private const val TAG = "CallReceiver"
        private var lastCallState = TelephonyManager.CALL_STATE_IDLE
        private var lastIncomingNumber: String? = null
    }
    
    override fun onReceive(context: Context, intent: Intent) {
        try {
            when (intent.action) {
                TelephonyManager.ACTION_PHONE_STATE_CHANGED -> {
                    handlePhoneStateChanged(context, intent)
                }
                Intent.ACTION_NEW_OUTGOING_CALL -> {
                    handleOutgoingCall(context, intent)
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "处理通话状态变化时出错", e)
        }
    }
    
    /**
     * 处理电话状态变化
     */
    private fun handlePhoneStateChanged(context: Context, intent: Intent) {
        val state = intent.getStringExtra(TelephonyManager.EXTRA_STATE)
        val incomingNumber = intent.getStringExtra(TelephonyManager.EXTRA_INCOMING_NUMBER)
        
        Log.d(TAG, "电话状态变化: $state, 号码: $incomingNumber")
        
        when (state) {
            TelephonyManager.EXTRA_STATE_RINGING -> {
                handleIncomingCall(context, incomingNumber)
            }
            TelephonyManager.EXTRA_STATE_OFFHOOK -> {
                handleCallAnswered(context, incomingNumber)
            }
            TelephonyManager.EXTRA_STATE_IDLE -> {
                handleCallEnded(context)
            }
        }
        
        lastCallState = when (state) {
            TelephonyManager.EXTRA_STATE_RINGING -> TelephonyManager.CALL_STATE_RINGING
            TelephonyManager.EXTRA_STATE_OFFHOOK -> TelephonyManager.CALL_STATE_OFFHOOK
            else -> TelephonyManager.CALL_STATE_IDLE
        }
    }
    
    /**
     * 处理来电
     */
    private fun handleIncomingCall(context: Context, phoneNumber: String?) {
        if (phoneNumber.isNullOrBlank()) return
        
        lastIncomingNumber = phoneNumber
        
        scope.launch {
            try {
                // 获取联系人姓名
                val contactName = contactUtils.getContactName(context, phoneNumber)
                
                // 处理来电并检查是否需要拦截
                val shouldBlock = processIncomingCallUseCase(phoneNumber, contactName)
                
                if (shouldBlock) {
                    Log.i(TAG, "拦截来电: $phoneNumber")
                    
                    // 拦截来电
                    phoneUtils.endCall(context)
                    
                    // 显示拦截通知
                    showBlockNotification(context, phoneNumber, contactName)
                    
                    // 启动悬浮窗显示拦截信息
                    startFloatingWindow(context, phoneNumber, contactName, true)
                } else {
                    Log.i(TAG, "允许来电: $phoneNumber")
                    
                    // 启动悬浮窗显示来电信息
                    startFloatingWindow(context, phoneNumber, contactName, false)
                }
            } catch (e: Exception) {
                Log.e(TAG, "处理来电时出错: $phoneNumber", e)
            }
        }
    }
    
    /**
     * 处理去电
     */
    private fun handleOutgoingCall(context: Context, intent: Intent) {
        val phoneNumber = intent.getStringExtra(Intent.EXTRA_PHONE_NUMBER)
        if (phoneNumber.isNullOrBlank()) return
        
        Log.d(TAG, "去电: $phoneNumber")
        
        scope.launch {
            try {
                val contactName = contactUtils.getContactName(context, phoneNumber)
                
                // 启动悬浮窗显示去电信息
                startFloatingWindow(context, phoneNumber, contactName, false)
            } catch (e: Exception) {
                Log.e(TAG, "处理去电时出错: $phoneNumber", e)
            }
        }
    }
    
    /**
     * 处理通话接听
     */
    private fun handleCallAnswered(context: Context, phoneNumber: String?) {
        Log.d(TAG, "通话接听: $phoneNumber")
        
        // 更新悬浮窗状态为通话中
        val intent = Intent(context, FloatingWindowService::class.java).apply {
            action = FloatingWindowService.ACTION_UPDATE_CALL_STATE
            putExtra(FloatingWindowService.EXTRA_CALL_STATE, "通话中")
        }
        context.startService(intent)
    }
    
    /**
     * 处理通话结束
     */
    private fun handleCallEnded(context: Context) {
        Log.d(TAG, "通话结束")
        
        // 关闭悬浮窗
        val intent = Intent(context, FloatingWindowService::class.java).apply {
            action = FloatingWindowService.ACTION_HIDE_WINDOW
        }
        context.startService(intent)
        
        lastIncomingNumber = null
    }
    
    /**
     * 显示拦截通知
     */
    private fun showBlockNotification(context: Context, phoneNumber: String, contactName: String?) {
        val intent = Intent(context, NotificationService::class.java).apply {
            action = NotificationService.ACTION_SHOW_BLOCK_NOTIFICATION
            putExtra(NotificationService.EXTRA_PHONE_NUMBER, phoneNumber)
            putExtra(NotificationService.EXTRA_CONTACT_NAME, contactName)
        }
        context.startService(intent)
    }
    
    /**
     * 启动悬浮窗
     */
    private fun startFloatingWindow(
        context: Context, 
        phoneNumber: String, 
        contactName: String?, 
        isBlocked: Boolean
    ) {
        val intent = Intent(context, FloatingWindowService::class.java).apply {
            action = FloatingWindowService.ACTION_SHOW_CALL_INFO
            putExtra(FloatingWindowService.EXTRA_PHONE_NUMBER, phoneNumber)
            putExtra(FloatingWindowService.EXTRA_CONTACT_NAME, contactName)
            putExtra(FloatingWindowService.EXTRA_IS_BLOCKED, isBlocked)
        }
        context.startService(intent)
    }
}
