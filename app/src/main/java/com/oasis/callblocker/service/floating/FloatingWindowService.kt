package com.oasis.callblocker.service.floating

import android.app.Service
import android.content.Intent
import android.graphics.PixelFormat
import android.os.Build
import android.os.IBinder
import android.provider.Settings
import android.util.Log
import android.view.Gravity
import android.view.LayoutInflater
import android.view.MotionEvent
import android.view.View
import android.view.WindowManager
import android.widget.TextView
import androidx.core.content.ContextCompat
import com.oasis.callblocker.R
import com.oasis.callblocker.data.local.preferences.OasisPreferences
import dagger.hilt.android.AndroidEntryPoint
import javax.inject.Inject

/**
 * 悬浮窗服务
 * 对应原来的FloatingViewService.java
 */
@AndroidEntryPoint
class FloatingWindowService : Service() {
    
    @Inject
    lateinit var preferences: OasisPreferences
    
    private var windowManager: WindowManager? = null
    private var floatingView: View? = null
    private var isWindowShowing = false
    
    // 悬浮窗位置
    private var lastX = 0
    private var lastY = 0
    private var initialX = 0
    private var initialY = 0
    private var initialTouchX = 0f
    private var initialTouchY = 0f
    
    companion object {
        private const val TAG = "FloatingWindowService"
        
        // Actions
        const val ACTION_SHOW_CALL_INFO = "show_call_info"
        const val ACTION_HIDE_WINDOW = "hide_window"
        const val ACTION_UPDATE_CALL_STATE = "update_call_state"
        
        // Extras
        const val EXTRA_PHONE_NUMBER = "phone_number"
        const val EXTRA_CONTACT_NAME = "contact_name"
        const val EXTRA_IS_BLOCKED = "is_blocked"
        const val EXTRA_CALL_STATE = "call_state"
    }
    
    override fun onCreate() {
        super.onCreate()
        windowManager = getSystemService(WINDOW_SERVICE) as WindowManager
        Log.d(TAG, "悬浮窗服务创建")
    }
    
    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        intent?.let { handleIntent(it) }
        return START_NOT_STICKY
    }
    
    override fun onBind(intent: Intent?): IBinder? = null
    
    override fun onDestroy() {
        super.onDestroy()
        hideFloatingWindow()
        Log.d(TAG, "悬浮窗服务销毁")
    }
    
    /**
     * 处理Intent
     */
    private fun handleIntent(intent: Intent) {
        when (intent.action) {
            ACTION_SHOW_CALL_INFO -> {
                val phoneNumber = intent.getStringExtra(EXTRA_PHONE_NUMBER) ?: return
                val contactName = intent.getStringExtra(EXTRA_CONTACT_NAME)
                val isBlocked = intent.getBooleanExtra(EXTRA_IS_BLOCKED, false)
                
                showCallInfo(phoneNumber, contactName, isBlocked)
            }
            ACTION_HIDE_WINDOW -> {
                hideFloatingWindow()
            }
            ACTION_UPDATE_CALL_STATE -> {
                val callState = intent.getStringExtra(EXTRA_CALL_STATE) ?: return
                updateCallState(callState)
            }
        }
    }
    
    /**
     * 显示通话信息
     */
    private fun showCallInfo(phoneNumber: String, contactName: String?, isBlocked: Boolean) {
        if (!canDrawOverlays()) {
            Log.w(TAG, "没有悬浮窗权限")
            return
        }
        
        try {
            // 如果已经显示，先隐藏
            if (isWindowShowing) {
                hideFloatingWindow()
            }
            
            // 创建悬浮窗布局
            floatingView = createFloatingView(phoneNumber, contactName, isBlocked)
            
            // 设置窗口参数
            val params = createWindowLayoutParams()
            
            // 添加到窗口管理器
            windowManager?.addView(floatingView, params)
            isWindowShowing = true
            
            Log.d(TAG, "显示悬浮窗: $phoneNumber, 拦截: $isBlocked")
            
        } catch (e: Exception) {
            Log.e(TAG, "显示悬浮窗失败", e)
        }
    }
    
    /**
     * 隐藏悬浮窗
     */
    private fun hideFloatingWindow() {
        try {
            if (isWindowShowing && floatingView != null) {
                windowManager?.removeView(floatingView)
                isWindowShowing = false
                floatingView = null
                Log.d(TAG, "隐藏悬浮窗")
            }
        } catch (e: Exception) {
            Log.e(TAG, "隐藏悬浮窗失败", e)
        }
    }
    
    /**
     * 更新通话状态
     */
    private fun updateCallState(callState: String) {
        floatingView?.findViewById<TextView>(R.id.tv_call_state)?.text = callState
    }
    
    /**
     * 创建悬浮窗视图
     */
    private fun createFloatingView(phoneNumber: String, contactName: String?, isBlocked: Boolean): View {
        val inflater = LayoutInflater.from(this)
        val view = inflater.inflate(R.layout.floating_call_info, null)
        
        // 设置通话信息
        val tvName = view.findViewById<TextView>(R.id.tv_contact_name)
        val tvNumber = view.findViewById<TextView>(R.id.tv_phone_number)
        val tvStatus = view.findViewById<TextView>(R.id.tv_call_status)
        val tvState = view.findViewById<TextView>(R.id.tv_call_state)
        
        tvName.text = contactName ?: "未知联系人"
        tvNumber.text = phoneNumber
        tvState.text = "来电中"
        
        if (isBlocked) {
            tvStatus.text = "已拦截"
            tvStatus.setTextColor(ContextCompat.getColor(this, R.color.blocked_red))
            view.setBackgroundResource(R.drawable.floating_window_blocked_bg)
        } else {
            tvStatus.text = "允许"
            tvStatus.setTextColor(ContextCompat.getColor(this, R.color.allowed_green))
            view.setBackgroundResource(R.drawable.floating_window_normal_bg)
        }
        
        // 设置触摸监听器
        view.setOnTouchListener(createTouchListener())
        
        return view
    }
    
    /**
     * 创建窗口布局参数
     */
    private fun createWindowLayoutParams(): WindowManager.LayoutParams {
        val type = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY
        } else {
            @Suppress("DEPRECATION")
            WindowManager.LayoutParams.TYPE_PHONE
        }
        
        val params = WindowManager.LayoutParams(
            WindowManager.LayoutParams.WRAP_CONTENT,
            WindowManager.LayoutParams.WRAP_CONTENT,
            type,
            WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE or
                    WindowManager.LayoutParams.FLAG_LAYOUT_IN_SCREEN or
                    WindowManager.LayoutParams.FLAG_NOT_TOUCH_MODAL,
            PixelFormat.TRANSLUCENT
        )
        
        // 设置位置
        params.gravity = Gravity.TOP or Gravity.START
        params.x = getFloatingWindowX()
        params.y = getFloatingWindowY()
        
        return params
    }
    
    /**
     * 创建触摸监听器
     */
    private fun createTouchListener(): View.OnTouchListener {
        return View.OnTouchListener { view, event ->
            when (event.action) {
                MotionEvent.ACTION_DOWN -> {
                    initialX = (view.layoutParams as WindowManager.LayoutParams).x
                    initialY = (view.layoutParams as WindowManager.LayoutParams).y
                    initialTouchX = event.rawX
                    initialTouchY = event.rawY
                    true
                }
                MotionEvent.ACTION_MOVE -> {
                    val params = view.layoutParams as WindowManager.LayoutParams
                    params.x = initialX + (event.rawX - initialTouchX).toInt()
                    params.y = initialY + (event.rawY - initialTouchY).toInt()
                    
                    windowManager?.updateViewLayout(view, params)
                    true
                }
                MotionEvent.ACTION_UP -> {
                    val params = view.layoutParams as WindowManager.LayoutParams
                    saveFloatingWindowPosition(params.x, params.y)
                    true
                }
                else -> false
            }
        }
    }
    
    /**
     * 检查是否有悬浮窗权限
     */
    private fun canDrawOverlays(): Boolean {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            Settings.canDrawOverlays(this)
        } else {
            true
        }
    }
    
    /**
     * 获取悬浮窗X坐标
     */
    private fun getFloatingWindowX(): Int {
        return preferences.popupPosition and 0xFFFF
    }
    
    /**
     * 获取悬浮窗Y坐标
     */
    private fun getFloatingWindowY(): Int {
        return (preferences.popupPosition shr 16) and 0xFFFF
    }
    
    /**
     * 保存悬浮窗位置
     */
    private fun saveFloatingWindowPosition(x: Int, y: Int) {
        preferences.popupPosition = (y shl 16) or (x and 0xFFFF)
    }
}
