package com.oasis.callblocker.service.background

import android.app.Notification
import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.PendingIntent
import android.app.Service
import android.content.Context
import android.content.Intent
import android.os.Build
import android.os.IBinder
import android.util.Log
import androidx.core.app.NotificationCompat
import com.oasis.callblocker.R
import com.oasis.callblocker.domain.usecase.notice.GetNoticesUseCase
import com.oasis.callblocker.presentation.ui.MainActivity
import com.oasis.callblocker.utils.ContactSyncManager
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.delay
import kotlinx.coroutines.isActive
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * 后台同步服务
 * 对应原来的MainService.java
 */
@AndroidEntryPoint
class BackgroundSyncService : Service() {
    
    @Inject
    lateinit var getNoticesUseCase: GetNoticesUseCase
    
    @Inject
    lateinit var contactSyncManager: ContactSyncManager
    
    private val serviceScope = CoroutineScope(Dispatchers.IO + SupervisorJob())
    private var syncJob: Job? = null
    
    companion object {
        private const val TAG = "BackgroundSyncService"
        private const val NOTIFICATION_ID = 1001
        private const val CHANNEL_ID = "oasis_background_service"
        private const val SYNC_INTERVAL = 30 * 60 * 1000L // 30分钟
        
        // Actions
        const val ACTION_START_SYNC = "start_sync"
        const val ACTION_STOP_SYNC = "stop_sync"
        const val ACTION_SYNC_CONTACTS = "sync_contacts"
        const val ACTION_CHECK_NOTICES = "check_notices"
    }
    
    override fun onCreate() {
        super.onCreate()
        createNotificationChannel()
        Log.d(TAG, "后台同步服务创建")
    }
    
    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        when (intent?.action) {
            ACTION_START_SYNC -> startBackgroundSync()
            ACTION_STOP_SYNC -> stopBackgroundSync()
            ACTION_SYNC_CONTACTS -> syncContacts()
            ACTION_CHECK_NOTICES -> checkNotices()
            else -> startBackgroundSync()
        }
        
        return START_STICKY // 服务被杀死后自动重启
    }
    
    override fun onBind(intent: Intent?): IBinder? = null
    
    override fun onDestroy() {
        super.onDestroy()
        stopBackgroundSync()
        Log.d(TAG, "后台同步服务销毁")
    }
    
    /**
     * 开始后台同步
     */
    private fun startBackgroundSync() {
        if (syncJob?.isActive == true) {
            Log.d(TAG, "后台同步已在运行")
            return
        }
        
        // 启动前台服务
        startForeground(NOTIFICATION_ID, createNotification())
        
        // 开始定期同步任务
        syncJob = serviceScope.launch {
            Log.d(TAG, "开始后台同步任务")
            
            while (isActive) {
                try {
                    // 执行同步任务
                    performSyncTasks()
                    
                    // 等待下次同步
                    delay(SYNC_INTERVAL)
                    
                } catch (e: Exception) {
                    Log.e(TAG, "后台同步任务出错", e)
                    delay(60 * 1000L) // 出错后等待1分钟再重试
                }
            }
        }
    }
    
    /**
     * 停止后台同步
     */
    private fun stopBackgroundSync() {
        syncJob?.cancel()
        syncJob = null
        stopForeground(true)
        stopSelf()
        Log.d(TAG, "停止后台同步")
    }
    
    /**
     * 执行同步任务
     */
    private suspend fun performSyncTasks() {
        Log.d(TAG, "执行同步任务")
        
        try {
            // 同步联系人
            contactSyncManager.syncContacts()
            
            // 检查新通知
            val hasNewNotices = getNoticesUseCase.checkNewNotices()
            if (hasNewNotices.isSuccess && hasNewNotices.getOrDefault(false)) {
                Log.d(TAG, "发现新通知")
                showNewNoticeNotification()
            }
            
            // 同步通知列表
            getNoticesUseCase.syncNoticesFromServer()
            
            Log.d(TAG, "同步任务完成")
            
        } catch (e: Exception) {
            Log.e(TAG, "执行同步任务失败", e)
        }
    }
    
    /**
     * 同步联系人
     */
    private fun syncContacts() {
        serviceScope.launch {
            try {
                contactSyncManager.syncContacts()
                Log.d(TAG, "联系人同步完成")
            } catch (e: Exception) {
                Log.e(TAG, "联系人同步失败", e)
            }
        }
    }
    
    /**
     * 检查通知
     */
    private fun checkNotices() {
        serviceScope.launch {
            try {
                val result = getNoticesUseCase.checkNewNotices()
                if (result.isSuccess && result.getOrDefault(false)) {
                    showNewNoticeNotification()
                }
                Log.d(TAG, "通知检查完成")
            } catch (e: Exception) {
                Log.e(TAG, "通知检查失败", e)
            }
        }
    }
    
    /**
     * 创建通知渠道
     */
    private fun createNotificationChannel() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val channel = NotificationChannel(
                CHANNEL_ID,
                "OASIS 后台服务",
                NotificationManager.IMPORTANCE_LOW
            ).apply {
                description = "OASIS 应用后台同步服务"
                setShowBadge(false)
            }
            
            val notificationManager = getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
            notificationManager.createNotificationChannel(channel)
        }
    }
    
    /**
     * 创建前台服务通知
     */
    private fun createNotification(): Notification {
        val intent = Intent(this, MainActivity::class.java)
        val pendingIntent = PendingIntent.getActivity(
            this, 0, intent,
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )
        
        return NotificationCompat.Builder(this, CHANNEL_ID)
            .setContentTitle("OASIS 来电拦截")
            .setContentText("正在后台运行，保护您的通话安全")
            .setSmallIcon(R.drawable.ic_notification)
            .setContentIntent(pendingIntent)
            .setOngoing(true)
            .setPriority(NotificationCompat.PRIORITY_LOW)
            .setCategory(NotificationCompat.CATEGORY_SERVICE)
            .build()
    }
    
    /**
     * 显示新通知提醒
     */
    private fun showNewNoticeNotification() {
        val intent = Intent(this, MainActivity::class.java).apply {
            putExtra("navigate_to", "notices")
        }
        val pendingIntent = PendingIntent.getActivity(
            this, 1, intent,
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )
        
        val notification = NotificationCompat.Builder(this, CHANNEL_ID)
            .setContentTitle("OASIS 新通知")
            .setContentText("您有新的系统通知，点击查看")
            .setSmallIcon(R.drawable.ic_notification)
            .setContentIntent(pendingIntent)
            .setAutoCancel(true)
            .setPriority(NotificationCompat.PRIORITY_DEFAULT)
            .build()
        
        val notificationManager = getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
        notificationManager.notify(1002, notification)
    }
}
