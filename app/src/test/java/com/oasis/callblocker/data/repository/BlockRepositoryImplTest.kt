package com.oasis.callblocker.data.repository

import com.oasis.callblocker.data.local.database.dao.BlockHistoryDao
import com.oasis.callblocker.data.local.database.dao.BlockNumberDao
import com.oasis.callblocker.data.local.database.entities.BlockNumberEntity
import com.oasis.callblocker.data.local.preferences.OasisPreferences
import com.oasis.callblocker.domain.model.BlockSettings
import com.oasis.callblocker.domain.model.BlockType
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import kotlinx.coroutines.test.runTest
import org.junit.jupiter.api.Assertions.*
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test

/**
 * BlockRepositoryImpl单元测试
 */
class BlockRepositoryImplTest {
    
    private lateinit var blockNumberDao: BlockNumberDao
    private lateinit var blockHistoryDao: BlockHistoryDao
    private lateinit var preferences: OasisPreferences
    private lateinit var repository: BlockRepositoryImpl
    
    @BeforeEach
    fun setup() {
        blockNumberDao = mockk()
        blockHistoryDao = mockk()
        preferences = mockk()
        repository = BlockRepositoryImpl(blockNumberDao, blockHistoryDao, preferences)
    }
    
    @Test
    fun `获取拦截设置应该从偏好设置中读取`() = runTest {
        // Given
        every { preferences.isBlockUnknown } returns true
        every { preferences.isBlockSpecificNumbers } returns false
        every { preferences.isBlockPrefix } returns true
        every { preferences.isBlockTodayCall } returns false
        every { preferences.isBlockAll } returns false
        every { preferences.isBlockCallExplosion } returns true
        every { preferences.limitTodayCall } returns 5
        every { preferences.callExplosionCount } returns 3
        
        // When
        val settings = repository.getBlockSettings()
        
        // Then
        assertTrue(settings.isBlockUnknown)
        assertFalse(settings.isBlockSpecificNumbers)
        assertTrue(settings.isBlockPrefix)
        assertFalse(settings.isBlockTodayCall)
        assertFalse(settings.isBlockAll)
        assertTrue(settings.isBlockCallExplosion)
        assertEquals(5, settings.limitTodayCall)
        assertEquals(3, settings.callExplosionCount)
    }
    
    @Test
    fun `更新拦截设置应该保存到偏好设置`() = runTest {
        // Given
        val settings = BlockSettings(
            isBlockUnknown = true,
            isBlockSpecificNumbers = false,
            isBlockPrefix = true,
            isBlockTodayCall = false,
            isBlockAll = false,
            isBlockCallExplosion = true,
            limitTodayCall = 5,
            callExplosionCount = 3
        )
        
        every { preferences.isBlockUnknown = any() } returns Unit
        every { preferences.isBlockSpecificNumbers = any() } returns Unit
        every { preferences.isBlockPrefix = any() } returns Unit
        every { preferences.isBlockTodayCall = any() } returns Unit
        every { preferences.isBlockAll = any() } returns Unit
        every { preferences.isBlockCallExplosion = any() } returns Unit
        every { preferences.limitTodayCall = any() } returns Unit
        every { preferences.callExplosionCount = any() } returns Unit
        
        // When
        repository.updateBlockSettings(settings)
        
        // Then
        verify { preferences.isBlockUnknown = true }
        verify { preferences.isBlockSpecificNumbers = false }
        verify { preferences.isBlockPrefix = true }
        verify { preferences.isBlockTodayCall = false }
        verify { preferences.isBlockAll = false }
        verify { preferences.isBlockCallExplosion = true }
        verify { preferences.limitTodayCall = 5 }
        verify { preferences.callExplosionCount = 3 }
    }
    
    @Test
    fun `拦截所有来电时应该返回true`() = runTest {
        // Given
        val phoneNumber = "13812345678"
        every { preferences.isBlockAll } returns true
        every { preferences.isBlockUnknown } returns false
        every { preferences.isBlockSpecificNumbers } returns false
        every { preferences.isBlockPrefix } returns false
        every { preferences.isBlockTodayCall } returns false
        every { preferences.isBlockCallExplosion } returns false
        every { preferences.limitTodayCall } returns 3
        every { preferences.callExplosionCount } returns 5
        
        // When
        val shouldBlock = repository.shouldBlockCall(phoneNumber)
        
        // Then
        assertTrue(shouldBlock)
    }
    
    @Test
    fun `特定号码拦截测试`() = runTest {
        // Given
        val phoneNumber = "13812345678"
        val blockNumberEntity = BlockNumberEntity(
            phoneNumber = phoneNumber,
            blockType = BlockType.SPECIFIC.value,
            todayCount = 0
        )
        
        every { preferences.isBlockAll } returns false
        every { preferences.isBlockSpecificNumbers } returns true
        every { preferences.isBlockPrefix } returns false
        every { preferences.isBlockTodayCall } returns false
        every { preferences.isBlockCallExplosion } returns false
        every { preferences.limitTodayCall } returns 3
        every { preferences.callExplosionCount } returns 5
        
        coEvery { blockNumberDao.getBlockNumber(phoneNumber) } returns blockNumberEntity
        
        // When
        val shouldBlock = repository.shouldBlockCall(phoneNumber)
        
        // Then
        assertTrue(shouldBlock)
        coVerify { blockNumberDao.getBlockNumber(phoneNumber) }
    }
    
    @Test
    fun `前缀拦截测试`() = runTest {
        // Given
        val phoneNumber = "13812345678"
        val prefix = "138"
        val prefixBlockNumbers = listOf(
            BlockNumberEntity(
                phoneNumber = prefix,
                blockType = BlockType.PREFIX.value,
                todayCount = 0
            )
        )
        
        every { preferences.isBlockAll } returns false
        every { preferences.isBlockSpecificNumbers } returns false
        every { preferences.isBlockPrefix } returns true
        every { preferences.isBlockTodayCall } returns false
        every { preferences.isBlockCallExplosion } returns false
        every { preferences.limitTodayCall } returns 3
        every { preferences.callExplosionCount } returns 5
        
        coEvery { blockNumberDao.getBlockNumber(phoneNumber) } returns null
        coEvery { 
            blockNumberDao.getBlockNumbersByPrefix(BlockType.PREFIX.value, prefix) 
        } returns prefixBlockNumbers
        
        // When
        val shouldBlock = repository.shouldBlockCall(phoneNumber)
        
        // Then
        assertTrue(shouldBlock)
        coVerify { blockNumberDao.getBlockNumbersByPrefix(BlockType.PREFIX.value, prefix) }
    }
    
    @Test
    fun `今日通话限制测试`() = runTest {
        // Given
        val phoneNumber = "13812345678"
        val todayLimit = 3
        val todayCount = 5 // 超过限制
        
        every { preferences.isBlockAll } returns false
        every { preferences.isBlockSpecificNumbers } returns false
        every { preferences.isBlockPrefix } returns false
        every { preferences.isBlockTodayCall } returns true
        every { preferences.isBlockCallExplosion } returns false
        every { preferences.limitTodayCall } returns todayLimit
        every { preferences.callExplosionCount } returns 5
        
        coEvery { blockNumberDao.getBlockNumber(phoneNumber) } returns null
        coEvery { blockNumberDao.getBlockNumbersByPrefix(any(), any()) } returns emptyList()
        coEvery { blockHistoryDao.getBlockCountSince(phoneNumber, any()) } returns todayCount
        
        // When
        val shouldBlock = repository.shouldBlockCall(phoneNumber)
        
        // Then
        assertTrue(shouldBlock)
        coVerify { blockHistoryDao.getBlockCountSince(phoneNumber, any()) }
    }
    
    @Test
    fun `呼叫爆炸检测测试`() = runTest {
        // Given
        val phoneNumber = "13812345678"
        val explosionLimit = 3
        val recentCount = 5 // 超过限制
        
        every { preferences.isBlockAll } returns false
        every { preferences.isBlockSpecificNumbers } returns false
        every { preferences.isBlockPrefix } returns false
        every { preferences.isBlockTodayCall } returns false
        every { preferences.isBlockCallExplosion } returns true
        every { preferences.limitTodayCall } returns 3
        every { preferences.callExplosionCount } returns explosionLimit
        
        coEvery { blockNumberDao.getBlockNumber(phoneNumber) } returns null
        coEvery { blockNumberDao.getBlockNumbersByPrefix(any(), any()) } returns emptyList()
        coEvery { blockHistoryDao.getBlockCountSince(phoneNumber, any()) } returns 0 andThen recentCount
        
        // When
        val shouldBlock = repository.shouldBlockCall(phoneNumber)
        
        // Then
        assertTrue(shouldBlock)
    }
    
    @Test
    fun `不满足任何拦截条件时应该返回false`() = runTest {
        // Given
        val phoneNumber = "13812345678"
        
        every { preferences.isBlockAll } returns false
        every { preferences.isBlockSpecificNumbers } returns false
        every { preferences.isBlockPrefix } returns false
        every { preferences.isBlockTodayCall } returns false
        every { preferences.isBlockCallExplosion } returns false
        every { preferences.limitTodayCall } returns 3
        every { preferences.callExplosionCount } returns 5
        
        coEvery { blockNumberDao.getBlockNumber(phoneNumber) } returns null
        coEvery { blockNumberDao.getBlockNumbersByPrefix(any(), any()) } returns emptyList()
        coEvery { blockHistoryDao.getBlockCountSince(phoneNumber, any()) } returns 0
        
        // When
        val shouldBlock = repository.shouldBlockCall(phoneNumber)
        
        // Then
        assertFalse(shouldBlock)
    }
}
