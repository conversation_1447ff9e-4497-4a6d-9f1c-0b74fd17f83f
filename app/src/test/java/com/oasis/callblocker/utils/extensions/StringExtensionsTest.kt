package com.oasis.callblocker.utils.extensions

import org.junit.jupiter.api.Assertions.*
import org.junit.jupiter.api.Test

/**
 * StringExtensions单元测试
 */
class StringExtensionsTest {
    
    @Test
    fun `测试邮箱验证`() {
        // 有效邮箱
        assertTrue("<EMAIL>".isValidEmail())
        assertTrue("<EMAIL>".isValidEmail())
        assertTrue("<EMAIL>".isValidEmail())
        
        // 无效邮箱
        assertFalse("invalid-email".isValidEmail())
        assertFalse("@example.com".isValidEmail())
        assertFalse("test@".isValidEmail())
        assertFalse("".isValidEmail())
    }
    
    @Test
    fun `测试手机号码验证`() {
        // 有效手机号码
        assertTrue("13812345678".isValidPhoneNumber())
        assertTrue("15987654321".isValidPhoneNumber())
        assertTrue("18666666666".isValidPhoneNumber())
        
        // 无效手机号码
        assertFalse("12812345678".isValidPhoneNumber()) // 不是1开头的有效号段
        assertFalse("1381234567".isValidPhoneNumber())  // 长度不够
        assertFalse("138123456789".isValidPhoneNumber()) // 长度过长
        assertFalse("".isValidPhoneNumber())
        assertFalse("abcdefghijk".isValidPhoneNumber())
    }
    
    @Test
    fun `测试电话号码格式化`() {
        assertEquals("13812345678", "+86 138-1234-5678".formatPhoneNumber())
        assertEquals("13812345678", "138 1234 5678".formatPhoneNumber())
        assertEquals("13812345678", "8613812345678".formatPhoneNumber())
        assertEquals("13812345678", "13812345678".formatPhoneNumber())
        assertEquals("1234567", "123-456-7".formatPhoneNumber())
    }
    
    @Test
    fun `测试电话号码掩码`() {
        assertEquals("138****5678", "13812345678".maskPhoneNumber())
        assertEquals("123****567", "1234567".maskPhoneNumber())
        assertEquals("123", "123".maskPhoneNumber()) // 长度不足时不掩码
    }
    
    @Test
    fun `测试时间戳格式化`() {
        val timestamp = 1640995200000L // 2022-01-01 00:00:00
        val formatted = timestamp.toFormattedDate("yyyy-MM-dd")
        assertEquals("2022-01-01", formatted)
    }
    
    @Test
    fun `测试相对时间`() {
        val now = System.currentTimeMillis()
        
        // 刚刚
        val justNow = now - 30 * 1000
        assertEquals("刚刚", justNow.toRelativeTime())
        
        // 分钟前
        val minutesAgo = now - 5 * 60 * 1000
        assertEquals("5分钟前", minutesAgo.toRelativeTime())
        
        // 小时前
        val hoursAgo = now - 2 * 60 * 60 * 1000
        assertEquals("2小时前", hoursAgo.toRelativeTime())
        
        // 天前
        val daysAgo = now - 3 * 24 * 60 * 60 * 1000
        assertEquals("3天前", daysAgo.toRelativeTime())
    }
    
    @Test
    fun `测试安全转换为整数`() {
        assertEquals(123, "123".toIntOrDefault())
        assertEquals(0, "abc".toIntOrDefault())
        assertEquals(-1, "invalid".toIntOrDefault(-1))
        assertEquals(456, "456.789".toIntOrDefault()) // 会失败，返回默认值
    }
    
    @Test
    fun `测试字符串截取`() {
        assertEquals("Hello...", "Hello World".truncate(8))
        assertEquals("Hello", "Hello".truncate(10))
        assertEquals("H...", "Hello".truncate(4))
    }
    
    @Test
    fun `测试首字母大写`() {
        assertEquals("Hello", "hello".capitalize())
        assertEquals("World", "WORLD".capitalize())
        assertEquals("", "".capitalize())
        assertEquals("A", "a".capitalize())
    }
    
    @Test
    fun `测试HTML标签移除`() {
        assertEquals("Hello World", "<p>Hello <b>World</b></p>".removeHtmlTags())
        assertEquals("Test", "<div>Test</div>".removeHtmlTags())
        assertEquals("Plain text", "Plain text".removeHtmlTags())
    }
    
    @Test
    fun `测试中文字符检测`() {
        assertTrue("你好世界".containsChinese())
        assertTrue("Hello 世界".containsChinese())
        assertFalse("Hello World".containsChinese())
        assertFalse("123456".containsChinese())
    }
    
    @Test
    fun `测试字节长度计算`() {
        assertEquals(5, "Hello".getByteLength()) // 英文字符1字节
        assertEquals(8, "你好世界".getByteLength()) // 中文字符2字节
        assertEquals(11, "Hello世界".getByteLength()) // 混合
    }
    
    @Test
    fun `测试null或空字符串检查`() {
        assertTrue("".isNullOrEmpty())
        assertTrue((null as String?).isNullOrEmpty())
        assertFalse("Hello".isNullOrEmpty())
        
        assertTrue("   ".isNullOrBlank())
        assertTrue((null as String?).isNullOrBlank())
        assertFalse("Hello".isNullOrBlank())
    }
    
    @Test
    fun `测试安全获取字符串`() {
        assertEquals("", (null as String?).orEmpty())
        assertEquals("Hello", "Hello".orEmpty())
        assertEquals("", "".orEmpty())
    }
}
