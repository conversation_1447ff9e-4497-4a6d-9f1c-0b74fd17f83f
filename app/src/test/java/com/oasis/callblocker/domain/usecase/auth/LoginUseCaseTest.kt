package com.oasis.callblocker.domain.usecase.auth

import com.oasis.callblocker.domain.model.LoginCredentials
import com.oasis.callblocker.domain.model.User
import com.oasis.callblocker.domain.repository.UserRepository
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.mockk
import kotlinx.coroutines.test.runTest
import org.junit.jupiter.api.Assertions.*
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test

/**
 * LoginUseCase单元测试
 */
class LoginUseCaseTest {
    
    private lateinit var userRepository: UserRepository
    private lateinit var loginUseCase: LoginUseCase
    
    @BeforeEach
    fun setup() {
        userRepository = mockk()
        loginUseCase = LoginUseCase(userRepository)
    }
    
    @Test
    fun `登录成功时应该返回用户信息`() = runTest {
        // Given
        val email = "<EMAIL>"
        val password = "password123"
        val deviceToken = "device_token"
        val expectedUser = User(
            userId = "user123",
            userEmail = email,
            userCompany = "Test Company"
        )
        
        coEvery { userRepository.getDeviceToken() } returns deviceToken
        coEvery { 
            userRepository.login(
                LoginCredentials(email, password, deviceToken)
            ) 
        } returns Result.success(expectedUser)
        
        // When
        val result = loginUseCase(email, password)
        
        // Then
        assertTrue(result.isSuccess)
        assertEquals(expectedUser, result.getOrNull())
        
        coVerify { userRepository.getDeviceToken() }
        coVerify { 
            userRepository.login(
                LoginCredentials(email, password, deviceToken)
            ) 
        }
    }
    
    @Test
    fun `邮箱为空时应该返回错误`() = runTest {
        // Given
        val email = ""
        val password = "password123"
        
        // When
        val result = loginUseCase(email, password)
        
        // Then
        assertTrue(result.isFailure)
        assertEquals("邮箱不能为空", result.exceptionOrNull()?.message)
    }
    
    @Test
    fun `密码为空时应该返回错误`() = runTest {
        // Given
        val email = "<EMAIL>"
        val password = ""
        
        // When
        val result = loginUseCase(email, password)
        
        // Then
        assertTrue(result.isFailure)
        assertEquals("密码不能为空", result.exceptionOrNull()?.message)
    }
    
    @Test
    fun `邮箱格式不正确时应该返回错误`() = runTest {
        // Given
        val email = "invalid-email"
        val password = "password123"
        
        // When
        val result = loginUseCase(email, password)
        
        // Then
        assertTrue(result.isFailure)
        assertEquals("邮箱格式不正确", result.exceptionOrNull()?.message)
    }
    
    @Test
    fun `登录失败时应该返回错误`() = runTest {
        // Given
        val email = "<EMAIL>"
        val password = "wrong_password"
        val deviceToken = "device_token"
        val errorMessage = "用户名或密码错误"
        
        coEvery { userRepository.getDeviceToken() } returns deviceToken
        coEvery { 
            userRepository.login(
                LoginCredentials(email, password, deviceToken)
            ) 
        } returns Result.failure(Exception(errorMessage))
        
        // When
        val result = loginUseCase(email, password)
        
        // Then
        assertTrue(result.isFailure)
        assertEquals(errorMessage, result.exceptionOrNull()?.message)
    }
    
    @Test
    fun `邮箱前后空格应该被去除`() = runTest {
        // Given
        val email = "  <EMAIL>  "
        val password = "password123"
        val deviceToken = "device_token"
        val expectedUser = User(
            userId = "user123",
            userEmail = "<EMAIL>",
            userCompany = "Test Company"
        )
        
        coEvery { userRepository.getDeviceToken() } returns deviceToken
        coEvery { 
            userRepository.login(
                LoginCredentials("<EMAIL>", password, deviceToken)
            ) 
        } returns Result.success(expectedUser)
        
        // When
        val result = loginUseCase(email, password)
        
        // Then
        assertTrue(result.isSuccess)
        
        coVerify { 
            userRepository.login(
                LoginCredentials("<EMAIL>", password, deviceToken)
            ) 
        }
    }
}
