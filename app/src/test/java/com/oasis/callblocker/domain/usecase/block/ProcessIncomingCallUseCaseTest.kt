package com.oasis.callblocker.domain.usecase.block

import com.oasis.callblocker.domain.model.CallLog
import com.oasis.callblocker.domain.model.CallType
import com.oasis.callblocker.domain.repository.BlockRepository
import com.oasis.callblocker.domain.repository.CallLogRepository
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.mockk
import io.mockk.slot
import kotlinx.coroutines.test.runTest
import org.junit.jupiter.api.Assertions.*
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test

/**
 * ProcessIncomingCallUseCase单元测试
 */
class ProcessIncomingCallUseCaseTest {
    
    private lateinit var blockRepository: BlockRepository
    private lateinit var callLogRepository: CallLogRepository
    private lateinit var processIncomingCallUseCase: ProcessIncomingCallUseCase
    
    @BeforeEach
    fun setup() {
        blockRepository = mockk()
        callLogRepository = mockk()
        processIncomingCallUseCase = ProcessIncomingCallUseCase(blockRepository, callLogRepository)
    }
    
    @Test
    fun `应该拦截的号码返回true并记录拦截日志`() = runTest {
        // Given
        val phoneNumber = "13812345678"
        val contactName = "测试联系人"
        
        coEvery { 
            blockRepository.processIncomingCall(phoneNumber, contactName) 
        } returns true
        
        coEvery { callLogRepository.addCallLog(any()) } returns Unit
        
        // When
        val result = processIncomingCallUseCase(phoneNumber, contactName)
        
        // Then
        assertTrue(result)
        
        // 验证调用
        coVerify { blockRepository.processIncomingCall(phoneNumber, contactName) }
        
        // 验证通话日志记录
        val callLogSlot = slot<CallLog>()
        coVerify { callLogRepository.addCallLog(capture(callLogSlot)) }
        
        val capturedCallLog = callLogSlot.captured
        assertEquals(phoneNumber, capturedCallLog.phoneNumber)
        assertEquals(contactName, capturedCallLog.contactName)
        assertEquals(CallType.INCOMING, capturedCallLog.callType)
        assertTrue(capturedCallLog.isBlocked)
    }
    
    @Test
    fun `不应该拦截的号码返回false并记录正常日志`() = runTest {
        // Given
        val phoneNumber = "13812345678"
        val contactName = "测试联系人"
        
        coEvery { 
            blockRepository.processIncomingCall(phoneNumber, contactName) 
        } returns false
        
        coEvery { callLogRepository.addCallLog(any()) } returns Unit
        
        // When
        val result = processIncomingCallUseCase(phoneNumber, contactName)
        
        // Then
        assertFalse(result)
        
        // 验证通话日志记录
        val callLogSlot = slot<CallLog>()
        coVerify { callLogRepository.addCallLog(capture(callLogSlot)) }
        
        val capturedCallLog = callLogSlot.captured
        assertEquals(phoneNumber, capturedCallLog.phoneNumber)
        assertEquals(contactName, capturedCallLog.contactName)
        assertEquals(CallType.INCOMING, capturedCallLog.callType)
        assertFalse(capturedCallLog.isBlocked)
    }
    
    @Test
    fun `没有联系人姓名时应该正常处理`() = runTest {
        // Given
        val phoneNumber = "13812345678"
        val contactName: String? = null
        
        coEvery { 
            blockRepository.processIncomingCall(phoneNumber, contactName) 
        } returns true
        
        coEvery { callLogRepository.addCallLog(any()) } returns Unit
        
        // When
        val result = processIncomingCallUseCase(phoneNumber, contactName)
        
        // Then
        assertTrue(result)
        
        // 验证通话日志记录
        val callLogSlot = slot<CallLog>()
        coVerify { callLogRepository.addCallLog(capture(callLogSlot)) }
        
        val capturedCallLog = callLogSlot.captured
        assertEquals(phoneNumber, capturedCallLog.phoneNumber)
        assertNull(capturedCallLog.contactName)
    }
    
    @Test
    fun `电话号码格式化测试`() = runTest {
        // Given
        val phoneNumber = "+86 138-1234-5678"
        val expectedFormattedNumber = "13812345678"
        
        coEvery { 
            blockRepository.processIncomingCall(expectedFormattedNumber, null) 
        } returns false
        
        coEvery { callLogRepository.addCallLog(any()) } returns Unit
        
        // When
        val result = processIncomingCallUseCase(phoneNumber, null)
        
        // Then
        assertFalse(result)
        
        // 验证使用格式化后的号码
        coVerify { blockRepository.processIncomingCall(expectedFormattedNumber, null) }
        
        val callLogSlot = slot<CallLog>()
        coVerify { callLogRepository.addCallLog(capture(callLogSlot)) }
        
        val capturedCallLog = callLogSlot.captured
        assertEquals(expectedFormattedNumber, capturedCallLog.phoneNumber)
    }
    
    @Test
    fun `国际号码格式化测试`() = runTest {
        // Given
        val phoneNumber = "8613812345678"
        val expectedFormattedNumber = "13812345678"
        
        coEvery { 
            blockRepository.processIncomingCall(expectedFormattedNumber, null) 
        } returns false
        
        coEvery { callLogRepository.addCallLog(any()) } returns Unit
        
        // When
        val result = processIncomingCallUseCase(phoneNumber, null)
        
        // Then
        assertFalse(result)
        
        // 验证使用格式化后的号码
        coVerify { blockRepository.processIncomingCall(expectedFormattedNumber, null) }
    }
    
    @Test
    fun `处理异常情况`() = runTest {
        // Given
        val phoneNumber = "13812345678"
        val contactName = "测试联系人"
        
        coEvery { 
            blockRepository.processIncomingCall(phoneNumber, contactName) 
        } throws Exception("处理失败")
        
        // When & Then
        assertThrows(Exception::class.java) {
            runTest {
                processIncomingCallUseCase(phoneNumber, contactName)
            }
        }
    }
}
