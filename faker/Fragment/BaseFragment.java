package com.developer.faker.Fragment;

import android.app.AlertDialog;
import android.app.ProgressDialog;
import android.content.DialogInterface;
import android.os.Bundle;
import android.support.v4.app.Fragment;
import android.widget.Toast;
import com.developer.faker.Activity.BaseActivity;
import com.developer.faker.Utils.Utils;

/* loaded from: classes.dex */
public class BaseFragment extends Fragment {
    public BaseActivity baseActivity;
    private ProgressDialog mProgressDlg;

    @Override // android.support.v4.app.Fragment
    public void onCreate(Bundle bundle) {
        super.onCreate(bundle);
        this.baseActivity = (BaseActivity) getActivity();
    }

    @Override // android.support.v4.app.Fragment
    public void onResume() {
        super.onResume();
        try {
            BaseActivity baseActivity = (BaseActivity) getActivity();
            baseActivity.hideSoftKeyboard();
            baseActivity.invalidateOptionsMenu();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    protected void showAlertDialog(String str, String str2) {
        AlertDialog.Builder builder = new AlertDialog.Builder(getContext());
        builder.setTitle(str);
        builder.setMessage(str2);
        builder.setPositiveButton("OK", (DialogInterface.OnClickListener) null);
        builder.show();
    }

    protected void showToast(String str) {
        Toast.makeText(getContext(), str, 0).show();
    }

    protected void showProgress(String str) {
        this.mProgressDlg = Utils.getInstance().openNewDialog(getContext(), str, false, false);
    }

    protected void dismissProgress() {
        try {
            if (this.mProgressDlg == null || !this.mProgressDlg.isShowing()) {
                return;
            }
            this.mProgressDlg.dismiss();
        } catch (Exception unused) {
        }
    }
}
