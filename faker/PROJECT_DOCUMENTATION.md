# Android 来电拦截与号码查询应用文档

## 📱 项目概述

这是一个功能全面的Android应用程序，专为**来电拦截和电话号码查询功能**而设计。该应用为用户提供拦截骚扰电话、搜索电话号码信息以及管理通话记录等多种过滤和拦截选项的能力。

### 🎯 核心功能
- **来电拦截**: 基于多种条件拦截来电（未知号码、特定号码、号码前缀等）
- **电话号码查询**: 搜索和识别未知电话号码
- **通话记录管理**: 跟踪和管理最近通话及被拦截通话
- **用户认证**: 基于令牌的安全登录系统
- **悬浮界面**: 通话期间快速访问的覆盖界面
- **通知系统**: 应用内通知和公告

## 🏗️ 项目结构

```
com.developer.faker/
├── Activity/           # 主要应用活动
├── Adapter/           # UI的RecyclerView/ListView适配器
├── Data/              # 数据模型类
├── Fragment/          # 不同屏幕的UI片段
├── Model/             # 接口和监听器
├── Service/           # 后台服务和接收器
└── Utils/             # 工具类和辅助类
```

## 📋 详细组件分析

### 🎬 活动(Activities)

#### `BaseActivity.java`
- **用途**: 所有活动的基类，提供通用功能
- **主要特性**:
  - 片段导航管理
  - 进度对话框处理
  - Toast消息工具
  - 软键盘管理
  - 服务生命周期管理

#### `LoginActivity.java`
- **用途**: 用户认证和登录界面
- **主要特性**:
  - 邮箱/密码认证
  - 基于RC4加密的令牌登录
  - 设备令牌注册
  - 权限请求处理
  - 自动登录功能

#### `MainActivity.java`
- **用途**: 带导航抽屉的主应用界面
- **主要特性**:
  - 带菜单项的导航抽屉
  - 片段切换（主页、拦截、通知、设置）
  - 带注销确认的返回按钮处理
  - 用户信息显示（公司、许可证到期）

### 🧩 片段(Fragments)

#### `BaseFragment.java`
- **用途**: 所有片段的基类
- **通用特性**:
  - 进度对话框管理
  - 警告对话框工具
  - Toast消息处理
  - 活动引用管理

#### `MainFragment.java`
- **用途**: 显示最近通话和快速操作的主仪表板
- **功能特性**:
  - 最近通话记录显示
  - 通话记录刷新功能
  - 搜索集成
  - 实时通话状态更新

#### `BlockFragment.java`
- **用途**: 来电拦截管理的容器片段
- **子片段**:
  - `BlockFragmentSetting`: 拦截配置选项
  - `BlockFragmentNumbers`: 管理被拦截号码列表
  - `BlockFragmentHistory`: 查看拦截历史

#### `SearchFragment.java`
- **用途**: 电话号码查询和搜索功能
- **功能特性**:
  - 使用服务器API进行号码搜索
  - 搜索历史管理
  - 结果显示和缓存

#### `NoticeFragment.java`
- **用途**: 显示应用通知和公告
- **功能特性**:
  - 通知列表显示
  - 新通知指示器
  - 服务器同步公告

#### `SettingFragment.java`
- **用途**: 应用配置和偏好设置
- **功能特性**:
  - 用户偏好管理
  - 应用设置配置
  - 账户管理

### 🔧 服务(Services)

#### `MainService.java`
- **用途**: 应用功能的核心后台服务
- **功能特性**:
  - 定期联系人同步
  - 通知检查
  - 带通知的前台服务
  - 基于定时器的后台任务

#### `NewPhonecallReceiver.java`
- **用途**: 来电/去电事件的广播接收器
- **功能特性**:
  - 通话状态监控（来电、去电、结束）
  - 基于规则的自动来电拦截
  - 通话记录日志
  - 实时UI更新

#### `FloatingViewService.java`
- **用途**: 通话信息显示的覆盖UI服务
- **功能特性**:
  - 悬浮窗口管理
  - 通话信息覆盖
  - 触摸事件处理
  - 窗口定位

#### `BootReceiver.java` & `RestartReceiver.java`
- **用途**: 服务持久化的系统事件接收器
- **功能特性**:
  - 设备启动时自动启动
  - 应用更新时服务重启
  - 系统事件处理

### 📊 数据模型(Data Models)

#### `BlockNumberData.java`
- **属性**: `phoneNumber`(电话号码), `nBlockType`(拦截类型), `nTodayCount`(今日次数)
- **类型**: 特定号码、基于前缀、呼叫爆炸

#### `BlockNumberHistory.java`
- **属性**: `number`(号码), `type`(类型), `dateTick`(日期时间戳)
- **类型**: 未知号码、今日通话限制、特定号码、前缀、全部、呼叫爆炸

#### `UserInfo.java`
- **属性**: 用户凭据和认证数据
- **用途**: 登录和会话管理

#### `RecentIncomeInfo.java` & `RecentCallData.java`
- **属性**: 通话记录信息
- **用途**: 最近通话显示和管理

#### `NoticeInfo.java`
- **属性**: 通知数据
- **用途**: 应用公告和通知

### 🔌 适配器(Adapters)

#### `BlockNumberListAdapter.java`
- **用途**: 显示被拦截号码列表
- **功能**: 号码格式化、删除功能、类型指示器

#### `BlockNumberHistoryAdapter.java`
- **用途**: 显示拦截历史
- **功能**: 日期格式化、拦截类型显示、历史管理

#### `RecentCallListAdapter.java`
- **用途**: 显示最近通话记录
- **功能**: 通话类型指示器、联系人集成、时间格式化

#### `DrawerAdapter.java`
- **用途**: 导航抽屉菜单项
- **功能**: 菜单项显示、图标管理

### 🛠️ 工具类(Utility Classes)

#### `UtilBlock.java`
- **用途**: 来电拦截逻辑和配置
- **关键方法**:
  - `IsNeedBlock()`: 判断号码是否应被拦截
  - `addBlockHistory()`: 记录拦截事件
  - `LoadSetting()` / `SaveSetting()`: 持久化管理

#### `UtilAuth.java`
- **用途**: 用户认证和会话管理
- **功能**: 令牌管理、登录状态、设备注册

#### `UtilContact.java`
- **用途**: 与服务器的联系人同步
- **功能**: 联系人上传、服务器通信

#### `Utils.java`
- **用途**: 通用工具函数
- **功能**: 电话号码格式化、通话管理、验证

#### `RC4.java`
- **用途**: 安全通信的加密/解密
- **功能**: 数据保护的RC4算法实现

#### `Global.java`
- **用途**: 全局状态管理
- **属性**:
  - `fragment_State`: 当前UI状态
  - `Incoming_Call_Number`: 活动通话号码
  - `NoticeList`: 应用通知
  - `RemainQueryCount`: API使用跟踪

## 🔄 应用流程

### 1. **应用启动**
```
LoginActivity → 身份验证 → MainActivity → MainFragment
```

### 2. **来电拦截流程**
```
NewPhonecallReceiver → UtilBlock.IsNeedBlock() → 拦截/允许决策 → 历史记录
```

### 3. **号码搜索流程**
```
SearchFragment → 服务器API → 结果显示 → 历史存储
```

### 4. **后台操作**
```
MainService → 联系人同步 → 通知检查 → UI更新
```

## 🔗 关键关系

- **MainActivity** 作为中央枢纽，管理片段导航
- **NewPhonecallReceiver** 与 **UtilBlock** 集成，实现实时来电拦截
- **MainService** 协调后台任务和服务器通信
- **FloatingViewService** 在活动通话期间提供覆盖UI
- 所有片段都继承 **BaseFragment** 以保持一致的行为
- **Global** 类维护跨组件的应用范围状态

## 🎛️ 配置与设置

应用使用 **SharedPreferences** 进行配置存储，键值在 `Const.java` 中定义：
- 拦截设置（未知号码、特定号码、前缀）
- 用户偏好（弹窗位置、显示选项）
- 认证令牌和用户数据
- API端点和加密密钥

## 📱 核心功能详解

### 🚫 来电拦截机制
应用提供多种拦截策略：
1. **未知号码拦截**: 拦截不在联系人中的号码
2. **特定号码拦截**: 用户手动添加的黑名单号码
3. **前缀拦截**: 基于号码前缀的批量拦截
4. **今日通话限制**: 限制同一号码的日通话次数
5. **全部拦截**: 拦截所有来电
6. **呼叫爆炸拦截**: 防止短时间内大量来电

### 🔍 号码查询功能
- 与服务器API集成，提供号码信息查询
- 支持搜索历史记录
- 缓存查询结果以提高性能
- 显示号码归属地和标记信息

### 📊 数据管理
- 使用RC4加密保护敏感数据传输
- SharedPreferences存储用户配置
- 实时同步联系人信息到服务器
- 维护详细的拦截和通话历史记录

这种架构为通话管理提供了强大、可扩展的解决方案，具有清晰的关注点分离和模块化设计。

## 📱 Android API 版本信息

### 🔢 应用版本配置
- **应用版本号 (versionCode)**: `1029`
- **应用版本名 (versionName)**: `"1029"`
- **应用包名**: `com.developer.faker`
- **构建类型**: `release`

### 🎯 Android SDK 版本支持

根据AndroidManifest.xml文件的配置：

#### **SDK 版本配置**
- **最低支持版本 (minSdkVersion)**: `19` (Android 4.4 KitKat)
- **目标版本 (targetSdkVersion)**: `28` (Android 9.0 Pie)
- **编译版本 (compileSdkVersion)**: `28` (Android 9.0 Pie)
- **平台构建版本**: `28`

#### **版本兼容性范围**
- **最低支持**: Android 4.4 (API 19) - 2013年发布
- **目标版本**: Android 9.0 (API 28) - 2018年发布
- **兼容范围**: 支持从Android 4.4到Android 9.0的所有版本

### 🔧 关键API级别特性

应用在代码中针对不同API级别做了兼容性处理：

1. **API 19-22**: 基础功能支持
2. **API 23 (Android 6.0)**: 运行时权限、悬浮窗权限
3. **API 26 (Android 8.0)**: 通知渠道、前台服务
4. **API 28 (Android 9.0)**: 来电管理、通话控制

### 📋 权限配置

应用请求了以下关键权限：
- **通话相关**: `READ_PHONE_STATE`, `CALL_PHONE`, `ANSWER_PHONE_CALLS`, `MODIFY_PHONE_STATE`
- **通话记录**: `READ_CALL_LOG`, `WRITE_CALL_LOG`, `PROCESS_OUTGOING_CALLS`
- **联系人**: `READ_CONTACTS`, `WRITE_CONTACTS`
- **短信**: `READ_SMS`, `RECEIVE_SMS`
- **系统**: `SYSTEM_ALERT_WINDOW`, `RECEIVE_BOOT_COMPLETED`, `FOREGROUND_SERVICE`
- **网络**: `INTERNET`, `ACCESS_NETWORK_STATE`
- **存储**: `READ_EXTERNAL_STORAGE`, `WRITE_EXTERNAL_STORAGE`
