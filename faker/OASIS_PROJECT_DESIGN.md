# OASIS - 来电拦截应用 Kotlin 重构设计

## 🎯 项目概述

OASIS (Optimized Android Security & Interception System) 是原来电拦截应用的现代化Kotlin重构版本，采用最新的Android开发最佳实践。

## 🏗️ 现代架构设计

### 📱 架构模式
- **MVVM (Model-View-ViewModel)**: 清晰的数据绑定和UI逻辑分离
- **Repository Pattern**: 统一的数据访问层
- **Clean Architecture**: 分层架构，依赖倒置
- **Single Activity**: 使用Navigation Component

### 🔧 技术栈
- **语言**: Kotlin 100%
- **UI**: Jetpack Compose + View Binding (过渡期)
- **架构组件**: ViewModel, LiveData, Room, Navigation
- **异步处理**: Kotlin Coroutines + Flow
- **依赖注入**: Hilt
- **网络**: Retrofit + OkHttp
- **图像加载**: Coil
- **测试**: JUnit5, Mockk, Espresso

## 📁 项目结构

```
app/src/main/java/com/oasis/callblocker/
├── data/                           # 数据层
│   ├── local/                      # 本地数据源
│   │   ├── database/              # Room数据库
│   │   │   ├── entities/          # 数据库实体
│   │   │   ├── dao/               # 数据访问对象
│   │   │   └── OasisDatabase.kt   # 数据库配置
│   │   └── preferences/           # SharedPreferences封装
│   ├── remote/                    # 远程数据源
│   │   ├── api/                   # API接口定义
│   │   ├── dto/                   # 数据传输对象
│   │   └── interceptors/          # 网络拦截器
│   └── repository/                # 仓库实现
├── domain/                        # 业务逻辑层
│   ├── model/                     # 业务模型
│   ├── repository/                # 仓库接口
│   └── usecase/                   # 用例
├── presentation/                  # 表现层
│   ├── ui/                        # UI组件
│   │   ├── main/                  # 主界面
│   │   ├── login/                 # 登录界面
│   │   ├── block/                 # 拦截管理
│   │   ├── search/                # 号码搜索
│   │   ├── notice/                # 通知管理
│   │   └── settings/              # 设置界面
│   ├── viewmodel/                 # ViewModel
│   └── adapter/                   # RecyclerView适配器
├── service/                       # 服务层
│   ├── call/                      # 通话相关服务
│   ├── floating/                  # 悬浮窗服务
│   └── background/                # 后台服务
├── receiver/                      # 广播接收器
├── utils/                         # 工具类
│   ├── extensions/                # Kotlin扩展函数
│   ├── constants/                 # 常量定义
│   └── helpers/                   # 辅助工具
└── di/                           # 依赖注入模块
```

## 🔄 重构映射关系

### 原Java类 → 新Kotlin类

#### 数据模型层
- `BlockNumberData.java` → `BlockNumberEntity.kt` (Room Entity)
- `UserInfo.java` → `UserEntity.kt` + `UserDto.kt`
- `RecentCallData.java` → `CallLogEntity.kt`
- `NoticeInfo.java` → `NoticeEntity.kt`

#### UI层
- `MainActivity.java` → `MainActivity.kt` (Single Activity)
- `LoginActivity.java` → `LoginFragment.kt` + `LoginViewModel.kt`
- `MainFragment.java` → `HomeFragment.kt` + `HomeViewModel.kt`
- `BlockFragment.java` → `BlockManagementFragment.kt` + `BlockViewModel.kt`

#### 服务层
- `MainService.java` → `BackgroundSyncService.kt`
- `NewPhonecallReceiver.java` → `CallReceiver.kt`
- `FloatingViewService.java` → `FloatingWindowService.kt`

#### 工具类
- `Utils.java` → `CallUtils.kt` + Extensions
- `UtilAuth.java` → `AuthRepository.kt` + `AuthManager.kt`
- `UtilBlock.java` → `BlockRepository.kt` + `BlockManager.kt`

## 🎨 UI/UX 改进

### 设计语言
- **Material Design 3**: 最新的Material You设计
- **动态主题**: 支持Android 12+的动态颜色
- **深色模式**: 完整的深色主题支持
- **无障碍**: 完整的无障碍功能支持

### 用户体验
- **流畅动画**: 使用Motion Layout和Compose动画
- **响应式设计**: 适配不同屏幕尺寸
- **手势导航**: 支持现代手势导航
- **快速操作**: 快捷方式和小部件支持

## 🔐 安全性增强

### 数据保护
- **加密存储**: 使用EncryptedSharedPreferences
- **证书固定**: API通信安全
- **混淆保护**: 代码混淆和资源保护
- **权限最小化**: 精确权限请求

### 隐私保护
- **数据最小化**: 只收集必要数据
- **本地优先**: 优先本地处理
- **透明度**: 清晰的隐私政策
- **用户控制**: 完整的数据控制权

## 📊 性能优化

### 内存管理
- **协程**: 替代线程池，减少内存占用
- **懒加载**: 按需加载数据和UI
- **缓存策略**: 智能缓存管理
- **内存泄漏**: 严格的内存泄漏检测

### 电池优化
- **后台限制**: 遵循Android后台限制
- **JobScheduler**: 智能任务调度
- **Doze模式**: 适配低功耗模式
- **网络优化**: 减少不必要的网络请求

## 🧪 测试策略

### 测试金字塔
- **单元测试**: 业务逻辑和工具类测试
- **集成测试**: Repository和数据库测试
- **UI测试**: 关键用户流程测试
- **端到端测试**: 完整功能测试

### 测试工具
- **JUnit5**: 现代测试框架
- **Mockk**: Kotlin友好的Mock框架
- **Turbine**: Flow测试工具
- **Robolectric**: Android单元测试

## 🚀 部署和发布

### CI/CD
- **GitHub Actions**: 自动化构建和测试
- **代码质量**: SonarQube代码分析
- **安全扫描**: 依赖漏洞扫描
- **自动发布**: 自动化应用发布流程

### 版本管理
- **语义化版本**: 清晰的版本号规则
- **变更日志**: 详细的更新记录
- **渐进式发布**: 分阶段发布策略
- **回滚机制**: 快速回滚能力

这个设计为OASIS项目提供了现代化、可维护、高性能的架构基础。
