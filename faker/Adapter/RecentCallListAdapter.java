package com.developer.faker.Adapter;

import android.content.Intent;
import android.net.Uri;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ArrayAdapter;
import android.widget.ImageView;
import android.widget.TextView;
import com.developer.faker.Activity.BaseActivity;
import com.developer.faker.BuildConfig;
import com.developer.faker.Data.RecentCallData;
import com.developer.faker.R;
import com.developer.faker.Utils.Const;
import java.util.ArrayList;

/* loaded from: classes.dex */
public class RecentCallListAdapter extends ArrayAdapter<RecentCallData> {
    BaseActivity activity;
    final Integer[] icons;

    public RecentCallListAdapter(BaseActivity baseActivity, int i, ArrayList<RecentCallData> arrayList) {
        super(baseActivity, i, arrayList);
        this.icons = new Integer[]{Integer.valueOf(R.mipmap.call), Integer.valueOf(R.mipmap.email)};
        this.activity = baseActivity;
    }

    @Override // android.widget.ArrayAdapter, android.widget.Adapter
    public View getView(int i, View view, ViewGroup viewGroup) {
        if (view == null) {
            view = newView(viewGroup);
        }
        bindView(i, view);
        return view;
    }

    private View newView(ViewGroup viewGroup) {
        return this.activity.getLayoutInflater().inflate(R.layout.adapter_recentcalllist, viewGroup, false);
    }

    private void bindView(int i, View view) {
        final RecentCallData item = getItem(i);
        view.findViewById(R.id.btnCall).setOnClickListener(new View.OnClickListener() { // from class: com.developer.faker.Adapter.RecentCallListAdapter.1
            @Override // android.view.View.OnClickListener
            public void onClick(View view2) {
                if (item.callType == Const.CALL_TYPE_PHONE.intValue()) {
                    RecentCallListAdapter.this.getContext().startActivity(new Intent("android.intent.action.CALL", Uri.parse("tel:" + item.phonenumber)));
                    return;
                }
                RecentCallListAdapter.this.sendSmsIntent(item.phonenumber);
            }
        });
        ImageView imageView = (ImageView) view.findViewById(R.id.imgPhone);
        TextView textView = (TextView) view.findViewById(R.id.txtPhoneNumber);
        TextView textView2 = (TextView) view.findViewById(R.id.txtDate);
        TextView textView3 = (TextView) view.findViewById(R.id.txtMemo);
        imageView.setImageResource(this.icons[item.callType].intValue());
        textView.setText(item.phonenumber);
        textView2.setText(item.date);
        textView3.setText(item.memo);
        view.setTag(item);
    }

    public void sendSmsIntent(String str) {
        try {
            Intent intent = new Intent("android.intent.action.SENDTO", Uri.parse("sms:" + str));
            intent.putExtra("sms_body", BuildConfig.FLAVOR);
            getContext().startActivity(intent);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
