package com.developer.faker.Adapter;

import android.app.AlertDialog;
import android.content.DialogInterface;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ArrayAdapter;
import android.widget.LinearLayout;
import android.widget.TextView;
import com.developer.faker.Activity.BaseActivity;
import com.developer.faker.Data.BlockNumberData;
import com.developer.faker.Model.BlockNumberDeleteListener;
import com.developer.faker.R;
import com.developer.faker.Utils.Utils;
import java.util.ArrayList;

/* loaded from: classes.dex */
public class BlockNumberListAdapter extends ArrayAdapter<BlockNumberData> {
    BaseActivity activity;
    BlockNumberDeleteListener mlistener;

    public BlockNumberListAdapter(BaseActivity baseActivity, int i, ArrayList<BlockNumberData> arrayList) {
        super(baseActivity, i, arrayList);
        this.mlistener = null;
        this.activity = baseActivity;
    }

    @Override // android.widget.ArrayAdapter, android.widget.Adapter
    public View getView(int i, View view, ViewGroup viewGroup) {
        if (view == null) {
            view = newView(viewGroup);
        }
        bindView(i, view);
        return view;
    }

    public void setOnClickCloseListener(BlockNumberDeleteListener blockNumberDeleteListener) {
        this.mlistener = blockNumberDeleteListener;
    }

    private View newView(ViewGroup viewGroup) {
        return this.activity.getLayoutInflater().inflate(R.layout.adapter_blocklist, viewGroup, false);
    }

    private void bindView(final int i, View view) {
        BlockNumberData item = getItem(i);
        ((TextView) view.findViewById(R.id.txt_phone)).setText(Utils.getHypenPhoneNumber(item.phoneNumber));
        ((TextView) view.findViewById(R.id.txt_blockcount)).setText(String.valueOf(item.nTodayCount));
        ((LinearLayout) view.findViewById(R.id.contanerlayout)).setBackgroundResource(item.nBlockType == BlockNumberData.BlockType_Pref ? R.color.greenlight_alpha_color : R.color.white_alpha_color);
        if (this.mlistener != null) {
            view.findViewById(R.id.btnDelete).setOnClickListener(new View.OnClickListener() { // from class: com.developer.faker.Adapter.BlockNumberListAdapter.1
                @Override // android.view.View.OnClickListener
                public void onClick(View view2) {
                    final BlockNumberData item2 = BlockNumberListAdapter.this.getItem(i);
                    AlertDialog.Builder builder = new AlertDialog.Builder(view2.getContext());
                    builder.setTitle("삭제");
                    builder.setMessage(item2.phoneNumber + " 를 삭제하시겠습니까?");
                    builder.setPositiveButton("예", new DialogInterface.OnClickListener() { // from class: com.developer.faker.Adapter.BlockNumberListAdapter.1.1
                        @Override // android.content.DialogInterface.OnClickListener
                        public void onClick(DialogInterface dialogInterface, int i2) {
                            BlockNumberListAdapter.this.mlistener.onResult(item2);
                        }
                    });
                    builder.setNegativeButton("아니", (DialogInterface.OnClickListener) null);
                    builder.show();
                }
            });
        }
        view.setTag(item);
    }
}
