package com.developer.faker.Activity;

import android.app.AlertDialog;
import android.content.DialogInterface;
import android.content.Intent;
import android.net.Uri;
import android.os.Build;
import android.os.Bundle;
import android.os.Environment;
import android.os.Handler;
import android.os.Message;
import android.provider.Settings;
import android.support.annotation.NonNull;
import android.support.annotation.Nullable;
import android.support.design.widget.CoordinatorLayout;
import android.support.design.widget.Snackbar;
import android.support.v4.app.ActivityCompat;
import android.support.v4.content.ContextCompat;
import android.support.v4.view.PointerIconCompat;
import android.view.View;
import android.widget.Button;
import android.widget.EditText;
import com.developer.faker.BuildConfig;
import com.developer.faker.Data.UserInfo;
import com.developer.faker.R;
import com.developer.faker.Utils.BackPressHandler;
import com.developer.faker.Utils.Const;
import com.developer.faker.Utils.Global;
import com.developer.faker.Utils.RC4;
import com.developer.faker.Utils.UtilAuth;
import com.developer.faker.Utils.UtilLogFile;
import com.developer.faker.Utils.Utils;
import com.mashape.relocation.cookie.ClientCookie;
import com.mashape.unirest.http.HttpResponse;
import com.mashape.unirest.http.Unirest;
import com.mashape.unirest.http.async.Callback;
import com.mashape.unirest.http.exceptions.UnirestException;
import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;
import org.json.JSONException;
import org.json.JSONObject;

/* loaded from: classes.dex */
public class LoginActivity extends BaseActivity implements ActivityCompat.OnRequestPermissionsResultCallback {
    CoordinatorLayout coordinatorLayout;
    Button m_BtnLogin;
    EditText m_EdtUserID;
    EditText m_EdtUserPass;
    private int PERMISSIONS_REQUEST_CODE = PointerIconCompat.TYPE_CONTEXT_MENU;
    private int OVERLAY_PERMISSION_REQ_CODE = PointerIconCompat.TYPE_HAND;
    private UpdateHandler updateHandler = new UpdateHandler();
    private UtilAuth utilAuth = null;
    private Handler ResultHandler = new Handler() { // from class: com.developer.faker.Activity.LoginActivity.1
        /* JADX WARN: Removed duplicated region for block: B:32:0x011d  */
        @Override // android.os.Handler
        /*
            Code decompiled incorrectly, please refer to instructions dump.
        */
        public void handleMessage(Message message) {
            char c;
            super.handleMessage(message);
            LoginActivity.this.DismissProgress();
            if (message.what == 0) {
                try {
                    JSONObject jSONObject = new JSONObject(RC4.getInstance().decrypt(new JSONObject(message.obj.toString()).getString("data"), Const.AUTH_KEY));
                    if (jSONObject.getBoolean("IsSuccess")) {
                        try {
                            LoginActivity.this.utilAuth.UserID = jSONObject.getInt("UserID");
                            LoginActivity.this.utilAuth.UserToken = jSONObject.getString("Token");
                            LoginActivity.this.utilAuth.UserEmail = LoginActivity.this.m_EdtUserID.getText().toString();
                            LoginActivity.this.utilAuth.UserPWD = LoginActivity.this.m_EdtUserPass.getText().toString();
                            LoginActivity.this.utilAuth.UserCompany = jSONObject.getString("CompanyName");
                            LoginActivity.this.utilAuth.SetRemainMinutes(jSONObject.getInt("RemainMinutes"));
                            LoginActivity.this.utilAuth.saveAuthInfo();
                            LoginActivity.this.startActivity(new Intent(LoginActivity.this, (Class<?>) MainActivity.class));
                            LoginActivity.this.overridePendingTransition(R.anim.slidein, R.anim.slideout);
                            return;
                        } catch (Exception e) {
                            UtilLogFile.getInstance(LoginActivity.this.getBaseContext()).writeLog(e.toString());
                            return;
                        }
                    }
                    String string = jSONObject.getString("ErrorCode");
                    switch (string.hashCode()) {
                        case -1739300946:
                            if (!string.equals("Error_Expired")) {
                                c = 65535;
                                break;
                            } else {
                                c = 3;
                                break;
                            }
                        case -451503499:
                            if (string.equals("Error_Blocked")) {
                                c = 2;
                                break;
                            }
                            break;
                        case 985443064:
                            if (string.equals("Error_Invalid_PhoneNumber")) {
                                c = 4;
                                break;
                            }
                            break;
                        case 1602013803:
                            if (string.equals("Error_InvalidUser")) {
                                c = 0;
                                break;
                            }
                            break;
                        case 1955543596:
                            if (string.equals("Error_NotAllowed")) {
                                c = 1;
                                break;
                            }
                            break;
                        case 1993901561:
                            if (string.equals("Error_Invalid_Version")) {
                                c = 5;
                                break;
                            }
                            break;
                        default:
                            c = 65535;
                            break;
                    }
                    if (c == 0) {
                        LoginActivity.this.showToast("로그인정보 틀림.");
                        return;
                    }
                    if (c == 1) {
                        LoginActivity.this.showToast("로그인 허락되지 않음.");
                        return;
                    }
                    if (c == 2) {
                        LoginActivity.this.showToast("사용금지됨.");
                        return;
                    }
                    if (c == 3) {
                        LoginActivity.this.showToast("사용기간 만료.");
                        return;
                    }
                    if (c == 4) {
                        LoginActivity.this.showToast("전화번호 틀림.");
                        return;
                    } else if (c == 5) {
                        LoginActivity.this.showToast("버젼 오류");
                        return;
                    } else {
                        LoginActivity.this.showToast("오류발생.");
                        return;
                    }
                } catch (Exception e2) {
                    e2.printStackTrace();
                    return;
                }
            }
            LoginActivity.this.showToast(message.obj.toString());
        }
    };
    private String m_RequestURL = BuildConfig.FLAVOR;
    private Map<String, String> m_Header = new HashMap();
    private BackPressHandler backPressHandler = new BackPressHandler(this);

    @Override // com.developer.faker.Activity.BaseActivity, android.support.v7.app.AppCompatActivity, android.support.v4.app.FragmentActivity, android.support.v4.app.SupportActivity, android.app.Activity
    protected void onCreate(Bundle bundle) {
        super.onCreate(bundle);
        setContentView(R.layout.activity_login);
        this.utilAuth = UtilAuth.getInstance(getBaseContext());
        this.coordinatorLayout = (CoordinatorLayout) findViewById(R.id.coordinatorLayout);
        this.m_EdtUserID = (EditText) findViewById(R.id.edtLogID);
        this.m_EdtUserID.setFocusable(true);
        this.m_EdtUserID.setEnabled(false);
        this.m_EdtUserPass = (EditText) findViewById(R.id.edtLogPass);
        this.m_EdtUserPass.setEnabled(false);
        this.m_BtnLogin = (Button) findViewById(R.id.btnLogin);
        this.m_BtnLogin.setEnabled(false);
        RequestPermission();
        this.m_EdtUserID.setText(this.utilAuth.UserEmail);
        this.m_EdtUserPass.setText(this.utilAuth.UserPWD);
    }

    public void onClickBtnLogin(View view) throws IOException {
        if (this.m_EdtUserID.getText().toString().isEmpty()) {
            showToast("아이디를 입력하세요.");
        } else if (this.m_EdtUserPass.getText().toString().isEmpty()) {
            showToast("비번을 입력하세요.");
        } else {
            Login();
        }
    }

    private void Login() throws IOException {
        try {
            UserInfo userInfo = new UserInfo();
            userInfo.userID = this.m_EdtUserID.getText().toString();
            userInfo.userPass = this.m_EdtUserPass.getText().toString();
            userInfo.devToken = this.utilAuth.getDeviceToken();
            if (!Utils.isNullOrEmptyString(userInfo.userID) && !Utils.isNullOrEmptyString(userInfo.userPass)) {
                ShowProgress(getResources().getString(R.string.wait));
                LoginWithEmail(userInfo);
            }
        } catch (Exception e) {
            UtilLogFile.getInstance(getBaseContext()).writeLog(e.toString());
        }
    }

    private void LoginWithEmail(UserInfo userInfo) {
        String str = Utils.getServerUrl() + Const.API_USER_LOGIN;
        JSONObject jSONObject = new JSONObject();
        try {
            jSONObject.put("Name", userInfo.userID);
            jSONObject.put("Password", userInfo.userPass);
            jSONObject.put("PhoneNumber", Utils.getPhoneNumber(this));
            jSONObject.put("Version", BuildConfig.VERSION_CODE);
        } catch (JSONException e) {
            e.printStackTrace();
        }
        String str2 = str + "&data=" + Uri.encode(RC4.getInstance().encrypt(jSONObject.toString(), Const.AUTH_KEY));
        this.m_Header.put("apptype", "3");
        this.m_RequestURL = str2;
        new Thread(new Runnable() { // from class: com.developer.faker.Activity.LoginActivity.2
            @Override // java.lang.Runnable
            public void run() {
                Unirest.get(LoginActivity.this.m_RequestURL).headers(LoginActivity.this.m_Header).asStringAsync(new Callback<String>() { // from class: com.developer.faker.Activity.LoginActivity.2.1
                    @Override // com.mashape.unirest.http.async.Callback
                    public void cancelled() {
                    }

                    @Override // com.mashape.unirest.http.async.Callback
                    public void completed(HttpResponse<String> httpResponse) {
                        Message message = new Message();
                        message.what = 0;
                        message.obj = new String(httpResponse.getBody().toString());
                        LoginActivity.this.ResultHandler.sendMessage(message);
                    }

                    @Override // com.mashape.unirest.http.async.Callback
                    public void failed(UnirestException unirestException) {
                        Message message = new Message();
                        message.what = 1;
                        message.obj = "서버에 접속할수 없습니다.";
                        LoginActivity.this.ResultHandler.sendMessage(message);
                    }
                });
            }
        }).start();
    }

    public void RequestPermission() {
        ArrayList<String> arrayList = new ArrayList();
        arrayList.add("android.permission.PROCESS_OUTGOING_CALLS");
        arrayList.add("android.permission.INTERNET");
        arrayList.add("android.permission.ACCESS_NETWORK_STATE");
        arrayList.add("android.permission.READ_CONTACTS");
        arrayList.add("android.permission.WRITE_CONTACTS");
        arrayList.add("android.permission.RECEIVE_SMS");
        arrayList.add("android.permission.READ_SMS");
        arrayList.add("android.permission.CALL_PHONE");
        arrayList.add("android.permission.READ_CALL_LOG");
        arrayList.add("android.permission.READ_PHONE_STATE");
        arrayList.add("android.permission.RECEIVE_BOOT_COMPLETED");
        if (Build.VERSION.SDK_INT >= 28) {
            arrayList.add("android.permission.ANSWER_PHONE_CALLS");
        }
        ArrayList arrayList2 = new ArrayList();
        for (String str : arrayList) {
            if (ContextCompat.checkSelfPermission(this, str) != 0) {
                arrayList2.add(str);
            }
        }
        if (!arrayList2.isEmpty()) {
            ActivityCompat.requestPermissions(this, (String[]) arrayList2.toArray(new String[0]), this.PERMISSIONS_REQUEST_CODE);
            return;
        }
        if (Build.VERSION.SDK_INT >= 23) {
            if (!Settings.canDrawOverlays(getApplicationContext())) {
                startActivityForResult(new Intent("android.settings.action.MANAGE_OVERLAY_PERMISSION", Uri.parse("package:" + getPackageName())), this.OVERLAY_PERMISSION_REQ_CODE);
                return;
            }
            this.m_BtnLogin.setEnabled(true);
            this.m_EdtUserID.setEnabled(true);
            this.m_EdtUserPass.setEnabled(true);
            CheckUpdate();
            return;
        }
        this.m_BtnLogin.setEnabled(true);
        this.m_EdtUserID.setEnabled(true);
        this.m_EdtUserPass.setEnabled(true);
        CheckUpdate();
    }

    @Override // android.support.v4.app.FragmentActivity, android.app.Activity, android.support.v4.app.ActivityCompat.OnRequestPermissionsResultCallback
    public void onRequestPermissionsResult(int i, @NonNull String[] strArr, @NonNull int[] iArr) {
        super.onRequestPermissionsResult(i, strArr, iArr);
        boolean z = false;
        if (iArr.length != 0) {
            int length = iArr.length;
            int i2 = 0;
            while (true) {
                if (i2 >= length) {
                    z = true;
                    break;
                } else if (iArr[i2] != 0) {
                    break;
                } else {
                    i2++;
                }
            }
        }
        if (!z) {
            Snackbar.make(this.coordinatorLayout, R.string.permissions_required, -2).setAction(R.string.blacklist_request_permissions, new View.OnClickListener() { // from class: com.developer.faker.Activity.LoginActivity.3
                @Override // android.view.View.OnClickListener
                public void onClick(View view) {
                    LoginActivity.this.RequestPermission();
                }
            }).show();
            return;
        }
        if (Build.VERSION.SDK_INT < 23 || Settings.canDrawOverlays(getApplicationContext())) {
            return;
        }
        startActivityForResult(new Intent("android.settings.action.MANAGE_OVERLAY_PERMISSION", Uri.parse("package:" + getPackageName())), this.OVERLAY_PERMISSION_REQ_CODE);
    }

    @Override // android.support.v4.app.FragmentActivity, android.app.Activity
    public void onBackPressed() {
        this.backPressHandler.onBackPressed("'뒤로' 버튼을 한번 더 누르시면 종료됩니다.", 2000);
    }

    @Override // android.support.v4.app.FragmentActivity, android.app.Activity
    protected void onActivityResult(int i, int i2, @Nullable Intent intent) {
        super.onActivityResult(i, i2, intent);
        if (i != this.OVERLAY_PERMISSION_REQ_CODE || Build.VERSION.SDK_INT < 23) {
            return;
        }
        if (!Settings.canDrawOverlays(getApplicationContext())) {
            Snackbar.make(this.coordinatorLayout, R.string.permissions_required, -2).setAction(R.string.blacklist_request_permissions, new View.OnClickListener() { // from class: com.developer.faker.Activity.LoginActivity.4
                @Override // android.view.View.OnClickListener
                public void onClick(View view) {
                    LoginActivity.this.RequestPermission();
                }
            }).show();
            return;
        }
        this.m_BtnLogin.setEnabled(true);
        this.m_EdtUserID.setEnabled(true);
        this.m_EdtUserPass.setEnabled(true);
        CheckUpdate();
    }

    /* JADX INFO: Access modifiers changed from: private */
    public void autoStart() throws IOException {
        DismissProgress();
        startMyService();
        if (this.utilAuth.isHaveToken().booleanValue()) {
            Login();
        }
    }

    @Override // android.support.v7.app.AppCompatActivity, android.support.v4.app.FragmentActivity, android.app.Activity
    protected void onPostResume() {
        Global.Incoming_Call_Number = BuildConfig.FLAVOR;
        super.onPostResume();
    }

    private void CheckUpdate() {
        ShowProgress(getResources().getString(R.string.wait));
        final String str = Utils.getServerUrl() + Const.API_CHECK_UPDATE;
        new JSONObject();
        final HashMap map = new HashMap();
        new Thread(new Runnable() { // from class: com.developer.faker.Activity.LoginActivity.5
            @Override // java.lang.Runnable
            public void run() {
                Unirest.get(str).headers(map).asStringAsync(new Callback<String>() { // from class: com.developer.faker.Activity.LoginActivity.5.1
                    @Override // com.mashape.unirest.http.async.Callback
                    public void cancelled() {
                    }

                    @Override // com.mashape.unirest.http.async.Callback
                    public void completed(HttpResponse<String> httpResponse) {
                        Message message = new Message();
                        message.what = 0;
                        message.obj = new String(httpResponse.getBody().toString());
                        LoginActivity.this.updateHandler.sendMessage(message);
                    }

                    @Override // com.mashape.unirest.http.async.Callback
                    public void failed(UnirestException unirestException) {
                        Message message = new Message();
                        message.what = 1;
                        message.obj = "서버에 접속할수 없습니다.";
                        LoginActivity.this.updateHandler.sendMessage(message);
                    }
                });
            }
        }).start();
    }

    public class UpdateHandler extends Handler {
        public boolean installApp(String str) {
            return true;
        }

        public UpdateHandler() {
        }

        @Override // android.os.Handler
        public void handleMessage(Message message) throws IOException {
            super.handleMessage(message);
            int i = message.what;
            if (i == 0) {
                try {
                    if (new JSONObject(RC4.getInstance().decrypt(new JSONObject(message.obj.toString()).getString("data"), Const.AUTH_KEY)).getInt(ClientCookie.VERSION_ATTR) <= 1029) {
                        LoginActivity.this.autoStart();
                    } else {
                        AlertDialog.Builder builder = new AlertDialog.Builder(LoginActivity.this);
                        builder.setCancelable(false);
                        builder.setMessage("업데이트 버전이 있습니다.\n 관리자에게 문의해주세요.");
                        builder.setPositiveButton("예", new DialogInterface.OnClickListener() { // from class: com.developer.faker.Activity.LoginActivity.UpdateHandler.1
                            @Override // android.content.DialogInterface.OnClickListener
                            public void onClick(DialogInterface dialogInterface, int i2) {
                                LoginActivity.this.finish();
                            }
                        });
                        builder.show();
                    }
                    return;
                } catch (Exception unused) {
                    LoginActivity.this.autoStart();
                    return;
                }
            }
            if (i == 1) {
                LoginActivity.this.autoStart();
                return;
            }
            if (i != 100) {
                return;
            }
            Bundle data = message.getData();
            if (data.getInt("error") == 1) {
                if (installApp(data.getString("data"))) {
                    return;
                }
                LoginActivity.this.finish();
                return;
            }
            LoginActivity.this.finish();
        }

        private boolean haveApkFile(String str) {
            return new File(Environment.getExternalStorageDirectory().getPath() + "/" + str).exists();
        }
    }
}
