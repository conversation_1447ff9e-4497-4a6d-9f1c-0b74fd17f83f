package com.developer.faker;

/* loaded from: classes.dex */
public final class R {

    public static final class anim {
        public static final int abc_fade_in = 0x7f010000;
        public static final int abc_fade_out = 0x7f010001;
        public static final int abc_grow_fade_in_from_bottom = 0x7f010002;
        public static final int abc_popup_enter = 0x7f010003;
        public static final int abc_popup_exit = 0x7f010004;
        public static final int abc_shrink_fade_out_from_bottom = 0x7f010005;
        public static final int abc_slide_in_bottom = 0x7f010006;
        public static final int abc_slide_in_top = 0x7f010007;
        public static final int abc_slide_out_bottom = 0x7f010008;
        public static final int abc_slide_out_top = 0x7f010009;
        public static final int abc_tooltip_enter = 0x7f01000a;
        public static final int abc_tooltip_exit = 0x7f01000b;
        public static final int alpha = 0x7f01000c;
        public static final int bounce = 0x7f01000d;
        public static final int design_bottom_sheet_slide_in = 0x7f01000e;
        public static final int design_bottom_sheet_slide_out = 0x7f01000f;
        public static final int design_snackbar_in = 0x7f010010;
        public static final int design_snackbar_out = 0x7f010011;
        public static final int slidein = 0x7f010012;
        public static final int slideout = 0x7f010013;
        public static final int translate = 0x7f010014;
    }

    public static final class animator {
        public static final int design_appbar_state_list_animator = 0x7f020000;
        public static final int design_fab_hide_motion_spec = 0x7f020001;
        public static final int design_fab_show_motion_spec = 0x7f020002;
        public static final int mtrl_btn_state_list_anim = 0x7f020003;
        public static final int mtrl_btn_unelevated_state_list_anim = 0x7f020004;
        public static final int mtrl_chip_state_list_anim = 0x7f020005;
        public static final int mtrl_fab_hide_motion_spec = 0x7f020006;
        public static final int mtrl_fab_show_motion_spec = 0x7f020007;
        public static final int mtrl_fab_transformation_sheet_collapse_spec = 0x7f020008;
        public static final int mtrl_fab_transformation_sheet_expand_spec = 0x7f020009;
    }

    public static final class array {
        public static final int drawer_items = 0x7f030000;
    }

    public static final class attr {
        public static final int actionBarDivider = 0x7f040000;
        public static final int actionBarItemBackground = 0x7f040001;
        public static final int actionBarPopupTheme = 0x7f040002;
        public static final int actionBarSize = 0x7f040003;
        public static final int actionBarSplitStyle = 0x7f040004;
        public static final int actionBarStyle = 0x7f040005;
        public static final int actionBarTabBarStyle = 0x7f040006;
        public static final int actionBarTabStyle = 0x7f040007;
        public static final int actionBarTabTextStyle = 0x7f040008;
        public static final int actionBarTheme = 0x7f040009;
        public static final int actionBarWidgetTheme = 0x7f04000a;
        public static final int actionButtonStyle = 0x7f04000b;
        public static final int actionDropDownStyle = 0x7f04000c;
        public static final int actionLayout = 0x7f04000d;
        public static final int actionMenuTextAppearance = 0x7f04000e;
        public static final int actionMenuTextColor = 0x7f04000f;
        public static final int actionModeBackground = 0x7f040010;
        public static final int actionModeCloseButtonStyle = 0x7f040011;
        public static final int actionModeCloseDrawable = 0x7f040012;
        public static final int actionModeCopyDrawable = 0x7f040013;
        public static final int actionModeCutDrawable = 0x7f040014;
        public static final int actionModeFindDrawable = 0x7f040015;
        public static final int actionModePasteDrawable = 0x7f040016;
        public static final int actionModePopupWindowStyle = 0x7f040017;
        public static final int actionModeSelectAllDrawable = 0x7f040018;
        public static final int actionModeShareDrawable = 0x7f040019;
        public static final int actionModeSplitBackground = 0x7f04001a;
        public static final int actionModeStyle = 0x7f04001b;
        public static final int actionModeWebSearchDrawable = 0x7f04001c;
        public static final int actionOverflowButtonStyle = 0x7f04001d;
        public static final int actionOverflowMenuStyle = 0x7f04001e;
        public static final int actionProviderClass = 0x7f04001f;
        public static final int actionViewClass = 0x7f040020;
        public static final int activityChooserViewStyle = 0x7f040021;
        public static final int alertDialogButtonGroupStyle = 0x7f040022;
        public static final int alertDialogCenterButtons = 0x7f040023;
        public static final int alertDialogStyle = 0x7f040024;
        public static final int alertDialogTheme = 0x7f040025;
        public static final int allowStacking = 0x7f040026;
        public static final int alpha = 0x7f040027;
        public static final int alphabeticModifiers = 0x7f040028;
        public static final int arrowHeadLength = 0x7f040029;
        public static final int arrowShaftLength = 0x7f04002a;
        public static final int autoCompleteTextViewStyle = 0x7f04002b;
        public static final int autoSizeMaxTextSize = 0x7f04002c;
        public static final int autoSizeMinTextSize = 0x7f04002d;
        public static final int autoSizePresetSizes = 0x7f04002e;
        public static final int autoSizeStepGranularity = 0x7f04002f;
        public static final int autoSizeTextType = 0x7f040030;
        public static final int background = 0x7f040031;
        public static final int backgroundSplit = 0x7f040032;
        public static final int backgroundStacked = 0x7f040033;
        public static final int backgroundTint = 0x7f040034;
        public static final int backgroundTintMode = 0x7f040035;
        public static final int barLength = 0x7f040036;
        public static final int behavior_autoHide = 0x7f040037;
        public static final int behavior_fitToContents = 0x7f040038;
        public static final int behavior_hideable = 0x7f040039;
        public static final int behavior_overlapTop = 0x7f04003a;
        public static final int behavior_peekHeight = 0x7f04003b;
        public static final int behavior_skipCollapsed = 0x7f04003c;
        public static final int borderWidth = 0x7f04003d;
        public static final int borderlessButtonStyle = 0x7f04003e;
        public static final int bottomAppBarStyle = 0x7f04003f;
        public static final int bottomNavigationStyle = 0x7f040040;
        public static final int bottomSheetDialogTheme = 0x7f040041;
        public static final int bottomSheetStyle = 0x7f040042;
        public static final int boxBackgroundColor = 0x7f040043;
        public static final int boxBackgroundMode = 0x7f040044;
        public static final int boxCollapsedPaddingTop = 0x7f040045;
        public static final int boxCornerRadiusBottomEnd = 0x7f040046;
        public static final int boxCornerRadiusBottomStart = 0x7f040047;
        public static final int boxCornerRadiusTopEnd = 0x7f040048;
        public static final int boxCornerRadiusTopStart = 0x7f040049;
        public static final int boxStrokeColor = 0x7f04004a;
        public static final int boxStrokeWidth = 0x7f04004b;
        public static final int buttonBarButtonStyle = 0x7f04004c;
        public static final int buttonBarNegativeButtonStyle = 0x7f04004d;
        public static final int buttonBarNeutralButtonStyle = 0x7f04004e;
        public static final int buttonBarPositiveButtonStyle = 0x7f04004f;
        public static final int buttonBarStyle = 0x7f040050;
        public static final int buttonGravity = 0x7f040051;
        public static final int buttonIconDimen = 0x7f040052;
        public static final int buttonPanelSideLayout = 0x7f040053;
        public static final int buttonStyle = 0x7f040054;
        public static final int buttonStyleSmall = 0x7f040055;
        public static final int buttonTint = 0x7f040056;
        public static final int buttonTintMode = 0x7f040057;
        public static final int cardBackgroundColor = 0x7f040058;
        public static final int cardCornerRadius = 0x7f040059;
        public static final int cardElevation = 0x7f04005a;
        public static final int cardMaxElevation = 0x7f04005b;
        public static final int cardPreventCornerOverlap = 0x7f04005c;
        public static final int cardUseCompatPadding = 0x7f04005d;
        public static final int cardViewStyle = 0x7f04005e;
        public static final int checkboxStyle = 0x7f04005f;
        public static final int checkedChip = 0x7f040060;
        public static final int checkedIcon = 0x7f040061;
        public static final int checkedIconEnabled = 0x7f040062;
        public static final int checkedIconVisible = 0x7f040063;
        public static final int checkedTextViewStyle = 0x7f040064;
        public static final int chipBackgroundColor = 0x7f040065;
        public static final int chipCornerRadius = 0x7f040066;
        public static final int chipEndPadding = 0x7f040067;
        public static final int chipGroupStyle = 0x7f040068;
        public static final int chipIcon = 0x7f040069;
        public static final int chipIconEnabled = 0x7f04006a;
        public static final int chipIconSize = 0x7f04006b;
        public static final int chipIconTint = 0x7f04006c;
        public static final int chipIconVisible = 0x7f04006d;
        public static final int chipMinHeight = 0x7f04006e;
        public static final int chipSpacing = 0x7f04006f;
        public static final int chipSpacingHorizontal = 0x7f040070;
        public static final int chipSpacingVertical = 0x7f040071;
        public static final int chipStandaloneStyle = 0x7f040072;
        public static final int chipStartPadding = 0x7f040073;
        public static final int chipStrokeColor = 0x7f040074;
        public static final int chipStrokeWidth = 0x7f040075;
        public static final int chipStyle = 0x7f040076;
        public static final int closeIcon = 0x7f040077;
        public static final int closeIconEnabled = 0x7f040078;
        public static final int closeIconEndPadding = 0x7f040079;
        public static final int closeIconSize = 0x7f04007a;
        public static final int closeIconStartPadding = 0x7f04007b;
        public static final int closeIconTint = 0x7f04007c;
        public static final int closeIconVisible = 0x7f04007d;
        public static final int closeItemLayout = 0x7f04007e;
        public static final int collapseContentDescription = 0x7f04007f;
        public static final int collapseIcon = 0x7f040080;
        public static final int collapsedTitleGravity = 0x7f040081;
        public static final int collapsedTitleTextAppearance = 0x7f040082;
        public static final int color = 0x7f040083;
        public static final int colorAccent = 0x7f040084;
        public static final int colorBackgroundFloating = 0x7f040085;
        public static final int colorButtonNormal = 0x7f040086;
        public static final int colorControlActivated = 0x7f040087;
        public static final int colorControlHighlight = 0x7f040088;
        public static final int colorControlNormal = 0x7f040089;
        public static final int colorError = 0x7f04008a;
        public static final int colorPrimary = 0x7f04008b;
        public static final int colorPrimaryDark = 0x7f04008c;
        public static final int colorSecondary = 0x7f04008d;
        public static final int colorSwitchThumbNormal = 0x7f04008e;
        public static final int commitIcon = 0x7f04008f;
        public static final int contentDescription = 0x7f040090;
        public static final int contentInsetEnd = 0x7f040091;
        public static final int contentInsetEndWithActions = 0x7f040092;
        public static final int contentInsetLeft = 0x7f040093;
        public static final int contentInsetRight = 0x7f040094;
        public static final int contentInsetStart = 0x7f040095;
        public static final int contentInsetStartWithNavigation = 0x7f040096;
        public static final int contentPadding = 0x7f040097;
        public static final int contentPaddingBottom = 0x7f040098;
        public static final int contentPaddingLeft = 0x7f040099;
        public static final int contentPaddingRight = 0x7f04009a;
        public static final int contentPaddingTop = 0x7f04009b;
        public static final int contentScrim = 0x7f04009c;
        public static final int controlBackground = 0x7f04009d;
        public static final int coordinatorLayoutStyle = 0x7f04009e;
        public static final int cornerRadius = 0x7f04009f;
        public static final int counterEnabled = 0x7f0400a0;
        public static final int counterMaxLength = 0x7f0400a1;
        public static final int counterOverflowTextAppearance = 0x7f0400a2;
        public static final int counterTextAppearance = 0x7f0400a3;
        public static final int customNavigationLayout = 0x7f0400a4;
        public static final int defaultQueryHint = 0x7f0400a5;
        public static final int dialogCornerRadius = 0x7f0400a6;
        public static final int dialogPreferredPadding = 0x7f0400a7;
        public static final int dialogTheme = 0x7f0400a8;
        public static final int displayOptions = 0x7f0400a9;
        public static final int divider = 0x7f0400aa;
        public static final int dividerHorizontal = 0x7f0400ab;
        public static final int dividerPadding = 0x7f0400ac;
        public static final int dividerVertical = 0x7f0400ad;
        public static final int drawableSize = 0x7f0400ae;
        public static final int drawerArrowStyle = 0x7f0400af;
        public static final int dropDownListViewStyle = 0x7f0400b0;
        public static final int dropdownListPreferredItemHeight = 0x7f0400b1;
        public static final int editTextBackground = 0x7f0400b2;
        public static final int editTextColor = 0x7f0400b3;
        public static final int editTextStyle = 0x7f0400b4;
        public static final int elevation = 0x7f0400b5;
        public static final int enforceMaterialTheme = 0x7f0400b6;
        public static final int enforceTextAppearance = 0x7f0400b7;
        public static final int errorEnabled = 0x7f0400b8;
        public static final int errorTextAppearance = 0x7f0400b9;
        public static final int expandActivityOverflowButtonDrawable = 0x7f0400ba;
        public static final int expanded = 0x7f0400bb;
        public static final int expandedTitleGravity = 0x7f0400bc;
        public static final int expandedTitleMargin = 0x7f0400bd;
        public static final int expandedTitleMarginBottom = 0x7f0400be;
        public static final int expandedTitleMarginEnd = 0x7f0400bf;
        public static final int expandedTitleMarginStart = 0x7f0400c0;
        public static final int expandedTitleMarginTop = 0x7f0400c1;
        public static final int expandedTitleTextAppearance = 0x7f0400c2;
        public static final int fabAlignmentMode = 0x7f0400c3;
        public static final int fabCradleMargin = 0x7f0400c4;
        public static final int fabCradleRoundedCornerRadius = 0x7f0400c5;
        public static final int fabCradleVerticalOffset = 0x7f0400c6;
        public static final int fabCustomSize = 0x7f0400c7;
        public static final int fabSize = 0x7f0400c8;
        public static final int fastScrollEnabled = 0x7f0400c9;
        public static final int fastScrollHorizontalThumbDrawable = 0x7f0400ca;
        public static final int fastScrollHorizontalTrackDrawable = 0x7f0400cb;
        public static final int fastScrollVerticalThumbDrawable = 0x7f0400cc;
        public static final int fastScrollVerticalTrackDrawable = 0x7f0400cd;
        public static final int firstBaselineToTopHeight = 0x7f0400ce;
        public static final int floatingActionButtonStyle = 0x7f0400cf;
        public static final int font = 0x7f0400d0;
        public static final int fontFamily = 0x7f0400d1;
        public static final int fontProviderAuthority = 0x7f0400d2;
        public static final int fontProviderCerts = 0x7f0400d3;
        public static final int fontProviderFetchStrategy = 0x7f0400d4;
        public static final int fontProviderFetchTimeout = 0x7f0400d5;
        public static final int fontProviderPackage = 0x7f0400d6;
        public static final int fontProviderQuery = 0x7f0400d7;
        public static final int fontStyle = 0x7f0400d8;
        public static final int fontVariationSettings = 0x7f0400d9;
        public static final int fontWeight = 0x7f0400da;
        public static final int foregroundInsidePadding = 0x7f0400db;
        public static final int gapBetweenBars = 0x7f0400dc;
        public static final int goIcon = 0x7f0400dd;
        public static final int headerLayout = 0x7f0400de;
        public static final int height = 0x7f0400df;
        public static final int helperText = 0x7f0400e0;
        public static final int helperTextEnabled = 0x7f0400e1;
        public static final int helperTextTextAppearance = 0x7f0400e2;
        public static final int hideMotionSpec = 0x7f0400e3;
        public static final int hideOnContentScroll = 0x7f0400e4;
        public static final int hideOnScroll = 0x7f0400e5;
        public static final int hintAnimationEnabled = 0x7f0400e6;
        public static final int hintEnabled = 0x7f0400e7;
        public static final int hintTextAppearance = 0x7f0400e8;
        public static final int homeAsUpIndicator = 0x7f0400e9;
        public static final int homeLayout = 0x7f0400ea;
        public static final int hoveredFocusedTranslationZ = 0x7f0400eb;
        public static final int icon = 0x7f0400ec;
        public static final int iconEndPadding = 0x7f0400ed;
        public static final int iconGravity = 0x7f0400ee;
        public static final int iconPadding = 0x7f0400ef;
        public static final int iconSize = 0x7f0400f0;
        public static final int iconStartPadding = 0x7f0400f1;
        public static final int iconTint = 0x7f0400f2;
        public static final int iconTintMode = 0x7f0400f3;
        public static final int iconifiedByDefault = 0x7f0400f4;
        public static final int imageButtonStyle = 0x7f0400f5;
        public static final int indeterminateProgressStyle = 0x7f0400f6;
        public static final int initialActivityCount = 0x7f0400f7;
        public static final int insetForeground = 0x7f0400f8;
        public static final int isLightTheme = 0x7f0400f9;
        public static final int itemBackground = 0x7f0400fa;
        public static final int itemHorizontalPadding = 0x7f0400fb;
        public static final int itemHorizontalTranslationEnabled = 0x7f0400fc;
        public static final int itemIconPadding = 0x7f0400fd;
        public static final int itemIconSize = 0x7f0400fe;
        public static final int itemIconTint = 0x7f0400ff;
        public static final int itemPadding = 0x7f040100;
        public static final int itemSpacing = 0x7f040101;
        public static final int itemTextAppearance = 0x7f040102;
        public static final int itemTextAppearanceActive = 0x7f040103;
        public static final int itemTextAppearanceInactive = 0x7f040104;
        public static final int itemTextColor = 0x7f040105;
        public static final int keylines = 0x7f040106;
        public static final int labelVisibilityMode = 0x7f040107;
        public static final int lastBaselineToBottomHeight = 0x7f040108;
        public static final int layout = 0x7f040109;
        public static final int layoutManager = 0x7f04010a;
        public static final int layout_anchor = 0x7f04010b;
        public static final int layout_anchorGravity = 0x7f04010c;
        public static final int layout_behavior = 0x7f04010d;
        public static final int layout_collapseMode = 0x7f04010e;
        public static final int layout_collapseParallaxMultiplier = 0x7f04010f;
        public static final int layout_dodgeInsetEdges = 0x7f040110;
        public static final int layout_insetEdge = 0x7f040111;
        public static final int layout_keyline = 0x7f040112;
        public static final int layout_scrollFlags = 0x7f040113;
        public static final int layout_scrollInterpolator = 0x7f040114;
        public static final int liftOnScroll = 0x7f040115;
        public static final int lineHeight = 0x7f040116;
        public static final int lineSpacing = 0x7f040117;
        public static final int listChoiceBackgroundIndicator = 0x7f040118;
        public static final int listDividerAlertDialog = 0x7f040119;
        public static final int listItemLayout = 0x7f04011a;
        public static final int listLayout = 0x7f04011b;
        public static final int listMenuViewStyle = 0x7f04011c;
        public static final int listPopupWindowStyle = 0x7f04011d;
        public static final int listPreferredItemHeight = 0x7f04011e;
        public static final int listPreferredItemHeightLarge = 0x7f04011f;
        public static final int listPreferredItemHeightSmall = 0x7f040120;
        public static final int listPreferredItemPaddingLeft = 0x7f040121;
        public static final int listPreferredItemPaddingRight = 0x7f040122;
        public static final int logo = 0x7f040123;
        public static final int logoDescription = 0x7f040124;
        public static final int materialButtonStyle = 0x7f040125;
        public static final int materialCardViewStyle = 0x7f040126;
        public static final int maxActionInlineWidth = 0x7f040127;
        public static final int maxButtonHeight = 0x7f040128;
        public static final int maxImageSize = 0x7f040129;
        public static final int measureWithLargestChild = 0x7f04012a;
        public static final int menu = 0x7f04012b;
        public static final int multiChoiceItemLayout = 0x7f04012c;
        public static final int navigationContentDescription = 0x7f04012d;
        public static final int navigationIcon = 0x7f04012e;
        public static final int navigationMode = 0x7f04012f;
        public static final int navigationViewStyle = 0x7f040130;
        public static final int numericModifiers = 0x7f040131;
        public static final int overlapAnchor = 0x7f040132;
        public static final int paddingBottomNoButtons = 0x7f040133;
        public static final int paddingEnd = 0x7f040134;
        public static final int paddingStart = 0x7f040135;
        public static final int paddingTopNoTitle = 0x7f040136;
        public static final int panelBackground = 0x7f040137;
        public static final int panelMenuListTheme = 0x7f040138;
        public static final int panelMenuListWidth = 0x7f040139;
        public static final int passwordToggleContentDescription = 0x7f04013a;
        public static final int passwordToggleDrawable = 0x7f04013b;
        public static final int passwordToggleEnabled = 0x7f04013c;
        public static final int passwordToggleTint = 0x7f04013d;
        public static final int passwordToggleTintMode = 0x7f04013e;
        public static final int popupMenuStyle = 0x7f04013f;
        public static final int popupTheme = 0x7f040140;
        public static final int popupWindowStyle = 0x7f040141;
        public static final int preserveIconSpacing = 0x7f040142;
        public static final int pressedTranslationZ = 0x7f040143;
        public static final int progressBarPadding = 0x7f040144;
        public static final int progressBarStyle = 0x7f040145;
        public static final int queryBackground = 0x7f040146;
        public static final int queryHint = 0x7f040147;
        public static final int radioButtonStyle = 0x7f040148;
        public static final int ratingBarStyle = 0x7f040149;
        public static final int ratingBarStyleIndicator = 0x7f04014a;
        public static final int ratingBarStyleSmall = 0x7f04014b;
        public static final int reverseLayout = 0x7f04014c;
        public static final int rippleColor = 0x7f04014d;
        public static final int scrimAnimationDuration = 0x7f04014e;
        public static final int scrimBackground = 0x7f04014f;
        public static final int scrimVisibleHeightTrigger = 0x7f040150;
        public static final int searchHintIcon = 0x7f040151;
        public static final int searchIcon = 0x7f040152;
        public static final int searchViewStyle = 0x7f040153;
        public static final int seekBarStyle = 0x7f040154;
        public static final int selectableItemBackground = 0x7f040155;
        public static final int selectableItemBackgroundBorderless = 0x7f040156;
        public static final int showAsAction = 0x7f040157;
        public static final int showDividers = 0x7f040158;
        public static final int showMotionSpec = 0x7f040159;
        public static final int showText = 0x7f04015a;
        public static final int showTitle = 0x7f04015b;
        public static final int singleChoiceItemLayout = 0x7f04015c;
        public static final int singleLine = 0x7f04015d;
        public static final int singleSelection = 0x7f04015e;
        public static final int snackbarButtonStyle = 0x7f04015f;
        public static final int snackbarStyle = 0x7f040160;
        public static final int spanCount = 0x7f040161;
        public static final int spinBars = 0x7f040162;
        public static final int spinnerDropDownItemStyle = 0x7f040163;
        public static final int spinnerStyle = 0x7f040164;
        public static final int splitTrack = 0x7f040165;
        public static final int srcCompat = 0x7f040166;
        public static final int stackFromEnd = 0x7f040167;
        public static final int state_above_anchor = 0x7f040168;
        public static final int state_collapsed = 0x7f040169;
        public static final int state_collapsible = 0x7f04016a;
        public static final int state_liftable = 0x7f04016b;
        public static final int state_lifted = 0x7f04016c;
        public static final int statusBarBackground = 0x7f04016d;
        public static final int statusBarScrim = 0x7f04016e;
        public static final int strokeColor = 0x7f04016f;
        public static final int strokeWidth = 0x7f040170;
        public static final int subMenuArrow = 0x7f040171;
        public static final int submitBackground = 0x7f040172;
        public static final int subtitle = 0x7f040173;
        public static final int subtitleTextAppearance = 0x7f040174;
        public static final int subtitleTextColor = 0x7f040175;
        public static final int subtitleTextStyle = 0x7f040176;
        public static final int suggestionRowLayout = 0x7f040177;
        public static final int switchMinWidth = 0x7f040178;
        public static final int switchPadding = 0x7f040179;
        public static final int switchStyle = 0x7f04017a;
        public static final int switchTextAppearance = 0x7f04017b;
        public static final int tabBackground = 0x7f04017c;
        public static final int tabContentStart = 0x7f04017d;
        public static final int tabGravity = 0x7f04017e;
        public static final int tabIconTint = 0x7f04017f;
        public static final int tabIconTintMode = 0x7f040180;
        public static final int tabIndicator = 0x7f040181;
        public static final int tabIndicatorAnimationDuration = 0x7f040182;
        public static final int tabIndicatorColor = 0x7f040183;
        public static final int tabIndicatorFullWidth = 0x7f040184;
        public static final int tabIndicatorGravity = 0x7f040185;
        public static final int tabIndicatorHeight = 0x7f040186;
        public static final int tabInlineLabel = 0x7f040187;
        public static final int tabMaxWidth = 0x7f040188;
        public static final int tabMinWidth = 0x7f040189;
        public static final int tabMode = 0x7f04018a;
        public static final int tabPadding = 0x7f04018b;
        public static final int tabPaddingBottom = 0x7f04018c;
        public static final int tabPaddingEnd = 0x7f04018d;
        public static final int tabPaddingStart = 0x7f04018e;
        public static final int tabPaddingTop = 0x7f04018f;
        public static final int tabRippleColor = 0x7f040190;
        public static final int tabSelectedTextColor = 0x7f040191;
        public static final int tabStyle = 0x7f040192;
        public static final int tabTextAppearance = 0x7f040193;
        public static final int tabTextColor = 0x7f040194;
        public static final int tabUnboundedRipple = 0x7f040195;
        public static final int textAllCaps = 0x7f040196;
        public static final int textAppearanceBody1 = 0x7f040197;
        public static final int textAppearanceBody2 = 0x7f040198;
        public static final int textAppearanceButton = 0x7f040199;
        public static final int textAppearanceCaption = 0x7f04019a;
        public static final int textAppearanceHeadline1 = 0x7f04019b;
        public static final int textAppearanceHeadline2 = 0x7f04019c;
        public static final int textAppearanceHeadline3 = 0x7f04019d;
        public static final int textAppearanceHeadline4 = 0x7f04019e;
        public static final int textAppearanceHeadline5 = 0x7f04019f;
        public static final int textAppearanceHeadline6 = 0x7f0401a0;
        public static final int textAppearanceLargePopupMenu = 0x7f0401a1;
        public static final int textAppearanceListItem = 0x7f0401a2;
        public static final int textAppearanceListItemSecondary = 0x7f0401a3;
        public static final int textAppearanceListItemSmall = 0x7f0401a4;
        public static final int textAppearanceOverline = 0x7f0401a5;
        public static final int textAppearancePopupMenuHeader = 0x7f0401a6;
        public static final int textAppearanceSearchResultSubtitle = 0x7f0401a7;
        public static final int textAppearanceSearchResultTitle = 0x7f0401a8;
        public static final int textAppearanceSmallPopupMenu = 0x7f0401a9;
        public static final int textAppearanceSubtitle1 = 0x7f0401aa;
        public static final int textAppearanceSubtitle2 = 0x7f0401ab;
        public static final int textColorAlertDialogListItem = 0x7f0401ac;
        public static final int textColorSearchUrl = 0x7f0401ad;
        public static final int textEndPadding = 0x7f0401ae;
        public static final int textInputStyle = 0x7f0401af;
        public static final int textStartPadding = 0x7f0401b0;
        public static final int theme = 0x7f0401b1;
        public static final int thickness = 0x7f0401b2;
        public static final int thumbTextPadding = 0x7f0401b3;
        public static final int thumbTint = 0x7f0401b4;
        public static final int thumbTintMode = 0x7f0401b5;
        public static final int tickMark = 0x7f0401b6;
        public static final int tickMarkTint = 0x7f0401b7;
        public static final int tickMarkTintMode = 0x7f0401b8;
        public static final int tint = 0x7f0401b9;
        public static final int tintMode = 0x7f0401ba;
        public static final int title = 0x7f0401bb;
        public static final int titleEnabled = 0x7f0401bc;
        public static final int titleMargin = 0x7f0401bd;
        public static final int titleMarginBottom = 0x7f0401be;
        public static final int titleMarginEnd = 0x7f0401bf;
        public static final int titleMarginStart = 0x7f0401c0;
        public static final int titleMarginTop = 0x7f0401c1;

        @Deprecated
        public static final int titleMargins = 0x7f0401c2;
        public static final int titleTextAppearance = 0x7f0401c3;
        public static final int titleTextColor = 0x7f0401c4;
        public static final int titleTextStyle = 0x7f0401c5;
        public static final int toolbarId = 0x7f0401c6;
        public static final int toolbarNavigationButtonStyle = 0x7f0401c7;
        public static final int toolbarStyle = 0x7f0401c8;
        public static final int tooltipForegroundColor = 0x7f0401c9;
        public static final int tooltipFrameBackground = 0x7f0401ca;
        public static final int tooltipText = 0x7f0401cb;
        public static final int track = 0x7f0401cc;
        public static final int trackTint = 0x7f0401cd;
        public static final int trackTintMode = 0x7f0401ce;
        public static final int ttcIndex = 0x7f0401cf;
        public static final int useCompatPadding = 0x7f0401d0;
        public static final int viewInflaterClass = 0x7f0401d1;
        public static final int voiceIcon = 0x7f0401d2;
        public static final int windowActionBar = 0x7f0401d3;
        public static final int windowActionBarOverlay = 0x7f0401d4;
        public static final int windowActionModeOverlay = 0x7f0401d5;
        public static final int windowFixedHeightMajor = 0x7f0401d6;
        public static final int windowFixedHeightMinor = 0x7f0401d7;
        public static final int windowFixedWidthMajor = 0x7f0401d8;
        public static final int windowFixedWidthMinor = 0x7f0401d9;
        public static final int windowMinWidthMajor = 0x7f0401da;
        public static final int windowMinWidthMinor = 0x7f0401db;
        public static final int windowNoTitle = 0x7f0401dc;
    }

    public static final class bool {
        public static final int abc_action_bar_embed_tabs = 0x7f050000;
        public static final int abc_allow_stacked_button_bar = 0x7f050001;
        public static final int abc_config_actionMenuItemAllCaps = 0x7f050002;
        public static final int mtrl_btn_textappearance_all_caps = 0x7f050003;
        public static final int sb__is_phone = 0x7f050004;
        public static final int sb__is_swipeable = 0x7f050005;
    }

    public static final class color {
        public static final int Gray = 0x7f060000;
        public static final int Gray_divider = 0x7f060001;
        public static final int LightGray = 0x7f060002;
        public static final int Login_Split = 0x7f060003;
        public static final int SubTitleBack = 0x7f060004;
        public static final int SubTitleBackLine = 0x7f060005;
        public static final int abc_background_cache_hint_selector_material_dark = 0x7f060006;
        public static final int abc_background_cache_hint_selector_material_light = 0x7f060007;
        public static final int abc_btn_colored_borderless_text_material = 0x7f060008;
        public static final int abc_btn_colored_text_material = 0x7f060009;
        public static final int abc_color_highlight_material = 0x7f06000a;
        public static final int abc_hint_foreground_material_dark = 0x7f06000b;
        public static final int abc_hint_foreground_material_light = 0x7f06000c;
        public static final int abc_input_method_navigation_guard = 0x7f06000d;
        public static final int abc_primary_text_disable_only_material_dark = 0x7f06000e;
        public static final int abc_primary_text_disable_only_material_light = 0x7f06000f;
        public static final int abc_primary_text_material_dark = 0x7f060010;
        public static final int abc_primary_text_material_light = 0x7f060011;
        public static final int abc_search_url_text = 0x7f060012;
        public static final int abc_search_url_text_normal = 0x7f060013;
        public static final int abc_search_url_text_pressed = 0x7f060014;
        public static final int abc_search_url_text_selected = 0x7f060015;
        public static final int abc_secondary_text_material_dark = 0x7f060016;
        public static final int abc_secondary_text_material_light = 0x7f060017;
        public static final int abc_tint_btn_checkable = 0x7f060018;
        public static final int abc_tint_default = 0x7f060019;
        public static final int abc_tint_edittext = 0x7f06001a;
        public static final int abc_tint_seek_thumb = 0x7f06001b;
        public static final int abc_tint_spinner = 0x7f06001c;
        public static final int abc_tint_switch_track = 0x7f06001d;
        public static final int accent_material_dark = 0x7f06001e;
        public static final int accent_material_light = 0x7f06001f;
        public static final int back_color = 0x7f060020;
        public static final int back_color1 = 0x7f060021;
        public static final int back_color2 = 0x7f060022;
        public static final int back_color3 = 0x7f060023;
        public static final int background_floating_material_dark = 0x7f060024;
        public static final int background_floating_material_light = 0x7f060025;
        public static final int background_material_dark = 0x7f060026;
        public static final int background_material_light = 0x7f060027;
        public static final int black = 0x7f060028;
        public static final int blue_default = 0x7f060029;
        public static final int blue_light = 0x7f06002a;
        public static final int bright_foreground_disabled_material_dark = 0x7f06002b;
        public static final int bright_foreground_disabled_material_light = 0x7f06002c;
        public static final int bright_foreground_inverse_material_dark = 0x7f06002d;
        public static final int bright_foreground_inverse_material_light = 0x7f06002e;
        public static final int bright_foreground_material_dark = 0x7f06002f;
        public static final int bright_foreground_material_light = 0x7f060030;
        public static final int button_material_dark = 0x7f060031;
        public static final int button_material_light = 0x7f060032;
        public static final int cardview_dark_background = 0x7f060033;
        public static final int cardview_light_background = 0x7f060034;
        public static final int cardview_shadow_end_color = 0x7f060035;
        public static final int cardview_shadow_start_color = 0x7f060036;
        public static final int colorAccent = 0x7f060037;
        public static final int colorPrimary = 0x7f060038;
        public static final int colorPrimaryDark = 0x7f060039;
        public static final int color_sync0 = 0x7f06003a;
        public static final int color_sync1 = 0x7f06003b;
        public static final int color_sync2 = 0x7f06003c;
        public static final int color_sync3 = 0x7f06003d;
        public static final int color_sync4 = 0x7f06003e;
        public static final int color_sync5 = 0x7f06003f;
        public static final int design_bottom_navigation_shadow_color = 0x7f060040;
        public static final int design_default_color_primary = 0x7f060041;
        public static final int design_default_color_primary_dark = 0x7f060042;
        public static final int design_error = 0x7f060043;
        public static final int design_fab_shadow_end_color = 0x7f060044;
        public static final int design_fab_shadow_mid_color = 0x7f060045;
        public static final int design_fab_shadow_start_color = 0x7f060046;
        public static final int design_fab_stroke_end_inner_color = 0x7f060047;
        public static final int design_fab_stroke_end_outer_color = 0x7f060048;
        public static final int design_fab_stroke_top_inner_color = 0x7f060049;
        public static final int design_fab_stroke_top_outer_color = 0x7f06004a;
        public static final int design_snackbar_background_color = 0x7f06004b;
        public static final int design_tint_password_toggle = 0x7f06004c;
        public static final int dim_foreground_disabled_material_dark = 0x7f06004d;
        public static final int dim_foreground_disabled_material_light = 0x7f06004e;
        public static final int dim_foreground_material_dark = 0x7f06004f;
        public static final int dim_foreground_material_light = 0x7f060050;
        public static final int divider_normal_color = 0x7f060051;
        public static final int error_color_material_dark = 0x7f060052;
        public static final int error_color_material_light = 0x7f060053;
        public static final int foreground_material_dark = 0x7f060054;
        public static final int foreground_material_light = 0x7f060055;
        public static final int greenlight_alpha_color = 0x7f060056;
        public static final int highlighted_text_material_dark = 0x7f060057;
        public static final int highlighted_text_material_light = 0x7f060058;
        public static final int line_color = 0x7f060059;
        public static final int line_color1 = 0x7f06005a;
        public static final int material_blue_grey_800 = 0x7f06005b;
        public static final int material_blue_grey_900 = 0x7f06005c;
        public static final int material_blue_grey_950 = 0x7f06005d;
        public static final int material_deep_teal_200 = 0x7f06005e;
        public static final int material_deep_teal_500 = 0x7f06005f;
        public static final int material_grey_100 = 0x7f060060;
        public static final int material_grey_300 = 0x7f060061;
        public static final int material_grey_50 = 0x7f060062;
        public static final int material_grey_600 = 0x7f060063;
        public static final int material_grey_800 = 0x7f060064;
        public static final int material_grey_850 = 0x7f060065;
        public static final int material_grey_900 = 0x7f060066;
        public static final int menu_item_back_color = 0x7f060067;
        public static final int menu_item_divider_color = 0x7f060068;
        public static final int menu_item_sel_color = 0x7f060069;
        public static final int mtrl_bottom_nav_colored_item_tint = 0x7f06006a;
        public static final int mtrl_bottom_nav_item_tint = 0x7f06006b;
        public static final int mtrl_btn_bg_color_disabled = 0x7f06006c;
        public static final int mtrl_btn_bg_color_selector = 0x7f06006d;
        public static final int mtrl_btn_ripple_color = 0x7f06006e;
        public static final int mtrl_btn_stroke_color_selector = 0x7f06006f;
        public static final int mtrl_btn_text_btn_ripple_color = 0x7f060070;
        public static final int mtrl_btn_text_color_disabled = 0x7f060071;
        public static final int mtrl_btn_text_color_selector = 0x7f060072;
        public static final int mtrl_btn_transparent_bg_color = 0x7f060073;
        public static final int mtrl_chip_background_color = 0x7f060074;
        public static final int mtrl_chip_close_icon_tint = 0x7f060075;
        public static final int mtrl_chip_ripple_color = 0x7f060076;
        public static final int mtrl_chip_text_color = 0x7f060077;
        public static final int mtrl_fab_ripple_color = 0x7f060078;
        public static final int mtrl_scrim_color = 0x7f060079;
        public static final int mtrl_tabs_colored_ripple_color = 0x7f06007a;
        public static final int mtrl_tabs_icon_color_selector = 0x7f06007b;
        public static final int mtrl_tabs_icon_color_selector_colored = 0x7f06007c;
        public static final int mtrl_tabs_legacy_text_color_selector = 0x7f06007d;
        public static final int mtrl_tabs_ripple_color = 0x7f06007e;
        public static final int mtrl_text_btn_text_color_selector = 0x7f06007f;
        public static final int mtrl_textinput_default_box_stroke_color = 0x7f060080;
        public static final int mtrl_textinput_disabled_color = 0x7f060081;
        public static final int mtrl_textinput_filled_box_default_background_color = 0x7f060082;
        public static final int mtrl_textinput_hovered_box_stroke_color = 0x7f060083;
        public static final int normal_text_color = 0x7f060084;
        public static final int normal_text_color2 = 0x7f060085;
        public static final int normal_text_color3 = 0x7f060086;
        public static final int normal_text_color4 = 0x7f060087;
        public static final int normal_text_color5 = 0x7f060088;
        public static final int normal_text_color6 = 0x7f060089;
        public static final int notification_action_color_filter = 0x7f06008a;
        public static final int notification_icon_bg_color = 0x7f06008b;
        public static final int notification_material_background_media_default_color = 0x7f06008c;
        public static final int primary_dark_material_dark = 0x7f06008d;
        public static final int primary_dark_material_light = 0x7f06008e;
        public static final int primary_material_dark = 0x7f06008f;
        public static final int primary_material_light = 0x7f060090;
        public static final int primary_text_default_material_dark = 0x7f060091;
        public static final int primary_text_default_material_light = 0x7f060092;
        public static final int primary_text_disabled_material_dark = 0x7f060093;
        public static final int primary_text_disabled_material_light = 0x7f060094;
        public static final int red = 0x7f060095;
        public static final int ripple_material_dark = 0x7f060096;
        public static final int ripple_material_light = 0x7f060097;
        public static final int secondary_text_default_material_dark = 0x7f060098;
        public static final int secondary_text_default_material_light = 0x7f060099;
        public static final int secondary_text_disabled_material_dark = 0x7f06009a;
        public static final int secondary_text_disabled_material_light = 0x7f06009b;
        public static final int switch_thumb_disabled_material_dark = 0x7f06009c;
        public static final int switch_thumb_disabled_material_light = 0x7f06009d;
        public static final int switch_thumb_material_dark = 0x7f06009e;
        public static final int switch_thumb_material_light = 0x7f06009f;
        public static final int switch_thumb_normal_material_dark = 0x7f0600a0;
        public static final int switch_thumb_normal_material_light = 0x7f0600a1;
        public static final int tooltip_background_dark = 0x7f0600a2;
        public static final int tooltip_background_light = 0x7f0600a3;
        public static final int white = 0x7f0600a4;
        public static final int white_alpha_color = 0x7f0600a5;
    }

    public static final class dimen {
        public static final int _100dp = 0x7f070000;
        public static final int _10dp = 0x7f070001;
        public static final int _10sp = 0x7f070002;
        public static final int _110dp = 0x7f070003;
        public static final int _11sp = 0x7f070004;
        public static final int _120dp = 0x7f070005;
        public static final int _12dp = 0x7f070006;
        public static final int _12sp = 0x7f070007;
        public static final int _13sp = 0x7f070008;
        public static final int _140dp = 0x7f070009;
        public static final int _14sp = 0x7f07000a;
        public static final int _150dp = 0x7f07000b;
        public static final int _15dp = 0x7f07000c;
        public static final int _16dp = 0x7f07000d;
        public static final int _16sp = 0x7f07000e;
        public static final int _180dp = 0x7f07000f;
        public static final int _18sp = 0x7f070010;
        public static final int _1dp = 0x7f070011;
        public static final int _200dp = 0x7f070012;
        public static final int _20dp = 0x7f070013;
        public static final int _20sp = 0x7f070014;
        public static final int _22sp = 0x7f070015;
        public static final int _240dp = 0x7f070016;
        public static final int _25dp = 0x7f070017;
        public static final int _260dp = 0x7f070018;
        public static final int _270dp = 0x7f070019;
        public static final int _280dp = 0x7f07001a;
        public static final int _28sp = 0x7f07001b;
        public static final int _2dp = 0x7f07001c;
        public static final int _30dp = 0x7f07001d;
        public static final int _35dp = 0x7f07001e;
        public static final int _36sp = 0x7f07001f;
        public static final int _38dp = 0x7f070020;
        public static final int _3dp = 0x7f070021;
        public static final int _40dp = 0x7f070022;
        public static final int _4dp = 0x7f070023;
        public static final int _50dp = 0x7f070024;
        public static final int _50sp = 0x7f070025;
        public static final int _5dp = 0x7f070026;
        public static final int _60dp = 0x7f070027;
        public static final int _64sp = 0x7f070028;
        public static final int _6dp = 0x7f070029;
        public static final int _70dp = 0x7f07002a;
        public static final int _7dp = 0x7f07002b;
        public static final int _80dp = 0x7f07002c;
        public static final int _8dp = 0x7f07002d;
        public static final int _90dp = 0x7f07002e;
        public static final int _9sp = 0x7f07002f;
        public static final int abc_action_bar_content_inset_material = 0x7f070030;
        public static final int abc_action_bar_content_inset_with_nav = 0x7f070031;
        public static final int abc_action_bar_default_height_material = 0x7f070032;
        public static final int abc_action_bar_default_padding_end_material = 0x7f070033;
        public static final int abc_action_bar_default_padding_start_material = 0x7f070034;
        public static final int abc_action_bar_elevation_material = 0x7f070035;
        public static final int abc_action_bar_icon_vertical_padding_material = 0x7f070036;
        public static final int abc_action_bar_overflow_padding_end_material = 0x7f070037;
        public static final int abc_action_bar_overflow_padding_start_material = 0x7f070038;
        public static final int abc_action_bar_stacked_max_height = 0x7f070039;
        public static final int abc_action_bar_stacked_tab_max_width = 0x7f07003a;
        public static final int abc_action_bar_subtitle_bottom_margin_material = 0x7f07003b;
        public static final int abc_action_bar_subtitle_top_margin_material = 0x7f07003c;
        public static final int abc_action_button_min_height_material = 0x7f07003d;
        public static final int abc_action_button_min_width_material = 0x7f07003e;
        public static final int abc_action_button_min_width_overflow_material = 0x7f07003f;
        public static final int abc_alert_dialog_button_bar_height = 0x7f070040;
        public static final int abc_alert_dialog_button_dimen = 0x7f070041;
        public static final int abc_button_inset_horizontal_material = 0x7f070042;
        public static final int abc_button_inset_vertical_material = 0x7f070043;
        public static final int abc_button_padding_horizontal_material = 0x7f070044;
        public static final int abc_button_padding_vertical_material = 0x7f070045;
        public static final int abc_cascading_menus_min_smallest_width = 0x7f070046;
        public static final int abc_config_prefDialogWidth = 0x7f070047;
        public static final int abc_control_corner_material = 0x7f070048;
        public static final int abc_control_inset_material = 0x7f070049;
        public static final int abc_control_padding_material = 0x7f07004a;
        public static final int abc_dialog_corner_radius_material = 0x7f07004b;
        public static final int abc_dialog_fixed_height_major = 0x7f07004c;
        public static final int abc_dialog_fixed_height_minor = 0x7f07004d;
        public static final int abc_dialog_fixed_width_major = 0x7f07004e;
        public static final int abc_dialog_fixed_width_minor = 0x7f07004f;
        public static final int abc_dialog_list_padding_bottom_no_buttons = 0x7f070050;
        public static final int abc_dialog_list_padding_top_no_title = 0x7f070051;
        public static final int abc_dialog_min_width_major = 0x7f070052;
        public static final int abc_dialog_min_width_minor = 0x7f070053;
        public static final int abc_dialog_padding_material = 0x7f070054;
        public static final int abc_dialog_padding_top_material = 0x7f070055;
        public static final int abc_dialog_title_divider_material = 0x7f070056;
        public static final int abc_disabled_alpha_material_dark = 0x7f070057;
        public static final int abc_disabled_alpha_material_light = 0x7f070058;
        public static final int abc_dropdownitem_icon_width = 0x7f070059;
        public static final int abc_dropdownitem_text_padding_left = 0x7f07005a;
        public static final int abc_dropdownitem_text_padding_right = 0x7f07005b;
        public static final int abc_edit_text_inset_bottom_material = 0x7f07005c;
        public static final int abc_edit_text_inset_horizontal_material = 0x7f07005d;
        public static final int abc_edit_text_inset_top_material = 0x7f07005e;
        public static final int abc_floating_window_z = 0x7f07005f;
        public static final int abc_list_item_padding_horizontal_material = 0x7f070060;
        public static final int abc_panel_menu_list_width = 0x7f070061;
        public static final int abc_progress_bar_height_material = 0x7f070062;
        public static final int abc_search_view_preferred_height = 0x7f070063;
        public static final int abc_search_view_preferred_width = 0x7f070064;
        public static final int abc_seekbar_track_background_height_material = 0x7f070065;
        public static final int abc_seekbar_track_progress_height_material = 0x7f070066;
        public static final int abc_select_dialog_padding_start_material = 0x7f070067;
        public static final int abc_switch_padding = 0x7f070068;
        public static final int abc_text_size_body_1_material = 0x7f070069;
        public static final int abc_text_size_body_2_material = 0x7f07006a;
        public static final int abc_text_size_button_material = 0x7f07006b;
        public static final int abc_text_size_caption_material = 0x7f07006c;
        public static final int abc_text_size_display_1_material = 0x7f07006d;
        public static final int abc_text_size_display_2_material = 0x7f07006e;
        public static final int abc_text_size_display_3_material = 0x7f07006f;
        public static final int abc_text_size_display_4_material = 0x7f070070;
        public static final int abc_text_size_headline_material = 0x7f070071;
        public static final int abc_text_size_large_material = 0x7f070072;
        public static final int abc_text_size_medium_material = 0x7f070073;
        public static final int abc_text_size_menu_header_material = 0x7f070074;
        public static final int abc_text_size_menu_material = 0x7f070075;
        public static final int abc_text_size_small_material = 0x7f070076;
        public static final int abc_text_size_subhead_material = 0x7f070077;
        public static final int abc_text_size_subtitle_material_toolbar = 0x7f070078;
        public static final int abc_text_size_title_material = 0x7f070079;
        public static final int abc_text_size_title_material_toolbar = 0x7f07007a;
        public static final int activity_horizontal_margin = 0x7f07007b;
        public static final int activity_vertical_margin = 0x7f07007c;
        public static final int appbar_padding = 0x7f07007d;
        public static final int appbar_padding_top = 0x7f07007e;
        public static final int cardview_compat_inset_shadow = 0x7f07007f;
        public static final int cardview_default_elevation = 0x7f070080;
        public static final int cardview_default_radius = 0x7f070081;
        public static final int compat_button_inset_horizontal_material = 0x7f070082;
        public static final int compat_button_inset_vertical_material = 0x7f070083;
        public static final int compat_button_padding_horizontal_material = 0x7f070084;
        public static final int compat_button_padding_vertical_material = 0x7f070085;
        public static final int compat_control_corner_material = 0x7f070086;
        public static final int compat_notification_large_icon_max_height = 0x7f070087;
        public static final int compat_notification_large_icon_max_width = 0x7f070088;
        public static final int design_appbar_elevation = 0x7f070089;
        public static final int design_bottom_navigation_active_item_max_width = 0x7f07008a;
        public static final int design_bottom_navigation_active_item_min_width = 0x7f07008b;
        public static final int design_bottom_navigation_active_text_size = 0x7f07008c;
        public static final int design_bottom_navigation_elevation = 0x7f07008d;
        public static final int design_bottom_navigation_height = 0x7f07008e;
        public static final int design_bottom_navigation_icon_size = 0x7f07008f;
        public static final int design_bottom_navigation_item_max_width = 0x7f070090;
        public static final int design_bottom_navigation_item_min_width = 0x7f070091;
        public static final int design_bottom_navigation_margin = 0x7f070092;
        public static final int design_bottom_navigation_shadow_height = 0x7f070093;
        public static final int design_bottom_navigation_text_size = 0x7f070094;
        public static final int design_bottom_sheet_modal_elevation = 0x7f070095;
        public static final int design_bottom_sheet_peek_height_min = 0x7f070096;
        public static final int design_fab_border_width = 0x7f070097;
        public static final int design_fab_elevation = 0x7f070098;
        public static final int design_fab_image_size = 0x7f070099;
        public static final int design_fab_size_mini = 0x7f07009a;
        public static final int design_fab_size_normal = 0x7f07009b;
        public static final int design_fab_translation_z_hovered_focused = 0x7f07009c;
        public static final int design_fab_translation_z_pressed = 0x7f07009d;
        public static final int design_navigation_elevation = 0x7f07009e;
        public static final int design_navigation_icon_padding = 0x7f07009f;
        public static final int design_navigation_icon_size = 0x7f0700a0;
        public static final int design_navigation_item_horizontal_padding = 0x7f0700a1;
        public static final int design_navigation_item_icon_padding = 0x7f0700a2;
        public static final int design_navigation_max_width = 0x7f0700a3;
        public static final int design_navigation_padding_bottom = 0x7f0700a4;
        public static final int design_navigation_separator_vertical_padding = 0x7f0700a5;
        public static final int design_snackbar_action_inline_max_width = 0x7f0700a6;
        public static final int design_snackbar_background_corner_radius = 0x7f0700a7;
        public static final int design_snackbar_elevation = 0x7f0700a8;
        public static final int design_snackbar_extra_spacing_horizontal = 0x7f0700a9;
        public static final int design_snackbar_max_width = 0x7f0700aa;
        public static final int design_snackbar_min_width = 0x7f0700ab;
        public static final int design_snackbar_padding_horizontal = 0x7f0700ac;
        public static final int design_snackbar_padding_vertical = 0x7f0700ad;
        public static final int design_snackbar_padding_vertical_2lines = 0x7f0700ae;
        public static final int design_snackbar_text_size = 0x7f0700af;
        public static final int design_tab_max_width = 0x7f0700b0;
        public static final int design_tab_scrollable_min_width = 0x7f0700b1;
        public static final int design_tab_text_size = 0x7f0700b2;
        public static final int design_tab_text_size_2line = 0x7f0700b3;
        public static final int design_textinput_caption_translate_y = 0x7f0700b4;
        public static final int disabled_alpha_material_dark = 0x7f0700b5;
        public static final int disabled_alpha_material_light = 0x7f0700b6;
        public static final int fab_margin = 0x7f0700b7;
        public static final int fastscroll_default_thickness = 0x7f0700b8;
        public static final int fastscroll_margin = 0x7f0700b9;
        public static final int fastscroll_minimum_range = 0x7f0700ba;
        public static final int highlight_alpha_material_colored = 0x7f0700bb;
        public static final int highlight_alpha_material_dark = 0x7f0700bc;
        public static final int highlight_alpha_material_light = 0x7f0700bd;
        public static final int hint_alpha_material_dark = 0x7f0700be;
        public static final int hint_alpha_material_light = 0x7f0700bf;
        public static final int hint_pressed_alpha_material_dark = 0x7f0700c0;
        public static final int hint_pressed_alpha_material_light = 0x7f0700c1;
        public static final int item_touch_helper_max_drag_scroll_per_frame = 0x7f0700c2;
        public static final int item_touch_helper_swipe_escape_max_velocity = 0x7f0700c3;
        public static final int item_touch_helper_swipe_escape_velocity = 0x7f0700c4;
        public static final int mtrl_bottomappbar_fabOffsetEndMode = 0x7f0700c5;
        public static final int mtrl_bottomappbar_fab_cradle_margin = 0x7f0700c6;
        public static final int mtrl_bottomappbar_fab_cradle_rounded_corner_radius = 0x7f0700c7;
        public static final int mtrl_bottomappbar_fab_cradle_vertical_offset = 0x7f0700c8;
        public static final int mtrl_bottomappbar_height = 0x7f0700c9;
        public static final int mtrl_btn_corner_radius = 0x7f0700ca;
        public static final int mtrl_btn_dialog_btn_min_width = 0x7f0700cb;
        public static final int mtrl_btn_disabled_elevation = 0x7f0700cc;
        public static final int mtrl_btn_disabled_z = 0x7f0700cd;
        public static final int mtrl_btn_elevation = 0x7f0700ce;
        public static final int mtrl_btn_focused_z = 0x7f0700cf;
        public static final int mtrl_btn_hovered_z = 0x7f0700d0;
        public static final int mtrl_btn_icon_btn_padding_left = 0x7f0700d1;
        public static final int mtrl_btn_icon_padding = 0x7f0700d2;
        public static final int mtrl_btn_inset = 0x7f0700d3;
        public static final int mtrl_btn_letter_spacing = 0x7f0700d4;
        public static final int mtrl_btn_padding_bottom = 0x7f0700d5;
        public static final int mtrl_btn_padding_left = 0x7f0700d6;
        public static final int mtrl_btn_padding_right = 0x7f0700d7;
        public static final int mtrl_btn_padding_top = 0x7f0700d8;
        public static final int mtrl_btn_pressed_z = 0x7f0700d9;
        public static final int mtrl_btn_stroke_size = 0x7f0700da;
        public static final int mtrl_btn_text_btn_icon_padding = 0x7f0700db;
        public static final int mtrl_btn_text_btn_padding_left = 0x7f0700dc;
        public static final int mtrl_btn_text_btn_padding_right = 0x7f0700dd;
        public static final int mtrl_btn_text_size = 0x7f0700de;
        public static final int mtrl_btn_z = 0x7f0700df;
        public static final int mtrl_card_elevation = 0x7f0700e0;
        public static final int mtrl_card_spacing = 0x7f0700e1;
        public static final int mtrl_chip_pressed_translation_z = 0x7f0700e2;
        public static final int mtrl_chip_text_size = 0x7f0700e3;
        public static final int mtrl_fab_elevation = 0x7f0700e4;
        public static final int mtrl_fab_translation_z_hovered_focused = 0x7f0700e5;
        public static final int mtrl_fab_translation_z_pressed = 0x7f0700e6;
        public static final int mtrl_navigation_elevation = 0x7f0700e7;
        public static final int mtrl_navigation_item_horizontal_padding = 0x7f0700e8;
        public static final int mtrl_navigation_item_icon_padding = 0x7f0700e9;
        public static final int mtrl_snackbar_background_corner_radius = 0x7f0700ea;
        public static final int mtrl_snackbar_margin = 0x7f0700eb;
        public static final int mtrl_textinput_box_bottom_offset = 0x7f0700ec;
        public static final int mtrl_textinput_box_corner_radius_medium = 0x7f0700ed;
        public static final int mtrl_textinput_box_corner_radius_small = 0x7f0700ee;
        public static final int mtrl_textinput_box_label_cutout_padding = 0x7f0700ef;
        public static final int mtrl_textinput_box_padding_end = 0x7f0700f0;
        public static final int mtrl_textinput_box_stroke_width_default = 0x7f0700f1;
        public static final int mtrl_textinput_box_stroke_width_focused = 0x7f0700f2;
        public static final int mtrl_textinput_outline_box_expanded_padding = 0x7f0700f3;
        public static final int mtrl_toolbar_default_height = 0x7f0700f4;
        public static final int nav_header_height = 0x7f0700f5;
        public static final int nav_header_vertical_spacing = 0x7f0700f6;
        public static final int notification_action_icon_size = 0x7f0700f7;
        public static final int notification_action_text_size = 0x7f0700f8;
        public static final int notification_big_circle_margin = 0x7f0700f9;
        public static final int notification_content_margin_start = 0x7f0700fa;
        public static final int notification_large_icon_height = 0x7f0700fb;
        public static final int notification_large_icon_width = 0x7f0700fc;
        public static final int notification_main_column_padding_top = 0x7f0700fd;
        public static final int notification_media_narrow_margin = 0x7f0700fe;
        public static final int notification_right_icon_size = 0x7f0700ff;
        public static final int notification_right_side_padding_top = 0x7f070100;
        public static final int notification_small_icon_background_padding = 0x7f070101;
        public static final int notification_small_icon_size_as_large = 0x7f070102;
        public static final int notification_subtext_size = 0x7f070103;
        public static final int notification_top_pad = 0x7f070104;
        public static final int notification_top_pad_large_text = 0x7f070105;
        public static final int subtitle_corner_radius = 0x7f070106;
        public static final int subtitle_outline_width = 0x7f070107;
        public static final int subtitle_shadow_offset = 0x7f070108;
        public static final int subtitle_shadow_radius = 0x7f070109;
        public static final int tooltip_corner_radius = 0x7f07010a;
        public static final int tooltip_horizontal_padding = 0x7f07010b;
        public static final int tooltip_margin = 0x7f07010c;
        public static final int tooltip_precise_anchor_extra_offset = 0x7f07010d;
        public static final int tooltip_precise_anchor_threshold = 0x7f07010e;
        public static final int tooltip_vertical_padding = 0x7f07010f;
        public static final int tooltip_y_offset_non_touch = 0x7f070110;
        public static final int tooltip_y_offset_touch = 0x7f070111;
    }

    public static final class drawable {
        public static final int abc_ab_share_pack_mtrl_alpha = 0x7f080006;
        public static final int abc_action_bar_item_background_material = 0x7f080007;
        public static final int abc_btn_borderless_material = 0x7f080008;
        public static final int abc_btn_check_material = 0x7f080009;
        public static final int abc_btn_check_to_on_mtrl_000 = 0x7f08000a;
        public static final int abc_btn_check_to_on_mtrl_015 = 0x7f08000b;
        public static final int abc_btn_colored_material = 0x7f08000c;
        public static final int abc_btn_default_mtrl_shape = 0x7f08000d;
        public static final int abc_btn_radio_material = 0x7f08000e;
        public static final int abc_btn_radio_to_on_mtrl_000 = 0x7f08000f;
        public static final int abc_btn_radio_to_on_mtrl_015 = 0x7f080010;
        public static final int abc_btn_switch_to_on_mtrl_00001 = 0x7f080011;
        public static final int abc_btn_switch_to_on_mtrl_00012 = 0x7f080012;
        public static final int abc_cab_background_internal_bg = 0x7f080013;
        public static final int abc_cab_background_top_material = 0x7f080014;
        public static final int abc_cab_background_top_mtrl_alpha = 0x7f080015;
        public static final int abc_control_background_material = 0x7f080016;
        public static final int abc_dialog_material_background = 0x7f080017;
        public static final int abc_edit_text_material = 0x7f080018;
        public static final int abc_ic_ab_back_material = 0x7f080019;
        public static final int abc_ic_arrow_drop_right_black_24dp = 0x7f08001a;
        public static final int abc_ic_clear_material = 0x7f08001b;
        public static final int abc_ic_commit_search_api_mtrl_alpha = 0x7f08001c;
        public static final int abc_ic_go_search_api_material = 0x7f08001d;
        public static final int abc_ic_menu_copy_mtrl_am_alpha = 0x7f08001e;
        public static final int abc_ic_menu_cut_mtrl_alpha = 0x7f08001f;
        public static final int abc_ic_menu_overflow_material = 0x7f080020;
        public static final int abc_ic_menu_paste_mtrl_am_alpha = 0x7f080021;
        public static final int abc_ic_menu_selectall_mtrl_alpha = 0x7f080022;
        public static final int abc_ic_menu_share_mtrl_alpha = 0x7f080023;
        public static final int abc_ic_search_api_material = 0x7f080024;
        public static final int abc_ic_star_black_16dp = 0x7f080025;
        public static final int abc_ic_star_black_36dp = 0x7f080026;
        public static final int abc_ic_star_black_48dp = 0x7f080027;
        public static final int abc_ic_star_half_black_16dp = 0x7f080028;
        public static final int abc_ic_star_half_black_36dp = 0x7f080029;
        public static final int abc_ic_star_half_black_48dp = 0x7f08002a;
        public static final int abc_ic_voice_search_api_material = 0x7f08002b;
        public static final int abc_item_background_holo_dark = 0x7f08002c;
        public static final int abc_item_background_holo_light = 0x7f08002d;
        public static final int abc_list_divider_material = 0x7f08002e;
        public static final int abc_list_divider_mtrl_alpha = 0x7f08002f;
        public static final int abc_list_focused_holo = 0x7f080030;
        public static final int abc_list_longpressed_holo = 0x7f080031;
        public static final int abc_list_pressed_holo_dark = 0x7f080032;
        public static final int abc_list_pressed_holo_light = 0x7f080033;
        public static final int abc_list_selector_background_transition_holo_dark = 0x7f080034;
        public static final int abc_list_selector_background_transition_holo_light = 0x7f080035;
        public static final int abc_list_selector_disabled_holo_dark = 0x7f080036;
        public static final int abc_list_selector_disabled_holo_light = 0x7f080037;
        public static final int abc_list_selector_holo_dark = 0x7f080038;
        public static final int abc_list_selector_holo_light = 0x7f080039;
        public static final int abc_menu_hardkey_panel_mtrl_mult = 0x7f08003a;
        public static final int abc_popup_background_mtrl_mult = 0x7f08003b;
        public static final int abc_ratingbar_indicator_material = 0x7f08003c;
        public static final int abc_ratingbar_material = 0x7f08003d;
        public static final int abc_ratingbar_small_material = 0x7f08003e;
        public static final int abc_scrubber_control_off_mtrl_alpha = 0x7f08003f;
        public static final int abc_scrubber_control_to_pressed_mtrl_000 = 0x7f080040;
        public static final int abc_scrubber_control_to_pressed_mtrl_005 = 0x7f080041;
        public static final int abc_scrubber_primary_mtrl_alpha = 0x7f080042;
        public static final int abc_scrubber_track_mtrl_alpha = 0x7f080043;
        public static final int abc_seekbar_thumb_material = 0x7f080044;
        public static final int abc_seekbar_tick_mark_material = 0x7f080045;
        public static final int abc_seekbar_track_material = 0x7f080046;
        public static final int abc_spinner_mtrl_am_alpha = 0x7f080047;
        public static final int abc_spinner_textfield_background_material = 0x7f080048;
        public static final int abc_switch_thumb_material = 0x7f080049;
        public static final int abc_switch_track_mtrl_alpha = 0x7f08004a;
        public static final int abc_tab_indicator_material = 0x7f08004b;
        public static final int abc_tab_indicator_mtrl_alpha = 0x7f08004c;
        public static final int abc_text_cursor_material = 0x7f08004d;
        public static final int abc_text_select_handle_left_mtrl_dark = 0x7f08004e;
        public static final int abc_text_select_handle_left_mtrl_light = 0x7f08004f;
        public static final int abc_text_select_handle_middle_mtrl_dark = 0x7f080050;
        public static final int abc_text_select_handle_middle_mtrl_light = 0x7f080051;
        public static final int abc_text_select_handle_right_mtrl_dark = 0x7f080052;
        public static final int abc_text_select_handle_right_mtrl_light = 0x7f080053;
        public static final int abc_textfield_activated_mtrl_alpha = 0x7f080054;
        public static final int abc_textfield_default_mtrl_alpha = 0x7f080055;
        public static final int abc_textfield_search_activated_mtrl_alpha = 0x7f080056;
        public static final int abc_textfield_search_default_mtrl_alpha = 0x7f080057;
        public static final int abc_textfield_search_material = 0x7f080058;
        public static final int abc_vector_test = 0x7f080059;
        public static final int avd_hide_password = 0x7f08005a;
        public static final int avd_show_password = 0x7f08005b;
        public static final int circle = 0x7f08005c;
        public static final int course_button_style = 0x7f08005d;
        public static final int default_board = 0x7f08005e;
        public static final int default_editbox = 0x7f08005f;
        public static final int design_bottom_navigation_item_background = 0x7f080060;
        public static final int design_fab_background = 0x7f080061;
        public static final int design_ic_visibility = 0x7f080062;
        public static final int design_ic_visibility_off = 0x7f080063;
        public static final int design_password_eye = 0x7f080064;
        public static final int design_snackbar_background = 0x7f080065;
        public static final int ic_menu_camera = 0x7f080066;
        public static final int ic_menu_gallery = 0x7f080067;
        public static final int ic_menu_manage = 0x7f080068;
        public static final int ic_menu_send = 0x7f080069;
        public static final int ic_menu_share = 0x7f08006a;
        public static final int ic_menu_slideshow = 0x7f08006b;
        public static final int ic_mtrl_chip_checked_black = 0x7f08006c;
        public static final int ic_mtrl_chip_checked_circle = 0x7f08006d;
        public static final int ic_mtrl_chip_close_circle = 0x7f08006e;
        public static final int icon2 = 0x7f08006f;
        public static final int icon_0 = 0x7f080070;
        public static final int icon_1 = 0x7f080071;
        public static final int icon_10 = 0x7f080072;
        public static final int icon_11 = 0x7f080073;
        public static final int icon_12 = 0x7f080074;
        public static final int icon_2 = 0x7f080075;
        public static final int icon_3 = 0x7f080076;
        public static final int icon_4 = 0x7f080077;
        public static final int icon_5 = 0x7f080078;
        public static final int icon_6 = 0x7f080079;
        public static final int icon_7 = 0x7f08007a;
        public static final int icon_8 = 0x7f08007b;
        public static final int icon_9 = 0x7f08007c;
        public static final int icon_accept = 0x7f08007d;
        public static final int icon_block = 0x7f08007e;
        public static final int icon_close = 0x7f08007f;
        public static final int icon_logout = 0x7f080080;
        public static final int icon_mail = 0x7f080081;
        public static final int icon_miss = 0x7f080082;
        public static final int icon_nodata = 0x7f080083;
        public static final int icon_none = 0x7f080084;
        public static final int icon_phone1 = 0x7f080085;
        public static final int icon_reject = 0x7f080086;
        public static final int icon_search = 0x7f080087;
        public static final int icon_search_mark = 0x7f080088;
        public static final int icon_sms = 0x7f080089;
        public static final int img_alpha = 0x7f08008a;
        public static final int img_alpha0 = 0x7f08008b;
        public static final int img_alpha1 = 0x7f08008c;
        public static final int img_alpha100 = 0x7f08008d;
        public static final int img_alpha25 = 0x7f08008e;
        public static final int img_alpha50 = 0x7f08008f;
        public static final int img_alpha75 = 0x7f080090;
        public static final int img_back2 = 0x7f080091;
        public static final int login_back = 0x7f080092;
        public static final int login_edit = 0x7f080093;
        public static final int login_mark_email = 0x7f080094;
        public static final int login_mark_pwd = 0x7f080095;
        public static final int main_title = 0x7f080096;
        public static final int mini_call_button = 0x7f080097;
        public static final int mtrl_snackbar_background = 0x7f080098;
        public static final int mtrl_tabs_default_indicator = 0x7f080099;
        public static final int navigation_empty_icon = 0x7f08009a;
        public static final int normal_button_style = 0x7f08009b;
        public static final int notification_action_background = 0x7f08009c;
        public static final int notification_bg = 0x7f08009d;
        public static final int notification_bg_low = 0x7f08009e;
        public static final int notification_bg_low_normal = 0x7f08009f;
        public static final int notification_bg_low_pressed = 0x7f0800a0;
        public static final int notification_bg_normal = 0x7f0800a1;
        public static final int notification_bg_normal_pressed = 0x7f0800a2;
        public static final int notification_icon_background = 0x7f0800a3;
        public static final int notification_template_icon_bg = 0x7f0800a4;
        public static final int notification_template_icon_low_bg = 0x7f0800a5;
        public static final int notification_tile_bg = 0x7f0800a6;
        public static final int notify_panel_notification_icon_bg = 0x7f0800a7;
        public static final int pop_accept = 0x7f0800a8;
        public static final int pop_back = 0x7f0800a9;
        public static final int pop_background = 0x7f0800aa;
        public static final int pop_close = 0x7f0800ab;
        public static final int pop_close_gray = 0x7f0800ac;
        public static final int pop_mark = 0x7f0800ad;
        public static final int pop_reject = 0x7f0800ae;
        public static final int pop_result = 0x7f0800af;
        public static final int recall_button_style = 0x7f0800b0;
        public static final int round_button = 0x7f0800b1;
        public static final int search_back = 0x7f0800b2;
        public static final int search_back1 = 0x7f0800b3;
        public static final int search_backicon = 0x7f0800b4;
        public static final int search_button = 0x7f0800b5;
        public static final int search_icon = 0x7f0800b6;
        public static final int shape_back_lightblue_stroke = 0x7f0800b7;
        public static final int shape_back_white_stroke = 0x7f0800b8;
        public static final int side_nav_bar = 0x7f0800b9;
        public static final int signin_line = 0x7f0800ba;
        public static final int social_button = 0x7f0800bb;
        public static final int telegram_icon = 0x7f0800bc;
        public static final int tooltip_frame_dark = 0x7f0800bd;
        public static final int tooltip_frame_light = 0x7f0800be;

        /* JADX INFO: Added by JADX */
        public static final int _avd_hide_password__0_res_0x7f080000 = 0x7f080000;

        /* JADX INFO: Added by JADX */
        public static final int _avd_hide_password__1_res_0x7f080001 = 0x7f080001;

        /* JADX INFO: Added by JADX */
        public static final int _avd_hide_password__2_res_0x7f080002 = 0x7f080002;

        /* JADX INFO: Added by JADX */
        public static final int _avd_show_password__0_res_0x7f080003 = 0x7f080003;

        /* JADX INFO: Added by JADX */
        public static final int _avd_show_password__1_res_0x7f080004 = 0x7f080004;

        /* JADX INFO: Added by JADX */
        public static final int _avd_show_password__2_res_0x7f080005 = 0x7f080005;
    }

    public static final class id {
        public static final int ALT = 0x7f090000;
        public static final int CTRL = 0x7f090001;
        public static final int FUNCTION = 0x7f090002;
        public static final int META = 0x7f090003;
        public static final int SHIFT = 0x7f090004;
        public static final int SYM = 0x7f090005;
        public static final int action0 = 0x7f090006;
        public static final int action_bar = 0x7f090007;
        public static final int action_bar_activity_content = 0x7f090008;
        public static final int action_bar_container = 0x7f090009;
        public static final int action_bar_root = 0x7f09000a;
        public static final int action_bar_spinner = 0x7f09000b;
        public static final int action_bar_subtitle = 0x7f09000c;
        public static final int action_bar_title = 0x7f09000d;
        public static final int action_container = 0x7f09000e;
        public static final int action_context_bar = 0x7f09000f;
        public static final int action_divider = 0x7f090010;
        public static final int action_image = 0x7f090011;
        public static final int action_menu_divider = 0x7f090012;
        public static final int action_menu_presenter = 0x7f090013;
        public static final int action_mode_bar = 0x7f090014;
        public static final int action_mode_bar_stub = 0x7f090015;
        public static final int action_mode_close_button = 0x7f090016;
        public static final int action_text = 0x7f090017;
        public static final int actions = 0x7f090018;
        public static final int activity_chooser_view_content = 0x7f090019;
        public static final int add = 0x7f09001a;
        public static final int alertTitle = 0x7f09001b;
        public static final int all = 0x7f09001c;
        public static final int always = 0x7f09001d;
        public static final int appBarLayout = 0x7f09001e;
        public static final int async = 0x7f09001f;
        public static final int auto = 0x7f090020;
        public static final int beginning = 0x7f090021;
        public static final int blockFragment = 0x7f090022;
        public static final int block_content = 0x7f090023;
        public static final int blocking = 0x7f090024;
        public static final int bottom = 0x7f090025;
        public static final int btnAccept = 0x7f090026;
        public static final int btnBlockPrefNumber = 0x7f090027;
        public static final int btnBlockSpecNumber = 0x7f090028;
        public static final int btnBlockTodayCall = 0x7f090029;
        public static final int btnCall = 0x7f09002a;
        public static final int btnCallExplosion = 0x7f09002b;
        public static final int btnCancel = 0x7f09002c;
        public static final int btnClose = 0x7f09002d;
        public static final int btnDelete = 0x7f09002e;
        public static final int btnDetail = 0x7f09002f;
        public static final int btnGoList = 0x7f090030;
        public static final int btnLogin = 0x7f090031;
        public static final int btnOk = 0x7f090032;
        public static final int btnRefresh = 0x7f090033;
        public static final int btnReject = 0x7f090034;
        public static final int btnsearch = 0x7f090035;
        public static final int buttonPanel = 0x7f090036;
        public static final int cancel_action = 0x7f090037;
        public static final int center = 0x7f090038;
        public static final int center_horizontal = 0x7f090039;
        public static final int center_vertical = 0x7f09003a;
        public static final int checkbox = 0x7f09003b;
        public static final int chkBottom = 0x7f09003c;
        public static final int chkMiddle = 0x7f09003d;
        public static final int chkTop = 0x7f09003e;
        public static final int chronometer = 0x7f09003f;
        public static final int clip_horizontal = 0x7f090040;
        public static final int clip_vertical = 0x7f090041;
        public static final int collapseActionView = 0x7f090042;
        public static final int container = 0x7f090043;
        public static final int contanerlayout = 0x7f090044;
        public static final int content = 0x7f090045;
        public static final int contentPanel = 0x7f090046;
        public static final int control = 0x7f090047;
        public static final int coordinator = 0x7f090048;
        public static final int coordinatorLayout = 0x7f090049;
        public static final int custom = 0x7f09004a;
        public static final int customPanel = 0x7f09004b;
        public static final int decor_content_parent = 0x7f09004c;
        public static final int default_activity_button = 0x7f09004d;
        public static final int design_bottom_sheet = 0x7f09004e;
        public static final int design_menu_item_action_area = 0x7f09004f;
        public static final int design_menu_item_action_area_stub = 0x7f090050;
        public static final int design_menu_item_text = 0x7f090051;
        public static final int design_navigation_view = 0x7f090052;
        public static final int disableHome = 0x7f090053;
        public static final int divider = 0x7f090054;
        public static final int divider0 = 0x7f090055;
        public static final int divider1 = 0x7f090056;
        public static final int divider2 = 0x7f090057;
        public static final int divider3 = 0x7f090058;
        public static final int divider4 = 0x7f090059;
        public static final int divider5 = 0x7f09005a;
        public static final int divider_2 = 0x7f09005b;
        public static final int drawerLV = 0x7f09005c;
        public static final int drawer_layout = 0x7f09005d;
        public static final int edit_query = 0x7f09005e;
        public static final int edtLogID = 0x7f09005f;
        public static final int edtLogPass = 0x7f090060;
        public static final int edtSearch = 0x7f090061;
        public static final int end = 0x7f090062;
        public static final int end_padder = 0x7f090063;
        public static final int enterAlways = 0x7f090064;
        public static final int enterAlwaysCollapsed = 0x7f090065;
        public static final int exitUntilCollapsed = 0x7f090066;
        public static final int expand_activities_button = 0x7f090067;
        public static final int expanded_menu = 0x7f090068;
        public static final int facebookBT = 0x7f090069;
        public static final int fill = 0x7f09006a;
        public static final int fill_horizontal = 0x7f09006b;
        public static final int fill_vertical = 0x7f09006c;
        public static final int filled = 0x7f09006d;
        public static final int fixed = 0x7f09006e;
        public static final int forever = 0x7f09006f;
        public static final int ghost_view = 0x7f090070;
        public static final int googleBT = 0x7f090071;
        public static final int group_divider = 0x7f090072;
        public static final int home = 0x7f090073;
        public static final int homeAsUp = 0x7f090074;
        public static final int icon = 0x7f090075;
        public static final int iconIV = 0x7f090076;
        public static final int icon_group = 0x7f090077;
        public static final int ifRoom = 0x7f090078;
        public static final int image = 0x7f090079;
        public static final int imgAction = 0x7f09007a;
        public static final int imgNew = 0x7f09007b;
        public static final int imgPhone = 0x7f09007c;
        public static final int incomeEmptyTV = 0x7f09007d;
        public static final int info = 0x7f09007e;
        public static final int italic = 0x7f09007f;
        public static final int item = 0x7f090080;
        public static final int item_touch_helper_previous_elevation = 0x7f090081;
        public static final int labeled = 0x7f090082;
        public static final int largeLabel = 0x7f090083;
        public static final int layoutWatermark = 0x7f090084;
        public static final int layout_close = 0x7f090085;
        public static final int layout_nodata = 0x7f090086;
        public static final int left = 0x7f090087;
        public static final int line1 = 0x7f090088;
        public static final int line3 = 0x7f090089;
        public static final int lineBT = 0x7f09008a;
        public static final int linear_manryo = 0x7f09008b;
        public static final int listMode = 0x7f09008c;
        public static final int list_item = 0x7f09008d;
        public static final int lstBlockHistory = 0x7f09008e;
        public static final int lstDetail = 0x7f09008f;
        public static final int lstRecentCall = 0x7f090090;
        public static final int lstReport = 0x7f090091;
        public static final int mainFragment = 0x7f090092;
        public static final int mainLayout = 0x7f090093;
        public static final int makeCallBT = 0x7f090094;
        public static final int masked = 0x7f090095;
        public static final int media_actions = 0x7f090096;
        public static final int menuLayout = 0x7f090097;
        public static final int message = 0x7f090098;
        public static final int middle = 0x7f090099;
        public static final int mini = 0x7f09009a;
        public static final int mtrl_child_content_container = 0x7f09009b;
        public static final int mtrl_internal_children_alpha_tag = 0x7f09009c;
        public static final int multiply = 0x7f09009d;
        public static final int nameTV = 0x7f09009e;
        public static final int nav_header = 0x7f09009f;
        public static final int navigation_header_container = 0x7f0900a0;
        public static final int never = 0x7f0900a1;
        public static final int none = 0x7f0900a2;
        public static final int normal = 0x7f0900a3;
        public static final int notice_button = 0x7f0900a4;
        public static final int notice_close = 0x7f0900a5;
        public static final int notification_background = 0x7f0900a6;
        public static final int notification_main_column = 0x7f0900a7;
        public static final int notification_main_column_container = 0x7f0900a8;
        public static final int outline = 0x7f0900a9;
        public static final int parallax = 0x7f0900aa;
        public static final int parentPanel = 0x7f0900ab;
        public static final int parent_matrix = 0x7f0900ac;
        public static final int pin = 0x7f0900ad;
        public static final int progress_circular = 0x7f0900ae;
        public static final int progress_horizontal = 0x7f0900af;
        public static final int radio = 0x7f0900b0;
        public static final int recentCallTV = 0x7f0900b1;
        public static final int reportFragment = 0x7f0900b2;
        public static final int right = 0x7f0900b3;
        public static final int right_icon = 0x7f0900b4;
        public static final int right_side = 0x7f0900b5;
        public static final int save_image_matrix = 0x7f0900b6;
        public static final int save_non_transition_alpha = 0x7f0900b7;
        public static final int save_scale_type = 0x7f0900b8;
        public static final int screen = 0x7f0900b9;
        public static final int scroll = 0x7f0900ba;
        public static final int scrollIndicatorDown = 0x7f0900bb;
        public static final int scrollIndicatorUp = 0x7f0900bc;
        public static final int scrollView = 0x7f0900bd;
        public static final int scrollable = 0x7f0900be;
        public static final int searchFrame = 0x7f0900bf;
        public static final int searchResultCount = 0x7f0900c0;
        public static final int searchResultEmptyTV = 0x7f0900c1;
        public static final int searchResultLV = 0x7f0900c2;
        public static final int search_badge = 0x7f0900c3;
        public static final int search_bar = 0x7f0900c4;
        public static final int search_button = 0x7f0900c5;
        public static final int search_close_btn = 0x7f0900c6;
        public static final int search_edit_frame = 0x7f0900c7;
        public static final int search_go_btn = 0x7f0900c8;
        public static final int search_mag_icon = 0x7f0900c9;
        public static final int search_plate = 0x7f0900ca;
        public static final int search_src_text = 0x7f0900cb;
        public static final int search_voice_btn = 0x7f0900cc;
        public static final int searchresultTX = 0x7f0900cd;
        public static final int select_dialog_listview = 0x7f0900ce;
        public static final int selected = 0x7f0900cf;
        public static final int sendSMSBT = 0x7f0900d0;
        public static final int settingFrame = 0x7f0900d1;
        public static final int shortcut = 0x7f0900d2;
        public static final int showCustom = 0x7f0900d3;
        public static final int showHome = 0x7f0900d4;
        public static final int showTitle = 0x7f0900d5;
        public static final int smallLabel = 0x7f0900d6;
        public static final int snackbar_action = 0x7f0900d7;
        public static final int snackbar_text = 0x7f0900d8;
        public static final int snap = 0x7f0900d9;
        public static final int snapMargins = 0x7f0900da;
        public static final int spacer = 0x7f0900db;
        public static final int split_action_bar = 0x7f0900dc;
        public static final int src_atop = 0x7f0900dd;
        public static final int src_in = 0x7f0900de;
        public static final int src_over = 0x7f0900df;
        public static final int start = 0x7f0900e0;
        public static final int status_bar_latest_event_content = 0x7f0900e1;
        public static final int stretch = 0x7f0900e2;
        public static final int submenuarrow = 0x7f0900e3;
        public static final int submit_area = 0x7f0900e4;
        public static final int swhBlockAllNumber = 0x7f0900e5;
        public static final int swhBlockPrefixNumber = 0x7f0900e6;
        public static final int swhBlockSpecNumber = 0x7f0900e7;
        public static final int swhBlockTodayCall = 0x7f0900e8;
        public static final int swhBlockUnknownNumber = 0x7f0900e9;
        public static final int swhCallExplosion = 0x7f0900ea;
        public static final int swhShowPopupRemain = 0x7f0900eb;
        public static final int swhShowTodayCall = 0x7f0900ec;
        public static final int tabBlockCtrl = 0x7f0900ed;
        public static final int tabBlockHistory = 0x7f0900ee;
        public static final int tabBlockNumbers = 0x7f0900ef;
        public static final int tabBlockSetting = 0x7f0900f0;
        public static final int tabMode = 0x7f0900f1;
        public static final int tag_transition_group = 0x7f0900f2;
        public static final int tag_unhandled_key_event_manager = 0x7f0900f3;
        public static final int tag_unhandled_key_listeners = 0x7f0900f4;
        public static final int talkBT = 0x7f0900f5;
        public static final int text = 0x7f0900f6;
        public static final int text2 = 0x7f0900f7;
        public static final int textSpacerNoButtons = 0x7f0900f8;
        public static final int textSpacerNoTitle = 0x7f0900f9;
        public static final int textStart = 0x7f0900fa;
        public static final int textView = 0x7f0900fb;
        public static final int text_input_password_toggle = 0x7f0900fc;
        public static final int textinput_counter = 0x7f0900fd;
        public static final int textinput_error = 0x7f0900fe;
        public static final int textinput_helper_text = 0x7f0900ff;
        public static final int time = 0x7f090100;
        public static final int title = 0x7f090101;
        public static final int titleDividerNoCustom = 0x7f090102;
        public static final int titleTV = 0x7f090103;
        public static final int title_template = 0x7f090104;
        public static final int titlebar = 0x7f090105;
        public static final int toolbar = 0x7f090106;
        public static final int top = 0x7f090107;
        public static final int topPanel = 0x7f090108;
        public static final int touch_outside = 0x7f090109;
        public static final int transition_current_scene = 0x7f09010a;
        public static final int transition_layout_save = 0x7f09010b;
        public static final int transition_position = 0x7f09010c;
        public static final int transition_scene_layoutid_cache = 0x7f09010d;
        public static final int transition_transform = 0x7f09010e;
        public static final int txtBlockComment = 0x7f09010f;
        public static final int txtBlockLimit = 0x7f090110;
        public static final int txtCompany = 0x7f090111;
        public static final int txtContact = 0x7f090112;
        public static final int txtCount = 0x7f090113;
        public static final int txtDate = 0x7f090114;
        public static final int txtError = 0x7f090115;
        public static final int txtMemo = 0x7f090116;
        public static final int txtNew = 0x7f090117;
        public static final int txtNewNotice = 0x7f090118;
        public static final int txtNoticeContent = 0x7f090119;
        public static final int txtNoticeDate = 0x7f09011a;
        public static final int txtNoticeTitle = 0x7f09011b;
        public static final int txtPhoneNumber = 0x7f09011c;
        public static final int txtRemainDay = 0x7f09011d;
        public static final int txtResultCount = 0x7f09011e;
        public static final int txtResultPhoneNumber = 0x7f09011f;
        public static final int txtTotalCount = 0x7f090120;
        public static final int txtUpdateDate = 0x7f090121;
        public static final int txtUpdateTime = 0x7f090122;
        public static final int txt_blockcount = 0x7f090123;
        public static final int txt_phone = 0x7f090124;
        public static final int txt_phone2 = 0x7f090125;
        public static final int uniform = 0x7f090126;
        public static final int unlabeled = 0x7f090127;
        public static final int up = 0x7f090128;
        public static final int useLogo = 0x7f090129;
        public static final int viewDetail = 0x7f09012a;
        public static final int view_offset_helper = 0x7f09012b;
        public static final int visible = 0x7f09012c;
        public static final int webSearch = 0x7f09012d;
        public static final int webView = 0x7f09012e;
        public static final int withText = 0x7f09012f;
        public static final int wrap_content = 0x7f090130;
    }

    public static final class integer {
        public static final int abc_config_activityDefaultDur = 0x7f0a0000;
        public static final int abc_config_activityShortDur = 0x7f0a0001;
        public static final int app_bar_elevation_anim_duration = 0x7f0a0002;
        public static final int bottom_sheet_slide_duration = 0x7f0a0003;
        public static final int cancel_button_image_alpha = 0x7f0a0004;
        public static final int config_tooltipAnimTime = 0x7f0a0005;
        public static final int design_snackbar_text_max_lines = 0x7f0a0006;
        public static final int design_tab_indicator_anim_duration_ms = 0x7f0a0007;
        public static final int hide_password_duration = 0x7f0a0008;
        public static final int mtrl_btn_anim_delay_ms = 0x7f0a0009;
        public static final int mtrl_btn_anim_duration_ms = 0x7f0a000a;
        public static final int mtrl_chip_anim_duration = 0x7f0a000b;
        public static final int mtrl_tab_indicator_anim_duration_ms = 0x7f0a000c;
        public static final int show_password_duration = 0x7f0a000d;
        public static final int status_bar_notification_info_maxnum = 0x7f0a000e;
    }

    public static final class interpolator {
        public static final int mtrl_fast_out_linear_in = 0x7f0b0000;
        public static final int mtrl_fast_out_slow_in = 0x7f0b0001;
        public static final int mtrl_linear = 0x7f0b0002;
        public static final int mtrl_linear_out_slow_in = 0x7f0b0003;
    }

    public static final class layout {
        public static final int abc_action_bar_title_item = 0x7f0c0000;
        public static final int abc_action_bar_up_container = 0x7f0c0001;
        public static final int abc_action_menu_item_layout = 0x7f0c0002;
        public static final int abc_action_menu_layout = 0x7f0c0003;
        public static final int abc_action_mode_bar = 0x7f0c0004;
        public static final int abc_action_mode_close_item_material = 0x7f0c0005;
        public static final int abc_activity_chooser_view = 0x7f0c0006;
        public static final int abc_activity_chooser_view_list_item = 0x7f0c0007;
        public static final int abc_alert_dialog_button_bar_material = 0x7f0c0008;
        public static final int abc_alert_dialog_material = 0x7f0c0009;
        public static final int abc_alert_dialog_title_material = 0x7f0c000a;
        public static final int abc_cascading_menu_item_layout = 0x7f0c000b;
        public static final int abc_dialog_title_material = 0x7f0c000c;
        public static final int abc_expanded_menu_layout = 0x7f0c000d;
        public static final int abc_list_menu_item_checkbox = 0x7f0c000e;
        public static final int abc_list_menu_item_icon = 0x7f0c000f;
        public static final int abc_list_menu_item_layout = 0x7f0c0010;
        public static final int abc_list_menu_item_radio = 0x7f0c0011;
        public static final int abc_popup_menu_header_item_layout = 0x7f0c0012;
        public static final int abc_popup_menu_item_layout = 0x7f0c0013;
        public static final int abc_screen_content_include = 0x7f0c0014;
        public static final int abc_screen_simple = 0x7f0c0015;
        public static final int abc_screen_simple_overlay_action_mode = 0x7f0c0016;
        public static final int abc_screen_toolbar = 0x7f0c0017;
        public static final int abc_search_dropdown_item_icons_2line = 0x7f0c0018;
        public static final int abc_search_view = 0x7f0c0019;
        public static final int abc_select_dialog_material = 0x7f0c001a;
        public static final int abc_tooltip = 0x7f0c001b;
        public static final int activity_login = 0x7f0c001c;
        public static final int activity_main = 0x7f0c001d;
        public static final int adapter_blockhistory = 0x7f0c001e;
        public static final int adapter_blocklist = 0x7f0c001f;
        public static final int adapter_drawer = 0x7f0c0020;
        public static final int adapter_noticelist = 0x7f0c0021;
        public static final int adapter_phone = 0x7f0c0022;
        public static final int adapter_recentcalllist = 0x7f0c0023;
        public static final int app_bar_main = 0x7f0c0024;
        public static final int content_main = 0x7f0c0025;
        public static final int content_main_search = 0x7f0c0026;
        public static final int content_search_tabbar = 0x7f0c0027;
        public static final int design_bottom_navigation_item = 0x7f0c0028;
        public static final int design_bottom_sheet_dialog = 0x7f0c0029;
        public static final int design_layout_snackbar = 0x7f0c002a;
        public static final int design_layout_snackbar_include = 0x7f0c002b;
        public static final int design_layout_tab_icon = 0x7f0c002c;
        public static final int design_layout_tab_text = 0x7f0c002d;
        public static final int design_menu_item_action_area = 0x7f0c002e;
        public static final int design_navigation_item = 0x7f0c002f;
        public static final int design_navigation_item_header = 0x7f0c0030;
        public static final int design_navigation_item_separator = 0x7f0c0031;
        public static final int design_navigation_item_subheader = 0x7f0c0032;
        public static final int design_navigation_menu = 0x7f0c0033;
        public static final int design_navigation_menu_item = 0x7f0c0034;
        public static final int design_text_input_password_icon = 0x7f0c0035;
        public static final int fragment_block = 0x7f0c0036;
        public static final int fragment_block_history = 0x7f0c0037;
        public static final int fragment_block_numbers = 0x7f0c0038;
        public static final int fragment_block_setting = 0x7f0c0039;
        public static final int fragment_main = 0x7f0c003a;
        public static final int fragment_notice = 0x7f0c003b;
        public static final int fragment_search = 0x7f0c003c;
        public static final int fragment_setting = 0x7f0c003d;
        public static final int fragment_websearch = 0x7f0c003e;
        public static final int mtrl_layout_snackbar = 0x7f0c003f;
        public static final int mtrl_layout_snackbar_include = 0x7f0c0040;
        public static final int nav_header_main = 0x7f0c0041;
        public static final int notification_action = 0x7f0c0042;
        public static final int notification_action_tombstone = 0x7f0c0043;
        public static final int notification_media_action = 0x7f0c0044;
        public static final int notification_media_cancel_action = 0x7f0c0045;
        public static final int notification_template_big_media = 0x7f0c0046;
        public static final int notification_template_big_media_custom = 0x7f0c0047;
        public static final int notification_template_big_media_narrow = 0x7f0c0048;
        public static final int notification_template_big_media_narrow_custom = 0x7f0c0049;
        public static final int notification_template_custom_big = 0x7f0c004a;
        public static final int notification_template_icon_group = 0x7f0c004b;
        public static final int notification_template_lines_media = 0x7f0c004c;
        public static final int notification_template_media = 0x7f0c004d;
        public static final int notification_template_media_custom = 0x7f0c004e;
        public static final int notification_template_part_chronometer = 0x7f0c004f;
        public static final int notification_template_part_time = 0x7f0c0050;
        public static final int pop_up_notice = 0x7f0c0051;
        public static final int pop_up_window = 0x7f0c0052;
        public static final int popup_block_all = 0x7f0c0053;
        public static final int popup_blockcallexp = 0x7f0c0054;
        public static final int popup_blockprefnum = 0x7f0c0055;
        public static final int popup_blockspecnum = 0x7f0c0056;
        public static final int popup_blocktodaycall = 0x7f0c0057;
        public static final int popup_blockunknown = 0x7f0c0058;
        public static final int select_dialog_item_material = 0x7f0c0059;
        public static final int select_dialog_multichoice_material = 0x7f0c005a;
        public static final int select_dialog_singlechoice_material = 0x7f0c005b;
        public static final int support_simple_spinner_dropdown_item = 0x7f0c005c;
    }

    public static final class menu {
        public static final int main = 0x7f0d0000;
    }

    public static final class mipmap {
        public static final int android_call = 0x7f0e0000;
        public static final int android_question = 0x7f0e0001;
        public static final int arrow_right = 0x7f0e0002;
        public static final int call = 0x7f0e0003;
        public static final int email = 0x7f0e0004;
        public static final int ic_launcher = 0x7f0e0005;
        public static final int ic_launcher_round = 0x7f0e0006;
        public static final int line = 0x7f0e0007;
        public static final int logo = 0x7f0e0008;
        public static final int nav_cnt_back = 0x7f0e0009;
        public static final int settings = 0x7f0e000a;
        public static final int social_call = 0x7f0e000b;
        public static final int social_facebook = 0x7f0e000c;
        public static final int social_google = 0x7f0e000d;
        public static final int social_kakaotalk = 0x7f0e000e;
        public static final int social_line = 0x7f0e000f;
        public static final int social_sms = 0x7f0e0010;
    }

    public static final class string {
        public static final int abc_action_bar_home_description = 0x7f0f0000;
        public static final int abc_action_bar_up_description = 0x7f0f0001;
        public static final int abc_action_menu_overflow_description = 0x7f0f0002;
        public static final int abc_action_mode_done = 0x7f0f0003;
        public static final int abc_activity_chooser_view_see_all = 0x7f0f0004;
        public static final int abc_activitychooserview_choose_application = 0x7f0f0005;
        public static final int abc_capital_off = 0x7f0f0006;
        public static final int abc_capital_on = 0x7f0f0007;
        public static final int abc_font_family_body_1_material = 0x7f0f0008;
        public static final int abc_font_family_body_2_material = 0x7f0f0009;
        public static final int abc_font_family_button_material = 0x7f0f000a;
        public static final int abc_font_family_caption_material = 0x7f0f000b;
        public static final int abc_font_family_display_1_material = 0x7f0f000c;
        public static final int abc_font_family_display_2_material = 0x7f0f000d;
        public static final int abc_font_family_display_3_material = 0x7f0f000e;
        public static final int abc_font_family_display_4_material = 0x7f0f000f;
        public static final int abc_font_family_headline_material = 0x7f0f0010;
        public static final int abc_font_family_menu_material = 0x7f0f0011;
        public static final int abc_font_family_subhead_material = 0x7f0f0012;
        public static final int abc_font_family_title_material = 0x7f0f0013;
        public static final int abc_menu_alt_shortcut_label = 0x7f0f0014;
        public static final int abc_menu_ctrl_shortcut_label = 0x7f0f0015;
        public static final int abc_menu_delete_shortcut_label = 0x7f0f0016;
        public static final int abc_menu_enter_shortcut_label = 0x7f0f0017;
        public static final int abc_menu_function_shortcut_label = 0x7f0f0018;
        public static final int abc_menu_meta_shortcut_label = 0x7f0f0019;
        public static final int abc_menu_shift_shortcut_label = 0x7f0f001a;
        public static final int abc_menu_space_shortcut_label = 0x7f0f001b;
        public static final int abc_menu_sym_shortcut_label = 0x7f0f001c;
        public static final int abc_prepend_shortcut_label = 0x7f0f001d;
        public static final int abc_search_hint = 0x7f0f001e;
        public static final int abc_searchview_description_clear = 0x7f0f001f;
        public static final int abc_searchview_description_query = 0x7f0f0020;
        public static final int abc_searchview_description_search = 0x7f0f0021;
        public static final int abc_searchview_description_submit = 0x7f0f0022;
        public static final int abc_searchview_description_voice = 0x7f0f0023;
        public static final int abc_shareactionprovider_share_with = 0x7f0f0024;
        public static final int abc_shareactionprovider_share_with_application = 0x7f0f0025;
        public static final int abc_toolbar_collapse_description = 0x7f0f0026;
        public static final int action_settings = 0x7f0f0027;
        public static final int app_name = 0x7f0f0028;
        public static final int appbar_scrolling_view_behavior = 0x7f0f0029;
        public static final int blacklist_request_permissions = 0x7f0f002a;
        public static final int bottom_sheet_behavior = 0x7f0f002b;
        public static final int cancel = 0x7f0f002c;
        public static final int character_counter_content_description = 0x7f0f002d;
        public static final int character_counter_pattern = 0x7f0f002e;
        public static final int empty_data = 0x7f0f002f;
        public static final int fab_transformation_scrim_behavior = 0x7f0f0030;
        public static final int fab_transformation_sheet_behavior = 0x7f0f0031;
        public static final int hello_blank_fragment = 0x7f0f0032;
        public static final int hide_bottom_view_on_scroll_behavior = 0x7f0f0033;
        public static final int mtrl_chip_close_icon_content_description = 0x7f0f0034;
        public static final int navigation_drawer_close = 0x7f0f0035;
        public static final int navigation_drawer_open = 0x7f0f0036;
        public static final int noCallHistory = 0x7f0f0037;
        public static final int noData = 0x7f0f0038;
        public static final int password_toggle_content_description = 0x7f0f0039;
        public static final int path_password_eye = 0x7f0f003a;
        public static final int path_password_eye_mask_strike_through = 0x7f0f003b;
        public static final int path_password_eye_mask_visible = 0x7f0f003c;
        public static final int path_password_strike_through = 0x7f0f003d;
        public static final int permissions_required = 0x7f0f003e;
        public static final int phonesearch_item = 0x7f0f003f;
        public static final int popup_position = 0x7f0f0040;
        public static final int question_item = 0x7f0f0041;
        public static final int report_item = 0x7f0f0042;
        public static final int search_hint = 0x7f0f0043;
        public static final int search_menu_title = 0x7f0f0044;
        public static final int service_fail = 0x7f0f0045;
        public static final int setting = 0x7f0f0046;
        public static final int setting_IPlabel = 0x7f0f0047;
        public static final int setting_Portlabel = 0x7f0f0048;
        public static final int setting_title = 0x7f0f0049;
        public static final int status_bar_notification_info_overflow = 0x7f0f004a;
        public static final int tab_txt_name = 0x7f0f004b;
        public static final int tab_txt_number = 0x7f0f004c;
        public static final int text_cancel = 0x7f0f004d;
        public static final int text_ok = 0x7f0f004e;
        public static final int title_phoneNumber = 0x7f0f004f;
        public static final int today_call_request = 0x7f0f0050;
        public static final int user_id_hint = 0x7f0f0051;
        public static final int user_login = 0x7f0f0052;
        public static final int user_login_title = 0x7f0f0053;
        public static final int user_pass_hint = 0x7f0f0054;
        public static final int wait = 0x7f0f0055;
    }

    public static final class style {
        public static final int AlertDialog_AppCompat = 0x7f100000;
        public static final int AlertDialog_AppCompat_Light = 0x7f100001;
        public static final int Animation_AppCompat_Dialog = 0x7f100002;
        public static final int Animation_AppCompat_DropDownUp = 0x7f100003;
        public static final int Animation_AppCompat_Tooltip = 0x7f100004;
        public static final int Animation_Design_BottomSheetDialog = 0x7f100005;
        public static final int AppTheme = 0x7f100006;
        public static final int AppTheme_AppBarOverlay = 0x7f100007;
        public static final int AppTheme_NoActionBar = 0x7f100008;
        public static final int AppTheme_PopupOverlay = 0x7f100009;
        public static final int Base_AlertDialog_AppCompat = 0x7f10000a;
        public static final int Base_AlertDialog_AppCompat_Light = 0x7f10000b;
        public static final int Base_Animation_AppCompat_Dialog = 0x7f10000c;
        public static final int Base_Animation_AppCompat_DropDownUp = 0x7f10000d;
        public static final int Base_Animation_AppCompat_Tooltip = 0x7f10000e;
        public static final int Base_CardView = 0x7f10000f;
        public static final int Base_DialogWindowTitleBackground_AppCompat = 0x7f100011;
        public static final int Base_DialogWindowTitle_AppCompat = 0x7f100010;
        public static final int Base_TextAppearance_AppCompat = 0x7f100012;
        public static final int Base_TextAppearance_AppCompat_Body1 = 0x7f100013;
        public static final int Base_TextAppearance_AppCompat_Body2 = 0x7f100014;
        public static final int Base_TextAppearance_AppCompat_Button = 0x7f100015;
        public static final int Base_TextAppearance_AppCompat_Caption = 0x7f100016;
        public static final int Base_TextAppearance_AppCompat_Display1 = 0x7f100017;
        public static final int Base_TextAppearance_AppCompat_Display2 = 0x7f100018;
        public static final int Base_TextAppearance_AppCompat_Display3 = 0x7f100019;
        public static final int Base_TextAppearance_AppCompat_Display4 = 0x7f10001a;
        public static final int Base_TextAppearance_AppCompat_Headline = 0x7f10001b;
        public static final int Base_TextAppearance_AppCompat_Inverse = 0x7f10001c;
        public static final int Base_TextAppearance_AppCompat_Large = 0x7f10001d;
        public static final int Base_TextAppearance_AppCompat_Large_Inverse = 0x7f10001e;
        public static final int Base_TextAppearance_AppCompat_Light_Widget_PopupMenu_Large = 0x7f10001f;
        public static final int Base_TextAppearance_AppCompat_Light_Widget_PopupMenu_Small = 0x7f100020;
        public static final int Base_TextAppearance_AppCompat_Medium = 0x7f100021;
        public static final int Base_TextAppearance_AppCompat_Medium_Inverse = 0x7f100022;
        public static final int Base_TextAppearance_AppCompat_Menu = 0x7f100023;
        public static final int Base_TextAppearance_AppCompat_SearchResult = 0x7f100024;
        public static final int Base_TextAppearance_AppCompat_SearchResult_Subtitle = 0x7f100025;
        public static final int Base_TextAppearance_AppCompat_SearchResult_Title = 0x7f100026;
        public static final int Base_TextAppearance_AppCompat_Small = 0x7f100027;
        public static final int Base_TextAppearance_AppCompat_Small_Inverse = 0x7f100028;
        public static final int Base_TextAppearance_AppCompat_Subhead = 0x7f100029;
        public static final int Base_TextAppearance_AppCompat_Subhead_Inverse = 0x7f10002a;
        public static final int Base_TextAppearance_AppCompat_Title = 0x7f10002b;
        public static final int Base_TextAppearance_AppCompat_Title_Inverse = 0x7f10002c;
        public static final int Base_TextAppearance_AppCompat_Tooltip = 0x7f10002d;
        public static final int Base_TextAppearance_AppCompat_Widget_ActionBar_Menu = 0x7f10002e;
        public static final int Base_TextAppearance_AppCompat_Widget_ActionBar_Subtitle = 0x7f10002f;
        public static final int Base_TextAppearance_AppCompat_Widget_ActionBar_Subtitle_Inverse = 0x7f100030;
        public static final int Base_TextAppearance_AppCompat_Widget_ActionBar_Title = 0x7f100031;
        public static final int Base_TextAppearance_AppCompat_Widget_ActionBar_Title_Inverse = 0x7f100032;
        public static final int Base_TextAppearance_AppCompat_Widget_ActionMode_Subtitle = 0x7f100033;
        public static final int Base_TextAppearance_AppCompat_Widget_ActionMode_Title = 0x7f100034;
        public static final int Base_TextAppearance_AppCompat_Widget_Button = 0x7f100035;
        public static final int Base_TextAppearance_AppCompat_Widget_Button_Borderless_Colored = 0x7f100036;
        public static final int Base_TextAppearance_AppCompat_Widget_Button_Colored = 0x7f100037;
        public static final int Base_TextAppearance_AppCompat_Widget_Button_Inverse = 0x7f100038;
        public static final int Base_TextAppearance_AppCompat_Widget_DropDownItem = 0x7f100039;
        public static final int Base_TextAppearance_AppCompat_Widget_PopupMenu_Header = 0x7f10003a;
        public static final int Base_TextAppearance_AppCompat_Widget_PopupMenu_Large = 0x7f10003b;
        public static final int Base_TextAppearance_AppCompat_Widget_PopupMenu_Small = 0x7f10003c;
        public static final int Base_TextAppearance_AppCompat_Widget_Switch = 0x7f10003d;
        public static final int Base_TextAppearance_AppCompat_Widget_TextView_SpinnerItem = 0x7f10003e;
        public static final int Base_TextAppearance_Widget_AppCompat_ExpandedMenu_Item = 0x7f10003f;
        public static final int Base_TextAppearance_Widget_AppCompat_Toolbar_Subtitle = 0x7f100040;
        public static final int Base_TextAppearance_Widget_AppCompat_Toolbar_Title = 0x7f100041;
        public static final int Base_ThemeOverlay_AppCompat = 0x7f100061;
        public static final int Base_ThemeOverlay_AppCompat_ActionBar = 0x7f100062;
        public static final int Base_ThemeOverlay_AppCompat_Dark = 0x7f100063;
        public static final int Base_ThemeOverlay_AppCompat_Dark_ActionBar = 0x7f100064;
        public static final int Base_ThemeOverlay_AppCompat_Dialog = 0x7f100065;
        public static final int Base_ThemeOverlay_AppCompat_Dialog_Alert = 0x7f100066;
        public static final int Base_ThemeOverlay_AppCompat_Light = 0x7f100067;
        public static final int Base_ThemeOverlay_MaterialComponents_Dialog = 0x7f100068;
        public static final int Base_ThemeOverlay_MaterialComponents_Dialog_Alert = 0x7f100069;
        public static final int Base_Theme_AppCompat = 0x7f100042;
        public static final int Base_Theme_AppCompat_CompactMenu = 0x7f100043;
        public static final int Base_Theme_AppCompat_Dialog = 0x7f100044;
        public static final int Base_Theme_AppCompat_DialogWhenLarge = 0x7f100048;
        public static final int Base_Theme_AppCompat_Dialog_Alert = 0x7f100045;
        public static final int Base_Theme_AppCompat_Dialog_FixedSize = 0x7f100046;
        public static final int Base_Theme_AppCompat_Dialog_MinWidth = 0x7f100047;
        public static final int Base_Theme_AppCompat_Light = 0x7f100049;
        public static final int Base_Theme_AppCompat_Light_DarkActionBar = 0x7f10004a;
        public static final int Base_Theme_AppCompat_Light_Dialog = 0x7f10004b;
        public static final int Base_Theme_AppCompat_Light_DialogWhenLarge = 0x7f10004f;
        public static final int Base_Theme_AppCompat_Light_Dialog_Alert = 0x7f10004c;
        public static final int Base_Theme_AppCompat_Light_Dialog_FixedSize = 0x7f10004d;
        public static final int Base_Theme_AppCompat_Light_Dialog_MinWidth = 0x7f10004e;
        public static final int Base_Theme_MaterialComponents = 0x7f100050;
        public static final int Base_Theme_MaterialComponents_Bridge = 0x7f100051;
        public static final int Base_Theme_MaterialComponents_CompactMenu = 0x7f100052;
        public static final int Base_Theme_MaterialComponents_Dialog = 0x7f100053;
        public static final int Base_Theme_MaterialComponents_DialogWhenLarge = 0x7f100057;
        public static final int Base_Theme_MaterialComponents_Dialog_Alert = 0x7f100054;
        public static final int Base_Theme_MaterialComponents_Dialog_FixedSize = 0x7f100055;
        public static final int Base_Theme_MaterialComponents_Dialog_MinWidth = 0x7f100056;
        public static final int Base_Theme_MaterialComponents_Light = 0x7f100058;
        public static final int Base_Theme_MaterialComponents_Light_Bridge = 0x7f100059;
        public static final int Base_Theme_MaterialComponents_Light_DarkActionBar = 0x7f10005a;
        public static final int Base_Theme_MaterialComponents_Light_DarkActionBar_Bridge = 0x7f10005b;
        public static final int Base_Theme_MaterialComponents_Light_Dialog = 0x7f10005c;
        public static final int Base_Theme_MaterialComponents_Light_DialogWhenLarge = 0x7f100060;
        public static final int Base_Theme_MaterialComponents_Light_Dialog_Alert = 0x7f10005d;
        public static final int Base_Theme_MaterialComponents_Light_Dialog_FixedSize = 0x7f10005e;
        public static final int Base_Theme_MaterialComponents_Light_Dialog_MinWidth = 0x7f10005f;
        public static final int Base_V14_ThemeOverlay_MaterialComponents_Dialog = 0x7f100071;
        public static final int Base_V14_ThemeOverlay_MaterialComponents_Dialog_Alert = 0x7f100072;
        public static final int Base_V14_Theme_MaterialComponents = 0x7f10006a;
        public static final int Base_V14_Theme_MaterialComponents_Bridge = 0x7f10006b;
        public static final int Base_V14_Theme_MaterialComponents_Dialog = 0x7f10006c;
        public static final int Base_V14_Theme_MaterialComponents_Light = 0x7f10006d;
        public static final int Base_V14_Theme_MaterialComponents_Light_Bridge = 0x7f10006e;
        public static final int Base_V14_Theme_MaterialComponents_Light_DarkActionBar_Bridge = 0x7f10006f;
        public static final int Base_V14_Theme_MaterialComponents_Light_Dialog = 0x7f100070;
        public static final int Base_V21_ThemeOverlay_AppCompat_Dialog = 0x7f100077;
        public static final int Base_V21_Theme_AppCompat = 0x7f100073;
        public static final int Base_V21_Theme_AppCompat_Dialog = 0x7f100074;
        public static final int Base_V21_Theme_AppCompat_Light = 0x7f100075;
        public static final int Base_V21_Theme_AppCompat_Light_Dialog = 0x7f100076;
        public static final int Base_V22_Theme_AppCompat = 0x7f100078;
        public static final int Base_V22_Theme_AppCompat_Light = 0x7f100079;
        public static final int Base_V23_Theme_AppCompat = 0x7f10007a;
        public static final int Base_V23_Theme_AppCompat_Light = 0x7f10007b;
        public static final int Base_V26_Theme_AppCompat = 0x7f10007c;
        public static final int Base_V26_Theme_AppCompat_Light = 0x7f10007d;
        public static final int Base_V26_Widget_AppCompat_Toolbar = 0x7f10007e;
        public static final int Base_V28_Theme_AppCompat = 0x7f10007f;
        public static final int Base_V28_Theme_AppCompat_Light = 0x7f100080;
        public static final int Base_V7_ThemeOverlay_AppCompat_Dialog = 0x7f100085;
        public static final int Base_V7_Theme_AppCompat = 0x7f100081;
        public static final int Base_V7_Theme_AppCompat_Dialog = 0x7f100082;
        public static final int Base_V7_Theme_AppCompat_Light = 0x7f100083;
        public static final int Base_V7_Theme_AppCompat_Light_Dialog = 0x7f100084;
        public static final int Base_V7_Widget_AppCompat_AutoCompleteTextView = 0x7f100086;
        public static final int Base_V7_Widget_AppCompat_EditText = 0x7f100087;
        public static final int Base_V7_Widget_AppCompat_Toolbar = 0x7f100088;
        public static final int Base_Widget_AppCompat_ActionBar = 0x7f100089;
        public static final int Base_Widget_AppCompat_ActionBar_Solid = 0x7f10008a;
        public static final int Base_Widget_AppCompat_ActionBar_TabBar = 0x7f10008b;
        public static final int Base_Widget_AppCompat_ActionBar_TabText = 0x7f10008c;
        public static final int Base_Widget_AppCompat_ActionBar_TabView = 0x7f10008d;
        public static final int Base_Widget_AppCompat_ActionButton = 0x7f10008e;
        public static final int Base_Widget_AppCompat_ActionButton_CloseMode = 0x7f10008f;
        public static final int Base_Widget_AppCompat_ActionButton_Overflow = 0x7f100090;
        public static final int Base_Widget_AppCompat_ActionMode = 0x7f100091;
        public static final int Base_Widget_AppCompat_ActivityChooserView = 0x7f100092;
        public static final int Base_Widget_AppCompat_AutoCompleteTextView = 0x7f100093;
        public static final int Base_Widget_AppCompat_Button = 0x7f100094;
        public static final int Base_Widget_AppCompat_ButtonBar = 0x7f10009a;
        public static final int Base_Widget_AppCompat_ButtonBar_AlertDialog = 0x7f10009b;
        public static final int Base_Widget_AppCompat_Button_Borderless = 0x7f100095;
        public static final int Base_Widget_AppCompat_Button_Borderless_Colored = 0x7f100096;
        public static final int Base_Widget_AppCompat_Button_ButtonBar_AlertDialog = 0x7f100097;
        public static final int Base_Widget_AppCompat_Button_Colored = 0x7f100098;
        public static final int Base_Widget_AppCompat_Button_Small = 0x7f100099;
        public static final int Base_Widget_AppCompat_CompoundButton_CheckBox = 0x7f10009c;
        public static final int Base_Widget_AppCompat_CompoundButton_RadioButton = 0x7f10009d;
        public static final int Base_Widget_AppCompat_CompoundButton_Switch = 0x7f10009e;
        public static final int Base_Widget_AppCompat_DrawerArrowToggle = 0x7f10009f;
        public static final int Base_Widget_AppCompat_DrawerArrowToggle_Common = 0x7f1000a0;
        public static final int Base_Widget_AppCompat_DropDownItem_Spinner = 0x7f1000a1;
        public static final int Base_Widget_AppCompat_EditText = 0x7f1000a2;
        public static final int Base_Widget_AppCompat_ImageButton = 0x7f1000a3;
        public static final int Base_Widget_AppCompat_Light_ActionBar = 0x7f1000a4;
        public static final int Base_Widget_AppCompat_Light_ActionBar_Solid = 0x7f1000a5;
        public static final int Base_Widget_AppCompat_Light_ActionBar_TabBar = 0x7f1000a6;
        public static final int Base_Widget_AppCompat_Light_ActionBar_TabText = 0x7f1000a7;
        public static final int Base_Widget_AppCompat_Light_ActionBar_TabText_Inverse = 0x7f1000a8;
        public static final int Base_Widget_AppCompat_Light_ActionBar_TabView = 0x7f1000a9;
        public static final int Base_Widget_AppCompat_Light_PopupMenu = 0x7f1000aa;
        public static final int Base_Widget_AppCompat_Light_PopupMenu_Overflow = 0x7f1000ab;
        public static final int Base_Widget_AppCompat_ListMenuView = 0x7f1000ac;
        public static final int Base_Widget_AppCompat_ListPopupWindow = 0x7f1000ad;
        public static final int Base_Widget_AppCompat_ListView = 0x7f1000ae;
        public static final int Base_Widget_AppCompat_ListView_DropDown = 0x7f1000af;
        public static final int Base_Widget_AppCompat_ListView_Menu = 0x7f1000b0;
        public static final int Base_Widget_AppCompat_PopupMenu = 0x7f1000b1;
        public static final int Base_Widget_AppCompat_PopupMenu_Overflow = 0x7f1000b2;
        public static final int Base_Widget_AppCompat_PopupWindow = 0x7f1000b3;
        public static final int Base_Widget_AppCompat_ProgressBar = 0x7f1000b4;
        public static final int Base_Widget_AppCompat_ProgressBar_Horizontal = 0x7f1000b5;
        public static final int Base_Widget_AppCompat_RatingBar = 0x7f1000b6;
        public static final int Base_Widget_AppCompat_RatingBar_Indicator = 0x7f1000b7;
        public static final int Base_Widget_AppCompat_RatingBar_Small = 0x7f1000b8;
        public static final int Base_Widget_AppCompat_SearchView = 0x7f1000b9;
        public static final int Base_Widget_AppCompat_SearchView_ActionBar = 0x7f1000ba;
        public static final int Base_Widget_AppCompat_SeekBar = 0x7f1000bb;
        public static final int Base_Widget_AppCompat_SeekBar_Discrete = 0x7f1000bc;
        public static final int Base_Widget_AppCompat_Spinner = 0x7f1000bd;
        public static final int Base_Widget_AppCompat_Spinner_Underlined = 0x7f1000be;
        public static final int Base_Widget_AppCompat_TextView_SpinnerItem = 0x7f1000bf;
        public static final int Base_Widget_AppCompat_Toolbar = 0x7f1000c0;
        public static final int Base_Widget_AppCompat_Toolbar_Button_Navigation = 0x7f1000c1;
        public static final int Base_Widget_Design_TabLayout = 0x7f1000c2;
        public static final int Base_Widget_MaterialComponents_Chip = 0x7f1000c3;
        public static final int Base_Widget_MaterialComponents_TextInputEditText = 0x7f1000c4;
        public static final int Base_Widget_MaterialComponents_TextInputLayout = 0x7f1000c5;
        public static final int CardView = 0x7f1000c6;
        public static final int CardView_Dark = 0x7f1000c7;
        public static final int CardView_Light = 0x7f1000c8;
        public static final int Platform_AppCompat = 0x7f1000c9;
        public static final int Platform_AppCompat_Light = 0x7f1000ca;
        public static final int Platform_MaterialComponents = 0x7f1000cb;
        public static final int Platform_MaterialComponents_Dialog = 0x7f1000cc;
        public static final int Platform_MaterialComponents_Light = 0x7f1000cd;
        public static final int Platform_MaterialComponents_Light_Dialog = 0x7f1000ce;
        public static final int Platform_ThemeOverlay_AppCompat = 0x7f1000cf;
        public static final int Platform_ThemeOverlay_AppCompat_Dark = 0x7f1000d0;
        public static final int Platform_ThemeOverlay_AppCompat_Light = 0x7f1000d1;
        public static final int Platform_V21_AppCompat = 0x7f1000d2;
        public static final int Platform_V21_AppCompat_Light = 0x7f1000d3;
        public static final int Platform_V25_AppCompat = 0x7f1000d4;
        public static final int Platform_V25_AppCompat_Light = 0x7f1000d5;
        public static final int Platform_Widget_AppCompat_Spinner = 0x7f1000d6;
        public static final int RtlOverlay_DialogWindowTitle_AppCompat = 0x7f1000d7;
        public static final int RtlOverlay_Widget_AppCompat_ActionBar_TitleItem = 0x7f1000d8;
        public static final int RtlOverlay_Widget_AppCompat_DialogTitle_Icon = 0x7f1000d9;
        public static final int RtlOverlay_Widget_AppCompat_PopupMenuItem = 0x7f1000da;
        public static final int RtlOverlay_Widget_AppCompat_PopupMenuItem_InternalGroup = 0x7f1000db;
        public static final int RtlOverlay_Widget_AppCompat_PopupMenuItem_Shortcut = 0x7f1000dc;
        public static final int RtlOverlay_Widget_AppCompat_PopupMenuItem_SubmenuArrow = 0x7f1000dd;
        public static final int RtlOverlay_Widget_AppCompat_PopupMenuItem_Text = 0x7f1000de;
        public static final int RtlOverlay_Widget_AppCompat_PopupMenuItem_Title = 0x7f1000df;
        public static final int RtlOverlay_Widget_AppCompat_SearchView_MagIcon = 0x7f1000e5;
        public static final int RtlOverlay_Widget_AppCompat_Search_DropDown = 0x7f1000e0;
        public static final int RtlOverlay_Widget_AppCompat_Search_DropDown_Icon1 = 0x7f1000e1;
        public static final int RtlOverlay_Widget_AppCompat_Search_DropDown_Icon2 = 0x7f1000e2;
        public static final int RtlOverlay_Widget_AppCompat_Search_DropDown_Query = 0x7f1000e3;
        public static final int RtlOverlay_Widget_AppCompat_Search_DropDown_Text = 0x7f1000e4;
        public static final int RtlUnderlay_Widget_AppCompat_ActionButton = 0x7f1000e6;
        public static final int RtlUnderlay_Widget_AppCompat_ActionButton_Overflow = 0x7f1000e7;
        public static final int TextAppearance_AppCompat = 0x7f1000e8;
        public static final int TextAppearance_AppCompat_Body1 = 0x7f1000e9;
        public static final int TextAppearance_AppCompat_Body2 = 0x7f1000ea;
        public static final int TextAppearance_AppCompat_Button = 0x7f1000eb;
        public static final int TextAppearance_AppCompat_Caption = 0x7f1000ec;
        public static final int TextAppearance_AppCompat_Display1 = 0x7f1000ed;
        public static final int TextAppearance_AppCompat_Display2 = 0x7f1000ee;
        public static final int TextAppearance_AppCompat_Display3 = 0x7f1000ef;
        public static final int TextAppearance_AppCompat_Display4 = 0x7f1000f0;
        public static final int TextAppearance_AppCompat_Headline = 0x7f1000f1;
        public static final int TextAppearance_AppCompat_Inverse = 0x7f1000f2;
        public static final int TextAppearance_AppCompat_Large = 0x7f1000f3;
        public static final int TextAppearance_AppCompat_Large_Inverse = 0x7f1000f4;
        public static final int TextAppearance_AppCompat_Light_SearchResult_Subtitle = 0x7f1000f5;
        public static final int TextAppearance_AppCompat_Light_SearchResult_Title = 0x7f1000f6;
        public static final int TextAppearance_AppCompat_Light_Widget_PopupMenu_Large = 0x7f1000f7;
        public static final int TextAppearance_AppCompat_Light_Widget_PopupMenu_Small = 0x7f1000f8;
        public static final int TextAppearance_AppCompat_Medium = 0x7f1000f9;
        public static final int TextAppearance_AppCompat_Medium_Inverse = 0x7f1000fa;
        public static final int TextAppearance_AppCompat_Menu = 0x7f1000fb;
        public static final int TextAppearance_AppCompat_SearchResult_Subtitle = 0x7f1000fc;
        public static final int TextAppearance_AppCompat_SearchResult_Title = 0x7f1000fd;
        public static final int TextAppearance_AppCompat_Small = 0x7f1000fe;
        public static final int TextAppearance_AppCompat_Small_Inverse = 0x7f1000ff;
        public static final int TextAppearance_AppCompat_Subhead = 0x7f100100;
        public static final int TextAppearance_AppCompat_Subhead_Inverse = 0x7f100101;
        public static final int TextAppearance_AppCompat_Title = 0x7f100102;
        public static final int TextAppearance_AppCompat_Title_Inverse = 0x7f100103;
        public static final int TextAppearance_AppCompat_Tooltip = 0x7f100104;
        public static final int TextAppearance_AppCompat_Widget_ActionBar_Menu = 0x7f100105;
        public static final int TextAppearance_AppCompat_Widget_ActionBar_Subtitle = 0x7f100106;
        public static final int TextAppearance_AppCompat_Widget_ActionBar_Subtitle_Inverse = 0x7f100107;
        public static final int TextAppearance_AppCompat_Widget_ActionBar_Title = 0x7f100108;
        public static final int TextAppearance_AppCompat_Widget_ActionBar_Title_Inverse = 0x7f100109;
        public static final int TextAppearance_AppCompat_Widget_ActionMode_Subtitle = 0x7f10010a;
        public static final int TextAppearance_AppCompat_Widget_ActionMode_Subtitle_Inverse = 0x7f10010b;
        public static final int TextAppearance_AppCompat_Widget_ActionMode_Title = 0x7f10010c;
        public static final int TextAppearance_AppCompat_Widget_ActionMode_Title_Inverse = 0x7f10010d;
        public static final int TextAppearance_AppCompat_Widget_Button = 0x7f10010e;
        public static final int TextAppearance_AppCompat_Widget_Button_Borderless_Colored = 0x7f10010f;
        public static final int TextAppearance_AppCompat_Widget_Button_Colored = 0x7f100110;
        public static final int TextAppearance_AppCompat_Widget_Button_Inverse = 0x7f100111;
        public static final int TextAppearance_AppCompat_Widget_DropDownItem = 0x7f100112;
        public static final int TextAppearance_AppCompat_Widget_PopupMenu_Header = 0x7f100113;
        public static final int TextAppearance_AppCompat_Widget_PopupMenu_Large = 0x7f100114;
        public static final int TextAppearance_AppCompat_Widget_PopupMenu_Small = 0x7f100115;
        public static final int TextAppearance_AppCompat_Widget_Switch = 0x7f100116;
        public static final int TextAppearance_AppCompat_Widget_TextView_SpinnerItem = 0x7f100117;
        public static final int TextAppearance_Compat_Notification = 0x7f100118;
        public static final int TextAppearance_Compat_Notification_Info = 0x7f100119;
        public static final int TextAppearance_Compat_Notification_Info_Media = 0x7f10011a;
        public static final int TextAppearance_Compat_Notification_Line2 = 0x7f10011b;
        public static final int TextAppearance_Compat_Notification_Line2_Media = 0x7f10011c;
        public static final int TextAppearance_Compat_Notification_Media = 0x7f10011d;
        public static final int TextAppearance_Compat_Notification_Time = 0x7f10011e;
        public static final int TextAppearance_Compat_Notification_Time_Media = 0x7f10011f;
        public static final int TextAppearance_Compat_Notification_Title = 0x7f100120;
        public static final int TextAppearance_Compat_Notification_Title_Media = 0x7f100121;
        public static final int TextAppearance_Design_CollapsingToolbar_Expanded = 0x7f100122;
        public static final int TextAppearance_Design_Counter = 0x7f100123;
        public static final int TextAppearance_Design_Counter_Overflow = 0x7f100124;
        public static final int TextAppearance_Design_Error = 0x7f100125;
        public static final int TextAppearance_Design_HelperText = 0x7f100126;
        public static final int TextAppearance_Design_Hint = 0x7f100127;
        public static final int TextAppearance_Design_Snackbar_Message = 0x7f100128;
        public static final int TextAppearance_Design_Tab = 0x7f100129;
        public static final int TextAppearance_MaterialComponents_Body1 = 0x7f10012a;
        public static final int TextAppearance_MaterialComponents_Body2 = 0x7f10012b;
        public static final int TextAppearance_MaterialComponents_Button = 0x7f10012c;
        public static final int TextAppearance_MaterialComponents_Caption = 0x7f10012d;
        public static final int TextAppearance_MaterialComponents_Chip = 0x7f10012e;
        public static final int TextAppearance_MaterialComponents_Headline1 = 0x7f10012f;
        public static final int TextAppearance_MaterialComponents_Headline2 = 0x7f100130;
        public static final int TextAppearance_MaterialComponents_Headline3 = 0x7f100131;
        public static final int TextAppearance_MaterialComponents_Headline4 = 0x7f100132;
        public static final int TextAppearance_MaterialComponents_Headline5 = 0x7f100133;
        public static final int TextAppearance_MaterialComponents_Headline6 = 0x7f100134;
        public static final int TextAppearance_MaterialComponents_Overline = 0x7f100135;
        public static final int TextAppearance_MaterialComponents_Subtitle1 = 0x7f100136;
        public static final int TextAppearance_MaterialComponents_Subtitle2 = 0x7f100137;
        public static final int TextAppearance_MaterialComponents_Tab = 0x7f100138;
        public static final int TextAppearance_Widget_AppCompat_ExpandedMenu_Item = 0x7f100139;
        public static final int TextAppearance_Widget_AppCompat_Toolbar_Subtitle = 0x7f10013a;
        public static final int TextAppearance_Widget_AppCompat_Toolbar_Title = 0x7f10013b;
        public static final int ThemeOverlay_AppCompat = 0x7f10016c;
        public static final int ThemeOverlay_AppCompat_ActionBar = 0x7f10016d;
        public static final int ThemeOverlay_AppCompat_Dark = 0x7f10016e;
        public static final int ThemeOverlay_AppCompat_Dark_ActionBar = 0x7f10016f;
        public static final int ThemeOverlay_AppCompat_Dialog = 0x7f100170;
        public static final int ThemeOverlay_AppCompat_Dialog_Alert = 0x7f100171;
        public static final int ThemeOverlay_AppCompat_Light = 0x7f100172;
        public static final int ThemeOverlay_MaterialComponents = 0x7f100173;
        public static final int ThemeOverlay_MaterialComponents_ActionBar = 0x7f100174;
        public static final int ThemeOverlay_MaterialComponents_Dark = 0x7f100175;
        public static final int ThemeOverlay_MaterialComponents_Dark_ActionBar = 0x7f100176;
        public static final int ThemeOverlay_MaterialComponents_Dialog = 0x7f100177;
        public static final int ThemeOverlay_MaterialComponents_Dialog_Alert = 0x7f100178;
        public static final int ThemeOverlay_MaterialComponents_Light = 0x7f100179;
        public static final int ThemeOverlay_MaterialComponents_TextInputEditText = 0x7f10017a;
        public static final int ThemeOverlay_MaterialComponents_TextInputEditText_FilledBox = 0x7f10017b;
        public static final int ThemeOverlay_MaterialComponents_TextInputEditText_FilledBox_Dense = 0x7f10017c;
        public static final int ThemeOverlay_MaterialComponents_TextInputEditText_OutlinedBox = 0x7f10017d;
        public static final int ThemeOverlay_MaterialComponents_TextInputEditText_OutlinedBox_Dense = 0x7f10017e;
        public static final int Theme_AppCompat = 0x7f10013c;
        public static final int Theme_AppCompat_CompactMenu = 0x7f10013d;
        public static final int Theme_AppCompat_DayNight = 0x7f10013e;
        public static final int Theme_AppCompat_DayNight_DarkActionBar = 0x7f10013f;
        public static final int Theme_AppCompat_DayNight_Dialog = 0x7f100140;
        public static final int Theme_AppCompat_DayNight_DialogWhenLarge = 0x7f100143;
        public static final int Theme_AppCompat_DayNight_Dialog_Alert = 0x7f100141;
        public static final int Theme_AppCompat_DayNight_Dialog_MinWidth = 0x7f100142;
        public static final int Theme_AppCompat_DayNight_NoActionBar = 0x7f100144;
        public static final int Theme_AppCompat_Dialog = 0x7f100145;
        public static final int Theme_AppCompat_DialogWhenLarge = 0x7f100148;
        public static final int Theme_AppCompat_Dialog_Alert = 0x7f100146;
        public static final int Theme_AppCompat_Dialog_MinWidth = 0x7f100147;
        public static final int Theme_AppCompat_Light = 0x7f100149;
        public static final int Theme_AppCompat_Light_DarkActionBar = 0x7f10014a;
        public static final int Theme_AppCompat_Light_Dialog = 0x7f10014b;
        public static final int Theme_AppCompat_Light_DialogWhenLarge = 0x7f10014e;
        public static final int Theme_AppCompat_Light_Dialog_Alert = 0x7f10014c;
        public static final int Theme_AppCompat_Light_Dialog_MinWidth = 0x7f10014d;
        public static final int Theme_AppCompat_Light_NoActionBar = 0x7f10014f;
        public static final int Theme_AppCompat_NoActionBar = 0x7f100150;
        public static final int Theme_Design = 0x7f100151;
        public static final int Theme_Design_BottomSheetDialog = 0x7f100152;
        public static final int Theme_Design_Light = 0x7f100153;
        public static final int Theme_Design_Light_BottomSheetDialog = 0x7f100154;
        public static final int Theme_Design_Light_NoActionBar = 0x7f100155;
        public static final int Theme_Design_NoActionBar = 0x7f100156;
        public static final int Theme_MaterialComponents = 0x7f100157;
        public static final int Theme_MaterialComponents_BottomSheetDialog = 0x7f100158;
        public static final int Theme_MaterialComponents_Bridge = 0x7f100159;
        public static final int Theme_MaterialComponents_CompactMenu = 0x7f10015a;
        public static final int Theme_MaterialComponents_Dialog = 0x7f10015b;
        public static final int Theme_MaterialComponents_DialogWhenLarge = 0x7f10015e;
        public static final int Theme_MaterialComponents_Dialog_Alert = 0x7f10015c;
        public static final int Theme_MaterialComponents_Dialog_MinWidth = 0x7f10015d;
        public static final int Theme_MaterialComponents_Light = 0x7f10015f;
        public static final int Theme_MaterialComponents_Light_BottomSheetDialog = 0x7f100160;
        public static final int Theme_MaterialComponents_Light_Bridge = 0x7f100161;
        public static final int Theme_MaterialComponents_Light_DarkActionBar = 0x7f100162;
        public static final int Theme_MaterialComponents_Light_DarkActionBar_Bridge = 0x7f100163;
        public static final int Theme_MaterialComponents_Light_Dialog = 0x7f100164;
        public static final int Theme_MaterialComponents_Light_DialogWhenLarge = 0x7f100167;
        public static final int Theme_MaterialComponents_Light_Dialog_Alert = 0x7f100165;
        public static final int Theme_MaterialComponents_Light_Dialog_MinWidth = 0x7f100166;
        public static final int Theme_MaterialComponents_Light_NoActionBar = 0x7f100168;
        public static final int Theme_MaterialComponents_Light_NoActionBar_Bridge = 0x7f100169;
        public static final int Theme_MaterialComponents_NoActionBar = 0x7f10016a;
        public static final int Theme_MaterialComponents_NoActionBar_Bridge = 0x7f10016b;
        public static final int ToolbarColoredBackArrow = 0x7f10017f;
        public static final int Widget_AppCompat_ActionBar = 0x7f100180;
        public static final int Widget_AppCompat_ActionBar_Solid = 0x7f100181;
        public static final int Widget_AppCompat_ActionBar_TabBar = 0x7f100182;
        public static final int Widget_AppCompat_ActionBar_TabText = 0x7f100183;
        public static final int Widget_AppCompat_ActionBar_TabView = 0x7f100184;
        public static final int Widget_AppCompat_ActionButton = 0x7f100185;
        public static final int Widget_AppCompat_ActionButton_CloseMode = 0x7f100186;
        public static final int Widget_AppCompat_ActionButton_Overflow = 0x7f100187;
        public static final int Widget_AppCompat_ActionMode = 0x7f100188;
        public static final int Widget_AppCompat_ActivityChooserView = 0x7f100189;
        public static final int Widget_AppCompat_AutoCompleteTextView = 0x7f10018a;
        public static final int Widget_AppCompat_Button = 0x7f10018b;
        public static final int Widget_AppCompat_ButtonBar = 0x7f100191;
        public static final int Widget_AppCompat_ButtonBar_AlertDialog = 0x7f100192;
        public static final int Widget_AppCompat_Button_Borderless = 0x7f10018c;
        public static final int Widget_AppCompat_Button_Borderless_Colored = 0x7f10018d;
        public static final int Widget_AppCompat_Button_ButtonBar_AlertDialog = 0x7f10018e;
        public static final int Widget_AppCompat_Button_Colored = 0x7f10018f;
        public static final int Widget_AppCompat_Button_Small = 0x7f100190;
        public static final int Widget_AppCompat_CompoundButton_CheckBox = 0x7f100193;
        public static final int Widget_AppCompat_CompoundButton_RadioButton = 0x7f100194;
        public static final int Widget_AppCompat_CompoundButton_Switch = 0x7f100195;
        public static final int Widget_AppCompat_DrawerArrowToggle = 0x7f100196;
        public static final int Widget_AppCompat_DropDownItem_Spinner = 0x7f100197;
        public static final int Widget_AppCompat_EditText = 0x7f100198;
        public static final int Widget_AppCompat_ImageButton = 0x7f100199;
        public static final int Widget_AppCompat_Light_ActionBar = 0x7f10019a;
        public static final int Widget_AppCompat_Light_ActionBar_Solid = 0x7f10019b;
        public static final int Widget_AppCompat_Light_ActionBar_Solid_Inverse = 0x7f10019c;
        public static final int Widget_AppCompat_Light_ActionBar_TabBar = 0x7f10019d;
        public static final int Widget_AppCompat_Light_ActionBar_TabBar_Inverse = 0x7f10019e;
        public static final int Widget_AppCompat_Light_ActionBar_TabText = 0x7f10019f;
        public static final int Widget_AppCompat_Light_ActionBar_TabText_Inverse = 0x7f1001a0;
        public static final int Widget_AppCompat_Light_ActionBar_TabView = 0x7f1001a1;
        public static final int Widget_AppCompat_Light_ActionBar_TabView_Inverse = 0x7f1001a2;
        public static final int Widget_AppCompat_Light_ActionButton = 0x7f1001a3;
        public static final int Widget_AppCompat_Light_ActionButton_CloseMode = 0x7f1001a4;
        public static final int Widget_AppCompat_Light_ActionButton_Overflow = 0x7f1001a5;
        public static final int Widget_AppCompat_Light_ActionMode_Inverse = 0x7f1001a6;
        public static final int Widget_AppCompat_Light_ActivityChooserView = 0x7f1001a7;
        public static final int Widget_AppCompat_Light_AutoCompleteTextView = 0x7f1001a8;
        public static final int Widget_AppCompat_Light_DropDownItem_Spinner = 0x7f1001a9;
        public static final int Widget_AppCompat_Light_ListPopupWindow = 0x7f1001aa;
        public static final int Widget_AppCompat_Light_ListView_DropDown = 0x7f1001ab;
        public static final int Widget_AppCompat_Light_PopupMenu = 0x7f1001ac;
        public static final int Widget_AppCompat_Light_PopupMenu_Overflow = 0x7f1001ad;
        public static final int Widget_AppCompat_Light_SearchView = 0x7f1001ae;
        public static final int Widget_AppCompat_Light_Spinner_DropDown_ActionBar = 0x7f1001af;
        public static final int Widget_AppCompat_ListMenuView = 0x7f1001b0;
        public static final int Widget_AppCompat_ListPopupWindow = 0x7f1001b1;
        public static final int Widget_AppCompat_ListView = 0x7f1001b2;
        public static final int Widget_AppCompat_ListView_DropDown = 0x7f1001b3;
        public static final int Widget_AppCompat_ListView_Menu = 0x7f1001b4;
        public static final int Widget_AppCompat_PopupMenu = 0x7f1001b5;
        public static final int Widget_AppCompat_PopupMenu_Overflow = 0x7f1001b6;
        public static final int Widget_AppCompat_PopupWindow = 0x7f1001b7;
        public static final int Widget_AppCompat_ProgressBar = 0x7f1001b8;
        public static final int Widget_AppCompat_ProgressBar_Horizontal = 0x7f1001b9;
        public static final int Widget_AppCompat_RatingBar = 0x7f1001ba;
        public static final int Widget_AppCompat_RatingBar_Indicator = 0x7f1001bb;
        public static final int Widget_AppCompat_RatingBar_Small = 0x7f1001bc;
        public static final int Widget_AppCompat_SearchView = 0x7f1001bd;
        public static final int Widget_AppCompat_SearchView_ActionBar = 0x7f1001be;
        public static final int Widget_AppCompat_SeekBar = 0x7f1001bf;
        public static final int Widget_AppCompat_SeekBar_Discrete = 0x7f1001c0;
        public static final int Widget_AppCompat_Spinner = 0x7f1001c1;
        public static final int Widget_AppCompat_Spinner_DropDown = 0x7f1001c2;
        public static final int Widget_AppCompat_Spinner_DropDown_ActionBar = 0x7f1001c3;
        public static final int Widget_AppCompat_Spinner_Underlined = 0x7f1001c4;
        public static final int Widget_AppCompat_TextView_SpinnerItem = 0x7f1001c5;
        public static final int Widget_AppCompat_Toolbar = 0x7f1001c6;
        public static final int Widget_AppCompat_Toolbar_Button_Navigation = 0x7f1001c7;
        public static final int Widget_Compat_NotificationActionContainer = 0x7f1001c8;
        public static final int Widget_Compat_NotificationActionText = 0x7f1001c9;
        public static final int Widget_Design_AppBarLayout = 0x7f1001ca;
        public static final int Widget_Design_BottomNavigationView = 0x7f1001cb;
        public static final int Widget_Design_BottomSheet_Modal = 0x7f1001cc;
        public static final int Widget_Design_CollapsingToolbar = 0x7f1001cd;
        public static final int Widget_Design_FloatingActionButton = 0x7f1001ce;
        public static final int Widget_Design_NavigationView = 0x7f1001cf;
        public static final int Widget_Design_ScrimInsetsFrameLayout = 0x7f1001d0;
        public static final int Widget_Design_Snackbar = 0x7f1001d1;
        public static final int Widget_Design_TabLayout = 0x7f1001d2;
        public static final int Widget_Design_TextInputLayout = 0x7f1001d3;
        public static final int Widget_MaterialComponents_BottomAppBar = 0x7f1001d4;
        public static final int Widget_MaterialComponents_BottomAppBar_Colored = 0x7f1001d5;
        public static final int Widget_MaterialComponents_BottomNavigationView = 0x7f1001d6;
        public static final int Widget_MaterialComponents_BottomNavigationView_Colored = 0x7f1001d7;
        public static final int Widget_MaterialComponents_BottomSheet_Modal = 0x7f1001d8;
        public static final int Widget_MaterialComponents_Button = 0x7f1001d9;
        public static final int Widget_MaterialComponents_Button_Icon = 0x7f1001da;
        public static final int Widget_MaterialComponents_Button_OutlinedButton = 0x7f1001db;
        public static final int Widget_MaterialComponents_Button_OutlinedButton_Icon = 0x7f1001dc;
        public static final int Widget_MaterialComponents_Button_TextButton = 0x7f1001dd;
        public static final int Widget_MaterialComponents_Button_TextButton_Dialog = 0x7f1001de;
        public static final int Widget_MaterialComponents_Button_TextButton_Dialog_Icon = 0x7f1001df;
        public static final int Widget_MaterialComponents_Button_TextButton_Icon = 0x7f1001e0;
        public static final int Widget_MaterialComponents_Button_UnelevatedButton = 0x7f1001e1;
        public static final int Widget_MaterialComponents_Button_UnelevatedButton_Icon = 0x7f1001e2;
        public static final int Widget_MaterialComponents_CardView = 0x7f1001e3;
        public static final int Widget_MaterialComponents_ChipGroup = 0x7f1001e8;
        public static final int Widget_MaterialComponents_Chip_Action = 0x7f1001e4;
        public static final int Widget_MaterialComponents_Chip_Choice = 0x7f1001e5;
        public static final int Widget_MaterialComponents_Chip_Entry = 0x7f1001e6;
        public static final int Widget_MaterialComponents_Chip_Filter = 0x7f1001e7;
        public static final int Widget_MaterialComponents_FloatingActionButton = 0x7f1001e9;
        public static final int Widget_MaterialComponents_NavigationView = 0x7f1001ea;
        public static final int Widget_MaterialComponents_Snackbar = 0x7f1001eb;
        public static final int Widget_MaterialComponents_Snackbar_FullWidth = 0x7f1001ec;
        public static final int Widget_MaterialComponents_TabLayout = 0x7f1001ed;
        public static final int Widget_MaterialComponents_TabLayout_Colored = 0x7f1001ee;
        public static final int Widget_MaterialComponents_TextInputEditText_FilledBox = 0x7f1001ef;
        public static final int Widget_MaterialComponents_TextInputEditText_FilledBox_Dense = 0x7f1001f0;
        public static final int Widget_MaterialComponents_TextInputEditText_OutlinedBox = 0x7f1001f1;
        public static final int Widget_MaterialComponents_TextInputEditText_OutlinedBox_Dense = 0x7f1001f2;
        public static final int Widget_MaterialComponents_TextInputLayout_FilledBox = 0x7f1001f3;
        public static final int Widget_MaterialComponents_TextInputLayout_FilledBox_Dense = 0x7f1001f4;
        public static final int Widget_MaterialComponents_TextInputLayout_OutlinedBox = 0x7f1001f5;
        public static final int Widget_MaterialComponents_TextInputLayout_OutlinedBox_Dense = 0x7f1001f6;
        public static final int Widget_MaterialComponents_Toolbar = 0x7f1001f7;
        public static final int Widget_Support_CoordinatorLayout = 0x7f1001f8;
        public static final int courseDuration = 0x7f1001f9;
        public static final int courseEmployee = 0x7f1001fa;
        public static final int courseMemo = 0x7f1001fb;
        public static final int courseMoney = 0x7f1001fc;
        public static final int courseRemainTime = 0x7f1001fd;
        public static final int courseTitle = 0x7f1001fe;
        public static final int login_edit = 0x7f1001ff;
        public static final int popupTheme = 0x7f100200;
        public static final int settingEdit = 0x7f100201;
        public static final int settingLabel = 0x7f100202;
        public static final int settingSpinner = 0x7f100203;
        public static final int social_button = 0x7f100204;
        public static final int toolbarWhiteText = 0x7f100205;
    }

    public static final class styleable {
        public static final int ActionBarLayout_android_layout_gravity = 0x00000000;
        public static final int ActionBar_background = 0x00000000;
        public static final int ActionBar_backgroundSplit = 0x00000001;
        public static final int ActionBar_backgroundStacked = 0x00000002;
        public static final int ActionBar_contentInsetEnd = 0x00000003;
        public static final int ActionBar_contentInsetEndWithActions = 0x00000004;
        public static final int ActionBar_contentInsetLeft = 0x00000005;
        public static final int ActionBar_contentInsetRight = 0x00000006;
        public static final int ActionBar_contentInsetStart = 0x00000007;
        public static final int ActionBar_contentInsetStartWithNavigation = 0x00000008;
        public static final int ActionBar_customNavigationLayout = 0x00000009;
        public static final int ActionBar_displayOptions = 0x0000000a;
        public static final int ActionBar_divider = 0x0000000b;
        public static final int ActionBar_elevation = 0x0000000c;
        public static final int ActionBar_height = 0x0000000d;
        public static final int ActionBar_hideOnContentScroll = 0x0000000e;
        public static final int ActionBar_homeAsUpIndicator = 0x0000000f;
        public static final int ActionBar_homeLayout = 0x00000010;
        public static final int ActionBar_icon = 0x00000011;
        public static final int ActionBar_indeterminateProgressStyle = 0x00000012;
        public static final int ActionBar_itemPadding = 0x00000013;
        public static final int ActionBar_logo = 0x00000014;
        public static final int ActionBar_navigationMode = 0x00000015;
        public static final int ActionBar_popupTheme = 0x00000016;
        public static final int ActionBar_progressBarPadding = 0x00000017;
        public static final int ActionBar_progressBarStyle = 0x00000018;
        public static final int ActionBar_subtitle = 0x00000019;
        public static final int ActionBar_subtitleTextStyle = 0x0000001a;
        public static final int ActionBar_title = 0x0000001b;
        public static final int ActionBar_titleTextStyle = 0x0000001c;
        public static final int ActionMenuItemView_android_minWidth = 0x00000000;
        public static final int ActionMode_background = 0x00000000;
        public static final int ActionMode_backgroundSplit = 0x00000001;
        public static final int ActionMode_closeItemLayout = 0x00000002;
        public static final int ActionMode_height = 0x00000003;
        public static final int ActionMode_subtitleTextStyle = 0x00000004;
        public static final int ActionMode_titleTextStyle = 0x00000005;
        public static final int ActivityChooserView_expandActivityOverflowButtonDrawable = 0x00000000;
        public static final int ActivityChooserView_initialActivityCount = 0x00000001;
        public static final int AlertDialog_android_layout = 0x00000000;
        public static final int AlertDialog_buttonIconDimen = 0x00000001;
        public static final int AlertDialog_buttonPanelSideLayout = 0x00000002;
        public static final int AlertDialog_listItemLayout = 0x00000003;
        public static final int AlertDialog_listLayout = 0x00000004;
        public static final int AlertDialog_multiChoiceItemLayout = 0x00000005;
        public static final int AlertDialog_showTitle = 0x00000006;
        public static final int AlertDialog_singleChoiceItemLayout = 0x00000007;
        public static final int AnimatedStateListDrawableCompat_android_constantSize = 0x00000003;
        public static final int AnimatedStateListDrawableCompat_android_dither = 0x00000000;
        public static final int AnimatedStateListDrawableCompat_android_enterFadeDuration = 0x00000004;
        public static final int AnimatedStateListDrawableCompat_android_exitFadeDuration = 0x00000005;
        public static final int AnimatedStateListDrawableCompat_android_variablePadding = 0x00000002;
        public static final int AnimatedStateListDrawableCompat_android_visible = 0x00000001;
        public static final int AnimatedStateListDrawableItem_android_drawable = 0x00000001;
        public static final int AnimatedStateListDrawableItem_android_id = 0x00000000;
        public static final int AnimatedStateListDrawableTransition_android_drawable = 0x00000000;
        public static final int AnimatedStateListDrawableTransition_android_fromId = 0x00000002;
        public static final int AnimatedStateListDrawableTransition_android_reversible = 0x00000003;
        public static final int AnimatedStateListDrawableTransition_android_toId = 0x00000001;
        public static final int AppBarLayoutStates_state_collapsed = 0x00000000;
        public static final int AppBarLayoutStates_state_collapsible = 0x00000001;
        public static final int AppBarLayoutStates_state_liftable = 0x00000002;
        public static final int AppBarLayoutStates_state_lifted = 0x00000003;
        public static final int AppBarLayout_Layout_layout_scrollFlags = 0x00000000;
        public static final int AppBarLayout_Layout_layout_scrollInterpolator = 0x00000001;
        public static final int AppBarLayout_android_background = 0x00000000;
        public static final int AppBarLayout_android_keyboardNavigationCluster = 0x00000002;
        public static final int AppBarLayout_android_touchscreenBlocksFocus = 0x00000001;
        public static final int AppBarLayout_elevation = 0x00000003;
        public static final int AppBarLayout_expanded = 0x00000004;
        public static final int AppBarLayout_liftOnScroll = 0x00000005;
        public static final int AppCompatImageView_android_src = 0x00000000;
        public static final int AppCompatImageView_srcCompat = 0x00000001;
        public static final int AppCompatImageView_tint = 0x00000002;
        public static final int AppCompatImageView_tintMode = 0x00000003;
        public static final int AppCompatSeekBar_android_thumb = 0x00000000;
        public static final int AppCompatSeekBar_tickMark = 0x00000001;
        public static final int AppCompatSeekBar_tickMarkTint = 0x00000002;
        public static final int AppCompatSeekBar_tickMarkTintMode = 0x00000003;
        public static final int AppCompatTextHelper_android_drawableBottom = 0x00000002;
        public static final int AppCompatTextHelper_android_drawableEnd = 0x00000006;
        public static final int AppCompatTextHelper_android_drawableLeft = 0x00000003;
        public static final int AppCompatTextHelper_android_drawableRight = 0x00000004;
        public static final int AppCompatTextHelper_android_drawableStart = 0x00000005;
        public static final int AppCompatTextHelper_android_drawableTop = 0x00000001;
        public static final int AppCompatTextHelper_android_textAppearance = 0x00000000;
        public static final int AppCompatTextView_android_textAppearance = 0x00000000;
        public static final int AppCompatTextView_autoSizeMaxTextSize = 0x00000001;
        public static final int AppCompatTextView_autoSizeMinTextSize = 0x00000002;
        public static final int AppCompatTextView_autoSizePresetSizes = 0x00000003;
        public static final int AppCompatTextView_autoSizeStepGranularity = 0x00000004;
        public static final int AppCompatTextView_autoSizeTextType = 0x00000005;
        public static final int AppCompatTextView_firstBaselineToTopHeight = 0x00000006;
        public static final int AppCompatTextView_fontFamily = 0x00000007;
        public static final int AppCompatTextView_lastBaselineToBottomHeight = 0x00000008;
        public static final int AppCompatTextView_lineHeight = 0x00000009;
        public static final int AppCompatTextView_textAllCaps = 0x0000000a;
        public static final int AppCompatTheme_actionBarDivider = 0x00000002;
        public static final int AppCompatTheme_actionBarItemBackground = 0x00000003;
        public static final int AppCompatTheme_actionBarPopupTheme = 0x00000004;
        public static final int AppCompatTheme_actionBarSize = 0x00000005;
        public static final int AppCompatTheme_actionBarSplitStyle = 0x00000006;
        public static final int AppCompatTheme_actionBarStyle = 0x00000007;
        public static final int AppCompatTheme_actionBarTabBarStyle = 0x00000008;
        public static final int AppCompatTheme_actionBarTabStyle = 0x00000009;
        public static final int AppCompatTheme_actionBarTabTextStyle = 0x0000000a;
        public static final int AppCompatTheme_actionBarTheme = 0x0000000b;
        public static final int AppCompatTheme_actionBarWidgetTheme = 0x0000000c;
        public static final int AppCompatTheme_actionButtonStyle = 0x0000000d;
        public static final int AppCompatTheme_actionDropDownStyle = 0x0000000e;
        public static final int AppCompatTheme_actionMenuTextAppearance = 0x0000000f;
        public static final int AppCompatTheme_actionMenuTextColor = 0x00000010;
        public static final int AppCompatTheme_actionModeBackground = 0x00000011;
        public static final int AppCompatTheme_actionModeCloseButtonStyle = 0x00000012;
        public static final int AppCompatTheme_actionModeCloseDrawable = 0x00000013;
        public static final int AppCompatTheme_actionModeCopyDrawable = 0x00000014;
        public static final int AppCompatTheme_actionModeCutDrawable = 0x00000015;
        public static final int AppCompatTheme_actionModeFindDrawable = 0x00000016;
        public static final int AppCompatTheme_actionModePasteDrawable = 0x00000017;
        public static final int AppCompatTheme_actionModePopupWindowStyle = 0x00000018;
        public static final int AppCompatTheme_actionModeSelectAllDrawable = 0x00000019;
        public static final int AppCompatTheme_actionModeShareDrawable = 0x0000001a;
        public static final int AppCompatTheme_actionModeSplitBackground = 0x0000001b;
        public static final int AppCompatTheme_actionModeStyle = 0x0000001c;
        public static final int AppCompatTheme_actionModeWebSearchDrawable = 0x0000001d;
        public static final int AppCompatTheme_actionOverflowButtonStyle = 0x0000001e;
        public static final int AppCompatTheme_actionOverflowMenuStyle = 0x0000001f;
        public static final int AppCompatTheme_activityChooserViewStyle = 0x00000020;
        public static final int AppCompatTheme_alertDialogButtonGroupStyle = 0x00000021;
        public static final int AppCompatTheme_alertDialogCenterButtons = 0x00000022;
        public static final int AppCompatTheme_alertDialogStyle = 0x00000023;
        public static final int AppCompatTheme_alertDialogTheme = 0x00000024;
        public static final int AppCompatTheme_android_windowAnimationStyle = 0x00000001;
        public static final int AppCompatTheme_android_windowIsFloating = 0x00000000;
        public static final int AppCompatTheme_autoCompleteTextViewStyle = 0x00000025;
        public static final int AppCompatTheme_borderlessButtonStyle = 0x00000026;
        public static final int AppCompatTheme_buttonBarButtonStyle = 0x00000027;
        public static final int AppCompatTheme_buttonBarNegativeButtonStyle = 0x00000028;
        public static final int AppCompatTheme_buttonBarNeutralButtonStyle = 0x00000029;
        public static final int AppCompatTheme_buttonBarPositiveButtonStyle = 0x0000002a;
        public static final int AppCompatTheme_buttonBarStyle = 0x0000002b;
        public static final int AppCompatTheme_buttonStyle = 0x0000002c;
        public static final int AppCompatTheme_buttonStyleSmall = 0x0000002d;
        public static final int AppCompatTheme_checkboxStyle = 0x0000002e;
        public static final int AppCompatTheme_checkedTextViewStyle = 0x0000002f;
        public static final int AppCompatTheme_colorAccent = 0x00000030;
        public static final int AppCompatTheme_colorBackgroundFloating = 0x00000031;
        public static final int AppCompatTheme_colorButtonNormal = 0x00000032;
        public static final int AppCompatTheme_colorControlActivated = 0x00000033;
        public static final int AppCompatTheme_colorControlHighlight = 0x00000034;
        public static final int AppCompatTheme_colorControlNormal = 0x00000035;
        public static final int AppCompatTheme_colorError = 0x00000036;
        public static final int AppCompatTheme_colorPrimary = 0x00000037;
        public static final int AppCompatTheme_colorPrimaryDark = 0x00000038;
        public static final int AppCompatTheme_colorSwitchThumbNormal = 0x00000039;
        public static final int AppCompatTheme_controlBackground = 0x0000003a;
        public static final int AppCompatTheme_dialogCornerRadius = 0x0000003b;
        public static final int AppCompatTheme_dialogPreferredPadding = 0x0000003c;
        public static final int AppCompatTheme_dialogTheme = 0x0000003d;
        public static final int AppCompatTheme_dividerHorizontal = 0x0000003e;
        public static final int AppCompatTheme_dividerVertical = 0x0000003f;
        public static final int AppCompatTheme_dropDownListViewStyle = 0x00000040;
        public static final int AppCompatTheme_dropdownListPreferredItemHeight = 0x00000041;
        public static final int AppCompatTheme_editTextBackground = 0x00000042;
        public static final int AppCompatTheme_editTextColor = 0x00000043;
        public static final int AppCompatTheme_editTextStyle = 0x00000044;
        public static final int AppCompatTheme_homeAsUpIndicator = 0x00000045;
        public static final int AppCompatTheme_imageButtonStyle = 0x00000046;
        public static final int AppCompatTheme_listChoiceBackgroundIndicator = 0x00000047;
        public static final int AppCompatTheme_listDividerAlertDialog = 0x00000048;
        public static final int AppCompatTheme_listMenuViewStyle = 0x00000049;
        public static final int AppCompatTheme_listPopupWindowStyle = 0x0000004a;
        public static final int AppCompatTheme_listPreferredItemHeight = 0x0000004b;
        public static final int AppCompatTheme_listPreferredItemHeightLarge = 0x0000004c;
        public static final int AppCompatTheme_listPreferredItemHeightSmall = 0x0000004d;
        public static final int AppCompatTheme_listPreferredItemPaddingLeft = 0x0000004e;
        public static final int AppCompatTheme_listPreferredItemPaddingRight = 0x0000004f;
        public static final int AppCompatTheme_panelBackground = 0x00000050;
        public static final int AppCompatTheme_panelMenuListTheme = 0x00000051;
        public static final int AppCompatTheme_panelMenuListWidth = 0x00000052;
        public static final int AppCompatTheme_popupMenuStyle = 0x00000053;
        public static final int AppCompatTheme_popupWindowStyle = 0x00000054;
        public static final int AppCompatTheme_radioButtonStyle = 0x00000055;
        public static final int AppCompatTheme_ratingBarStyle = 0x00000056;
        public static final int AppCompatTheme_ratingBarStyleIndicator = 0x00000057;
        public static final int AppCompatTheme_ratingBarStyleSmall = 0x00000058;
        public static final int AppCompatTheme_searchViewStyle = 0x00000059;
        public static final int AppCompatTheme_seekBarStyle = 0x0000005a;
        public static final int AppCompatTheme_selectableItemBackground = 0x0000005b;
        public static final int AppCompatTheme_selectableItemBackgroundBorderless = 0x0000005c;
        public static final int AppCompatTheme_spinnerDropDownItemStyle = 0x0000005d;
        public static final int AppCompatTheme_spinnerStyle = 0x0000005e;
        public static final int AppCompatTheme_switchStyle = 0x0000005f;
        public static final int AppCompatTheme_textAppearanceLargePopupMenu = 0x00000060;
        public static final int AppCompatTheme_textAppearanceListItem = 0x00000061;
        public static final int AppCompatTheme_textAppearanceListItemSecondary = 0x00000062;
        public static final int AppCompatTheme_textAppearanceListItemSmall = 0x00000063;
        public static final int AppCompatTheme_textAppearancePopupMenuHeader = 0x00000064;
        public static final int AppCompatTheme_textAppearanceSearchResultSubtitle = 0x00000065;
        public static final int AppCompatTheme_textAppearanceSearchResultTitle = 0x00000066;
        public static final int AppCompatTheme_textAppearanceSmallPopupMenu = 0x00000067;
        public static final int AppCompatTheme_textColorAlertDialogListItem = 0x00000068;
        public static final int AppCompatTheme_textColorSearchUrl = 0x00000069;
        public static final int AppCompatTheme_toolbarNavigationButtonStyle = 0x0000006a;
        public static final int AppCompatTheme_toolbarStyle = 0x0000006b;
        public static final int AppCompatTheme_tooltipForegroundColor = 0x0000006c;
        public static final int AppCompatTheme_tooltipFrameBackground = 0x0000006d;
        public static final int AppCompatTheme_viewInflaterClass = 0x0000006e;
        public static final int AppCompatTheme_windowActionBar = 0x0000006f;
        public static final int AppCompatTheme_windowActionBarOverlay = 0x00000070;
        public static final int AppCompatTheme_windowActionModeOverlay = 0x00000071;
        public static final int AppCompatTheme_windowFixedHeightMajor = 0x00000072;
        public static final int AppCompatTheme_windowFixedHeightMinor = 0x00000073;
        public static final int AppCompatTheme_windowFixedWidthMajor = 0x00000074;
        public static final int AppCompatTheme_windowFixedWidthMinor = 0x00000075;
        public static final int AppCompatTheme_windowMinWidthMajor = 0x00000076;
        public static final int AppCompatTheme_windowMinWidthMinor = 0x00000077;
        public static final int AppCompatTheme_windowNoTitle = 0x00000078;
        public static final int BottomAppBar_backgroundTint = 0x00000000;
        public static final int BottomAppBar_fabAlignmentMode = 0x00000001;
        public static final int BottomAppBar_fabCradleMargin = 0x00000002;
        public static final int BottomAppBar_fabCradleRoundedCornerRadius = 0x00000003;
        public static final int BottomAppBar_fabCradleVerticalOffset = 0x00000004;
        public static final int BottomAppBar_hideOnScroll = 0x00000005;
        public static final int BottomNavigationView_elevation = 0x00000000;
        public static final int BottomNavigationView_itemBackground = 0x00000001;
        public static final int BottomNavigationView_itemHorizontalTranslationEnabled = 0x00000002;
        public static final int BottomNavigationView_itemIconSize = 0x00000003;
        public static final int BottomNavigationView_itemIconTint = 0x00000004;
        public static final int BottomNavigationView_itemTextAppearanceActive = 0x00000005;
        public static final int BottomNavigationView_itemTextAppearanceInactive = 0x00000006;
        public static final int BottomNavigationView_itemTextColor = 0x00000007;
        public static final int BottomNavigationView_labelVisibilityMode = 0x00000008;
        public static final int BottomNavigationView_menu = 0x00000009;
        public static final int BottomSheetBehavior_Layout_behavior_fitToContents = 0x00000000;
        public static final int BottomSheetBehavior_Layout_behavior_hideable = 0x00000001;
        public static final int BottomSheetBehavior_Layout_behavior_peekHeight = 0x00000002;
        public static final int BottomSheetBehavior_Layout_behavior_skipCollapsed = 0x00000003;
        public static final int ButtonBarLayout_allowStacking = 0x00000000;
        public static final int CardView_android_minHeight = 0x00000001;
        public static final int CardView_android_minWidth = 0x00000000;
        public static final int CardView_cardBackgroundColor = 0x00000002;
        public static final int CardView_cardCornerRadius = 0x00000003;
        public static final int CardView_cardElevation = 0x00000004;
        public static final int CardView_cardMaxElevation = 0x00000005;
        public static final int CardView_cardPreventCornerOverlap = 0x00000006;
        public static final int CardView_cardUseCompatPadding = 0x00000007;
        public static final int CardView_contentPadding = 0x00000008;
        public static final int CardView_contentPaddingBottom = 0x00000009;
        public static final int CardView_contentPaddingLeft = 0x0000000a;
        public static final int CardView_contentPaddingRight = 0x0000000b;
        public static final int CardView_contentPaddingTop = 0x0000000c;
        public static final int ChipGroup_checkedChip = 0x00000000;
        public static final int ChipGroup_chipSpacing = 0x00000001;
        public static final int ChipGroup_chipSpacingHorizontal = 0x00000002;
        public static final int ChipGroup_chipSpacingVertical = 0x00000003;
        public static final int ChipGroup_singleLine = 0x00000004;
        public static final int ChipGroup_singleSelection = 0x00000005;
        public static final int Chip_android_checkable = 0x00000004;
        public static final int Chip_android_ellipsize = 0x00000001;
        public static final int Chip_android_maxWidth = 0x00000002;
        public static final int Chip_android_text = 0x00000003;
        public static final int Chip_android_textAppearance = 0x00000000;
        public static final int Chip_checkedIcon = 0x00000005;
        public static final int Chip_checkedIconEnabled = 0x00000006;
        public static final int Chip_checkedIconVisible = 0x00000007;
        public static final int Chip_chipBackgroundColor = 0x00000008;
        public static final int Chip_chipCornerRadius = 0x00000009;
        public static final int Chip_chipEndPadding = 0x0000000a;
        public static final int Chip_chipIcon = 0x0000000b;
        public static final int Chip_chipIconEnabled = 0x0000000c;
        public static final int Chip_chipIconSize = 0x0000000d;
        public static final int Chip_chipIconTint = 0x0000000e;
        public static final int Chip_chipIconVisible = 0x0000000f;
        public static final int Chip_chipMinHeight = 0x00000010;
        public static final int Chip_chipStartPadding = 0x00000011;
        public static final int Chip_chipStrokeColor = 0x00000012;
        public static final int Chip_chipStrokeWidth = 0x00000013;
        public static final int Chip_closeIcon = 0x00000014;
        public static final int Chip_closeIconEnabled = 0x00000015;
        public static final int Chip_closeIconEndPadding = 0x00000016;
        public static final int Chip_closeIconSize = 0x00000017;
        public static final int Chip_closeIconStartPadding = 0x00000018;
        public static final int Chip_closeIconTint = 0x00000019;
        public static final int Chip_closeIconVisible = 0x0000001a;
        public static final int Chip_hideMotionSpec = 0x0000001b;
        public static final int Chip_iconEndPadding = 0x0000001c;
        public static final int Chip_iconStartPadding = 0x0000001d;
        public static final int Chip_rippleColor = 0x0000001e;
        public static final int Chip_showMotionSpec = 0x0000001f;
        public static final int Chip_textEndPadding = 0x00000020;
        public static final int Chip_textStartPadding = 0x00000021;
        public static final int CollapsingToolbarLayout_Layout_layout_collapseMode = 0x00000000;
        public static final int CollapsingToolbarLayout_Layout_layout_collapseParallaxMultiplier = 0x00000001;
        public static final int CollapsingToolbarLayout_collapsedTitleGravity = 0x00000000;
        public static final int CollapsingToolbarLayout_collapsedTitleTextAppearance = 0x00000001;
        public static final int CollapsingToolbarLayout_contentScrim = 0x00000002;
        public static final int CollapsingToolbarLayout_expandedTitleGravity = 0x00000003;
        public static final int CollapsingToolbarLayout_expandedTitleMargin = 0x00000004;
        public static final int CollapsingToolbarLayout_expandedTitleMarginBottom = 0x00000005;
        public static final int CollapsingToolbarLayout_expandedTitleMarginEnd = 0x00000006;
        public static final int CollapsingToolbarLayout_expandedTitleMarginStart = 0x00000007;
        public static final int CollapsingToolbarLayout_expandedTitleMarginTop = 0x00000008;
        public static final int CollapsingToolbarLayout_expandedTitleTextAppearance = 0x00000009;
        public static final int CollapsingToolbarLayout_scrimAnimationDuration = 0x0000000a;
        public static final int CollapsingToolbarLayout_scrimVisibleHeightTrigger = 0x0000000b;
        public static final int CollapsingToolbarLayout_statusBarScrim = 0x0000000c;
        public static final int CollapsingToolbarLayout_title = 0x0000000d;
        public static final int CollapsingToolbarLayout_titleEnabled = 0x0000000e;
        public static final int CollapsingToolbarLayout_toolbarId = 0x0000000f;
        public static final int ColorStateListItem_alpha = 0x00000002;
        public static final int ColorStateListItem_android_alpha = 0x00000001;
        public static final int ColorStateListItem_android_color = 0x00000000;
        public static final int CompoundButton_android_button = 0x00000000;
        public static final int CompoundButton_buttonTint = 0x00000001;
        public static final int CompoundButton_buttonTintMode = 0x00000002;
        public static final int CoordinatorLayout_Layout_android_layout_gravity = 0x00000000;
        public static final int CoordinatorLayout_Layout_layout_anchor = 0x00000001;
        public static final int CoordinatorLayout_Layout_layout_anchorGravity = 0x00000002;
        public static final int CoordinatorLayout_Layout_layout_behavior = 0x00000003;
        public static final int CoordinatorLayout_Layout_layout_dodgeInsetEdges = 0x00000004;
        public static final int CoordinatorLayout_Layout_layout_insetEdge = 0x00000005;
        public static final int CoordinatorLayout_Layout_layout_keyline = 0x00000006;
        public static final int CoordinatorLayout_keylines = 0x00000000;
        public static final int CoordinatorLayout_statusBarBackground = 0x00000001;
        public static final int DesignTheme_bottomSheetDialogTheme = 0x00000000;
        public static final int DesignTheme_bottomSheetStyle = 0x00000001;
        public static final int DrawerArrowToggle_arrowHeadLength = 0x00000000;
        public static final int DrawerArrowToggle_arrowShaftLength = 0x00000001;
        public static final int DrawerArrowToggle_barLength = 0x00000002;
        public static final int DrawerArrowToggle_color = 0x00000003;
        public static final int DrawerArrowToggle_drawableSize = 0x00000004;
        public static final int DrawerArrowToggle_gapBetweenBars = 0x00000005;
        public static final int DrawerArrowToggle_spinBars = 0x00000006;
        public static final int DrawerArrowToggle_thickness = 0x00000007;
        public static final int FloatingActionButton_Behavior_Layout_behavior_autoHide = 0x00000000;
        public static final int FloatingActionButton_backgroundTint = 0x00000000;
        public static final int FloatingActionButton_backgroundTintMode = 0x00000001;
        public static final int FloatingActionButton_borderWidth = 0x00000002;
        public static final int FloatingActionButton_elevation = 0x00000003;
        public static final int FloatingActionButton_fabCustomSize = 0x00000004;
        public static final int FloatingActionButton_fabSize = 0x00000005;
        public static final int FloatingActionButton_hideMotionSpec = 0x00000006;
        public static final int FloatingActionButton_hoveredFocusedTranslationZ = 0x00000007;
        public static final int FloatingActionButton_maxImageSize = 0x00000008;
        public static final int FloatingActionButton_pressedTranslationZ = 0x00000009;
        public static final int FloatingActionButton_rippleColor = 0x0000000a;
        public static final int FloatingActionButton_showMotionSpec = 0x0000000b;
        public static final int FloatingActionButton_useCompatPadding = 0x0000000c;
        public static final int FlowLayout_itemSpacing = 0x00000000;
        public static final int FlowLayout_lineSpacing = 0x00000001;
        public static final int FontFamilyFont_android_font = 0x00000000;
        public static final int FontFamilyFont_android_fontStyle = 0x00000002;
        public static final int FontFamilyFont_android_fontVariationSettings = 0x00000004;
        public static final int FontFamilyFont_android_fontWeight = 0x00000001;
        public static final int FontFamilyFont_android_ttcIndex = 0x00000003;
        public static final int FontFamilyFont_font = 0x00000005;
        public static final int FontFamilyFont_fontStyle = 0x00000006;
        public static final int FontFamilyFont_fontVariationSettings = 0x00000007;
        public static final int FontFamilyFont_fontWeight = 0x00000008;
        public static final int FontFamilyFont_ttcIndex = 0x00000009;
        public static final int FontFamily_fontProviderAuthority = 0x00000000;
        public static final int FontFamily_fontProviderCerts = 0x00000001;
        public static final int FontFamily_fontProviderFetchStrategy = 0x00000002;
        public static final int FontFamily_fontProviderFetchTimeout = 0x00000003;
        public static final int FontFamily_fontProviderPackage = 0x00000004;
        public static final int FontFamily_fontProviderQuery = 0x00000005;
        public static final int ForegroundLinearLayout_android_foreground = 0x00000000;
        public static final int ForegroundLinearLayout_android_foregroundGravity = 0x00000001;
        public static final int ForegroundLinearLayout_foregroundInsidePadding = 0x00000002;
        public static final int GradientColorItem_android_color = 0x00000000;
        public static final int GradientColorItem_android_offset = 0x00000001;
        public static final int GradientColor_android_centerColor = 0x00000007;
        public static final int GradientColor_android_centerX = 0x00000003;
        public static final int GradientColor_android_centerY = 0x00000004;
        public static final int GradientColor_android_endColor = 0x00000001;
        public static final int GradientColor_android_endX = 0x0000000a;
        public static final int GradientColor_android_endY = 0x0000000b;
        public static final int GradientColor_android_gradientRadius = 0x00000005;
        public static final int GradientColor_android_startColor = 0x00000000;
        public static final int GradientColor_android_startX = 0x00000008;
        public static final int GradientColor_android_startY = 0x00000009;
        public static final int GradientColor_android_tileMode = 0x00000006;
        public static final int GradientColor_android_type = 0x00000002;
        public static final int LinearLayoutCompat_Layout_android_layout_gravity = 0x00000000;
        public static final int LinearLayoutCompat_Layout_android_layout_height = 0x00000002;
        public static final int LinearLayoutCompat_Layout_android_layout_weight = 0x00000003;
        public static final int LinearLayoutCompat_Layout_android_layout_width = 0x00000001;
        public static final int LinearLayoutCompat_android_baselineAligned = 0x00000002;
        public static final int LinearLayoutCompat_android_baselineAlignedChildIndex = 0x00000003;
        public static final int LinearLayoutCompat_android_gravity = 0x00000000;
        public static final int LinearLayoutCompat_android_orientation = 0x00000001;
        public static final int LinearLayoutCompat_android_weightSum = 0x00000004;
        public static final int LinearLayoutCompat_divider = 0x00000005;
        public static final int LinearLayoutCompat_dividerPadding = 0x00000006;
        public static final int LinearLayoutCompat_measureWithLargestChild = 0x00000007;
        public static final int LinearLayoutCompat_showDividers = 0x00000008;
        public static final int ListPopupWindow_android_dropDownHorizontalOffset = 0x00000000;
        public static final int ListPopupWindow_android_dropDownVerticalOffset = 0x00000001;
        public static final int MaterialButton_android_insetBottom = 0x00000003;
        public static final int MaterialButton_android_insetLeft = 0x00000000;
        public static final int MaterialButton_android_insetRight = 0x00000001;
        public static final int MaterialButton_android_insetTop = 0x00000002;
        public static final int MaterialButton_backgroundTint = 0x00000004;
        public static final int MaterialButton_backgroundTintMode = 0x00000005;
        public static final int MaterialButton_cornerRadius = 0x00000006;
        public static final int MaterialButton_icon = 0x00000007;
        public static final int MaterialButton_iconGravity = 0x00000008;
        public static final int MaterialButton_iconPadding = 0x00000009;
        public static final int MaterialButton_iconSize = 0x0000000a;
        public static final int MaterialButton_iconTint = 0x0000000b;
        public static final int MaterialButton_iconTintMode = 0x0000000c;
        public static final int MaterialButton_rippleColor = 0x0000000d;
        public static final int MaterialButton_strokeColor = 0x0000000e;
        public static final int MaterialButton_strokeWidth = 0x0000000f;
        public static final int MaterialCardView_strokeColor = 0x00000000;
        public static final int MaterialCardView_strokeWidth = 0x00000001;
        public static final int MaterialComponentsTheme_bottomSheetDialogTheme = 0x00000000;
        public static final int MaterialComponentsTheme_bottomSheetStyle = 0x00000001;
        public static final int MaterialComponentsTheme_chipGroupStyle = 0x00000002;
        public static final int MaterialComponentsTheme_chipStandaloneStyle = 0x00000003;
        public static final int MaterialComponentsTheme_chipStyle = 0x00000004;
        public static final int MaterialComponentsTheme_colorAccent = 0x00000005;
        public static final int MaterialComponentsTheme_colorBackgroundFloating = 0x00000006;
        public static final int MaterialComponentsTheme_colorPrimary = 0x00000007;
        public static final int MaterialComponentsTheme_colorPrimaryDark = 0x00000008;
        public static final int MaterialComponentsTheme_colorSecondary = 0x00000009;
        public static final int MaterialComponentsTheme_editTextStyle = 0x0000000a;
        public static final int MaterialComponentsTheme_floatingActionButtonStyle = 0x0000000b;
        public static final int MaterialComponentsTheme_materialButtonStyle = 0x0000000c;
        public static final int MaterialComponentsTheme_materialCardViewStyle = 0x0000000d;
        public static final int MaterialComponentsTheme_navigationViewStyle = 0x0000000e;
        public static final int MaterialComponentsTheme_scrimBackground = 0x0000000f;
        public static final int MaterialComponentsTheme_snackbarButtonStyle = 0x00000010;
        public static final int MaterialComponentsTheme_tabStyle = 0x00000011;
        public static final int MaterialComponentsTheme_textAppearanceBody1 = 0x00000012;
        public static final int MaterialComponentsTheme_textAppearanceBody2 = 0x00000013;
        public static final int MaterialComponentsTheme_textAppearanceButton = 0x00000014;
        public static final int MaterialComponentsTheme_textAppearanceCaption = 0x00000015;
        public static final int MaterialComponentsTheme_textAppearanceHeadline1 = 0x00000016;
        public static final int MaterialComponentsTheme_textAppearanceHeadline2 = 0x00000017;
        public static final int MaterialComponentsTheme_textAppearanceHeadline3 = 0x00000018;
        public static final int MaterialComponentsTheme_textAppearanceHeadline4 = 0x00000019;
        public static final int MaterialComponentsTheme_textAppearanceHeadline5 = 0x0000001a;
        public static final int MaterialComponentsTheme_textAppearanceHeadline6 = 0x0000001b;
        public static final int MaterialComponentsTheme_textAppearanceOverline = 0x0000001c;
        public static final int MaterialComponentsTheme_textAppearanceSubtitle1 = 0x0000001d;
        public static final int MaterialComponentsTheme_textAppearanceSubtitle2 = 0x0000001e;
        public static final int MaterialComponentsTheme_textInputStyle = 0x0000001f;
        public static final int MenuGroup_android_checkableBehavior = 0x00000005;
        public static final int MenuGroup_android_enabled = 0x00000000;
        public static final int MenuGroup_android_id = 0x00000001;
        public static final int MenuGroup_android_menuCategory = 0x00000003;
        public static final int MenuGroup_android_orderInCategory = 0x00000004;
        public static final int MenuGroup_android_visible = 0x00000002;
        public static final int MenuItem_actionLayout = 0x0000000d;
        public static final int MenuItem_actionProviderClass = 0x0000000e;
        public static final int MenuItem_actionViewClass = 0x0000000f;
        public static final int MenuItem_alphabeticModifiers = 0x00000010;
        public static final int MenuItem_android_alphabeticShortcut = 0x00000009;
        public static final int MenuItem_android_checkable = 0x0000000b;
        public static final int MenuItem_android_checked = 0x00000003;
        public static final int MenuItem_android_enabled = 0x00000001;
        public static final int MenuItem_android_icon = 0x00000000;
        public static final int MenuItem_android_id = 0x00000002;
        public static final int MenuItem_android_menuCategory = 0x00000005;
        public static final int MenuItem_android_numericShortcut = 0x0000000a;
        public static final int MenuItem_android_onClick = 0x0000000c;
        public static final int MenuItem_android_orderInCategory = 0x00000006;
        public static final int MenuItem_android_title = 0x00000007;
        public static final int MenuItem_android_titleCondensed = 0x00000008;
        public static final int MenuItem_android_visible = 0x00000004;
        public static final int MenuItem_contentDescription = 0x00000011;
        public static final int MenuItem_iconTint = 0x00000012;
        public static final int MenuItem_iconTintMode = 0x00000013;
        public static final int MenuItem_numericModifiers = 0x00000014;
        public static final int MenuItem_showAsAction = 0x00000015;
        public static final int MenuItem_tooltipText = 0x00000016;
        public static final int MenuView_android_headerBackground = 0x00000004;
        public static final int MenuView_android_horizontalDivider = 0x00000002;
        public static final int MenuView_android_itemBackground = 0x00000005;
        public static final int MenuView_android_itemIconDisabledAlpha = 0x00000006;
        public static final int MenuView_android_itemTextAppearance = 0x00000001;
        public static final int MenuView_android_verticalDivider = 0x00000003;
        public static final int MenuView_android_windowAnimationStyle = 0x00000000;
        public static final int MenuView_preserveIconSpacing = 0x00000007;
        public static final int MenuView_subMenuArrow = 0x00000008;
        public static final int NavigationView_android_background = 0x00000000;
        public static final int NavigationView_android_fitsSystemWindows = 0x00000001;
        public static final int NavigationView_android_maxWidth = 0x00000002;
        public static final int NavigationView_elevation = 0x00000003;
        public static final int NavigationView_headerLayout = 0x00000004;
        public static final int NavigationView_itemBackground = 0x00000005;
        public static final int NavigationView_itemHorizontalPadding = 0x00000006;
        public static final int NavigationView_itemIconPadding = 0x00000007;
        public static final int NavigationView_itemIconTint = 0x00000008;
        public static final int NavigationView_itemTextAppearance = 0x00000009;
        public static final int NavigationView_itemTextColor = 0x0000000a;
        public static final int NavigationView_menu = 0x0000000b;
        public static final int PopupWindowBackgroundState_state_above_anchor = 0x00000000;
        public static final int PopupWindow_android_popupAnimationStyle = 0x00000001;
        public static final int PopupWindow_android_popupBackground = 0x00000000;
        public static final int PopupWindow_overlapAnchor = 0x00000002;
        public static final int RecycleListView_paddingBottomNoButtons = 0x00000000;
        public static final int RecycleListView_paddingTopNoTitle = 0x00000001;
        public static final int RecyclerView_android_descendantFocusability = 0x00000001;
        public static final int RecyclerView_android_orientation = 0x00000000;
        public static final int RecyclerView_fastScrollEnabled = 0x00000002;
        public static final int RecyclerView_fastScrollHorizontalThumbDrawable = 0x00000003;
        public static final int RecyclerView_fastScrollHorizontalTrackDrawable = 0x00000004;
        public static final int RecyclerView_fastScrollVerticalThumbDrawable = 0x00000005;
        public static final int RecyclerView_fastScrollVerticalTrackDrawable = 0x00000006;
        public static final int RecyclerView_layoutManager = 0x00000007;
        public static final int RecyclerView_reverseLayout = 0x00000008;
        public static final int RecyclerView_spanCount = 0x00000009;
        public static final int RecyclerView_stackFromEnd = 0x0000000a;
        public static final int ScrimInsetsFrameLayout_insetForeground = 0x00000000;
        public static final int ScrollingViewBehavior_Layout_behavior_overlapTop = 0x00000000;
        public static final int SearchView_android_focusable = 0x00000000;
        public static final int SearchView_android_imeOptions = 0x00000003;
        public static final int SearchView_android_inputType = 0x00000002;
        public static final int SearchView_android_maxWidth = 0x00000001;
        public static final int SearchView_closeIcon = 0x00000004;
        public static final int SearchView_commitIcon = 0x00000005;
        public static final int SearchView_defaultQueryHint = 0x00000006;
        public static final int SearchView_goIcon = 0x00000007;
        public static final int SearchView_iconifiedByDefault = 0x00000008;
        public static final int SearchView_layout = 0x00000009;
        public static final int SearchView_queryBackground = 0x0000000a;
        public static final int SearchView_queryHint = 0x0000000b;
        public static final int SearchView_searchHintIcon = 0x0000000c;
        public static final int SearchView_searchIcon = 0x0000000d;
        public static final int SearchView_submitBackground = 0x0000000e;
        public static final int SearchView_suggestionRowLayout = 0x0000000f;
        public static final int SearchView_voiceIcon = 0x00000010;
        public static final int SnackbarLayout_android_maxWidth = 0x00000000;
        public static final int SnackbarLayout_elevation = 0x00000001;
        public static final int SnackbarLayout_maxActionInlineWidth = 0x00000002;
        public static final int Snackbar_snackbarButtonStyle = 0x00000000;
        public static final int Snackbar_snackbarStyle = 0x00000001;
        public static final int Spinner_android_dropDownWidth = 0x00000003;
        public static final int Spinner_android_entries = 0x00000000;
        public static final int Spinner_android_popupBackground = 0x00000001;
        public static final int Spinner_android_prompt = 0x00000002;
        public static final int Spinner_popupTheme = 0x00000004;
        public static final int StateListDrawableItem_android_drawable = 0x00000000;
        public static final int StateListDrawable_android_constantSize = 0x00000003;
        public static final int StateListDrawable_android_dither = 0x00000000;
        public static final int StateListDrawable_android_enterFadeDuration = 0x00000004;
        public static final int StateListDrawable_android_exitFadeDuration = 0x00000005;
        public static final int StateListDrawable_android_variablePadding = 0x00000002;
        public static final int StateListDrawable_android_visible = 0x00000001;
        public static final int SwitchCompat_android_textOff = 0x00000001;
        public static final int SwitchCompat_android_textOn = 0x00000000;
        public static final int SwitchCompat_android_thumb = 0x00000002;
        public static final int SwitchCompat_showText = 0x00000003;
        public static final int SwitchCompat_splitTrack = 0x00000004;
        public static final int SwitchCompat_switchMinWidth = 0x00000005;
        public static final int SwitchCompat_switchPadding = 0x00000006;
        public static final int SwitchCompat_switchTextAppearance = 0x00000007;
        public static final int SwitchCompat_thumbTextPadding = 0x00000008;
        public static final int SwitchCompat_thumbTint = 0x00000009;
        public static final int SwitchCompat_thumbTintMode = 0x0000000a;
        public static final int SwitchCompat_track = 0x0000000b;
        public static final int SwitchCompat_trackTint = 0x0000000c;
        public static final int SwitchCompat_trackTintMode = 0x0000000d;
        public static final int TabItem_android_icon = 0x00000000;
        public static final int TabItem_android_layout = 0x00000001;
        public static final int TabItem_android_text = 0x00000002;
        public static final int TabLayout_tabBackground = 0x00000000;
        public static final int TabLayout_tabContentStart = 0x00000001;
        public static final int TabLayout_tabGravity = 0x00000002;
        public static final int TabLayout_tabIconTint = 0x00000003;
        public static final int TabLayout_tabIconTintMode = 0x00000004;
        public static final int TabLayout_tabIndicator = 0x00000005;
        public static final int TabLayout_tabIndicatorAnimationDuration = 0x00000006;
        public static final int TabLayout_tabIndicatorColor = 0x00000007;
        public static final int TabLayout_tabIndicatorFullWidth = 0x00000008;
        public static final int TabLayout_tabIndicatorGravity = 0x00000009;
        public static final int TabLayout_tabIndicatorHeight = 0x0000000a;
        public static final int TabLayout_tabInlineLabel = 0x0000000b;
        public static final int TabLayout_tabMaxWidth = 0x0000000c;
        public static final int TabLayout_tabMinWidth = 0x0000000d;
        public static final int TabLayout_tabMode = 0x0000000e;
        public static final int TabLayout_tabPadding = 0x0000000f;
        public static final int TabLayout_tabPaddingBottom = 0x00000010;
        public static final int TabLayout_tabPaddingEnd = 0x00000011;
        public static final int TabLayout_tabPaddingStart = 0x00000012;
        public static final int TabLayout_tabPaddingTop = 0x00000013;
        public static final int TabLayout_tabRippleColor = 0x00000014;
        public static final int TabLayout_tabSelectedTextColor = 0x00000015;
        public static final int TabLayout_tabTextAppearance = 0x00000016;
        public static final int TabLayout_tabTextColor = 0x00000017;
        public static final int TabLayout_tabUnboundedRipple = 0x00000018;
        public static final int TextAppearance_android_fontFamily = 0x0000000a;
        public static final int TextAppearance_android_shadowColor = 0x00000006;
        public static final int TextAppearance_android_shadowDx = 0x00000007;
        public static final int TextAppearance_android_shadowDy = 0x00000008;
        public static final int TextAppearance_android_shadowRadius = 0x00000009;
        public static final int TextAppearance_android_textColor = 0x00000003;
        public static final int TextAppearance_android_textColorHint = 0x00000004;
        public static final int TextAppearance_android_textColorLink = 0x00000005;
        public static final int TextAppearance_android_textSize = 0x00000000;
        public static final int TextAppearance_android_textStyle = 0x00000002;
        public static final int TextAppearance_android_typeface = 0x00000001;
        public static final int TextAppearance_fontFamily = 0x0000000b;
        public static final int TextAppearance_textAllCaps = 0x0000000c;
        public static final int TextInputLayout_android_hint = 0x00000001;
        public static final int TextInputLayout_android_textColorHint = 0x00000000;
        public static final int TextInputLayout_boxBackgroundColor = 0x00000002;
        public static final int TextInputLayout_boxBackgroundMode = 0x00000003;
        public static final int TextInputLayout_boxCollapsedPaddingTop = 0x00000004;
        public static final int TextInputLayout_boxCornerRadiusBottomEnd = 0x00000005;
        public static final int TextInputLayout_boxCornerRadiusBottomStart = 0x00000006;
        public static final int TextInputLayout_boxCornerRadiusTopEnd = 0x00000007;
        public static final int TextInputLayout_boxCornerRadiusTopStart = 0x00000008;
        public static final int TextInputLayout_boxStrokeColor = 0x00000009;
        public static final int TextInputLayout_boxStrokeWidth = 0x0000000a;
        public static final int TextInputLayout_counterEnabled = 0x0000000b;
        public static final int TextInputLayout_counterMaxLength = 0x0000000c;
        public static final int TextInputLayout_counterOverflowTextAppearance = 0x0000000d;
        public static final int TextInputLayout_counterTextAppearance = 0x0000000e;
        public static final int TextInputLayout_errorEnabled = 0x0000000f;
        public static final int TextInputLayout_errorTextAppearance = 0x00000010;
        public static final int TextInputLayout_helperText = 0x00000011;
        public static final int TextInputLayout_helperTextEnabled = 0x00000012;
        public static final int TextInputLayout_helperTextTextAppearance = 0x00000013;
        public static final int TextInputLayout_hintAnimationEnabled = 0x00000014;
        public static final int TextInputLayout_hintEnabled = 0x00000015;
        public static final int TextInputLayout_hintTextAppearance = 0x00000016;
        public static final int TextInputLayout_passwordToggleContentDescription = 0x00000017;
        public static final int TextInputLayout_passwordToggleDrawable = 0x00000018;
        public static final int TextInputLayout_passwordToggleEnabled = 0x00000019;
        public static final int TextInputLayout_passwordToggleTint = 0x0000001a;
        public static final int TextInputLayout_passwordToggleTintMode = 0x0000001b;
        public static final int ThemeEnforcement_android_textAppearance = 0x00000000;
        public static final int ThemeEnforcement_enforceMaterialTheme = 0x00000001;
        public static final int ThemeEnforcement_enforceTextAppearance = 0x00000002;
        public static final int Toolbar_android_gravity = 0x00000000;
        public static final int Toolbar_android_minHeight = 0x00000001;
        public static final int Toolbar_buttonGravity = 0x00000002;
        public static final int Toolbar_collapseContentDescription = 0x00000003;
        public static final int Toolbar_collapseIcon = 0x00000004;
        public static final int Toolbar_contentInsetEnd = 0x00000005;
        public static final int Toolbar_contentInsetEndWithActions = 0x00000006;
        public static final int Toolbar_contentInsetLeft = 0x00000007;
        public static final int Toolbar_contentInsetRight = 0x00000008;
        public static final int Toolbar_contentInsetStart = 0x00000009;
        public static final int Toolbar_contentInsetStartWithNavigation = 0x0000000a;
        public static final int Toolbar_logo = 0x0000000b;
        public static final int Toolbar_logoDescription = 0x0000000c;
        public static final int Toolbar_maxButtonHeight = 0x0000000d;
        public static final int Toolbar_navigationContentDescription = 0x0000000e;
        public static final int Toolbar_navigationIcon = 0x0000000f;
        public static final int Toolbar_popupTheme = 0x00000010;
        public static final int Toolbar_subtitle = 0x00000011;
        public static final int Toolbar_subtitleTextAppearance = 0x00000012;
        public static final int Toolbar_subtitleTextColor = 0x00000013;
        public static final int Toolbar_title = 0x00000014;
        public static final int Toolbar_titleMargin = 0x00000015;
        public static final int Toolbar_titleMarginBottom = 0x00000016;
        public static final int Toolbar_titleMarginEnd = 0x00000017;
        public static final int Toolbar_titleMarginStart = 0x00000018;
        public static final int Toolbar_titleMarginTop = 0x00000019;

        @Deprecated
        public static final int Toolbar_titleMargins = 0x0000001a;
        public static final int Toolbar_titleTextAppearance = 0x0000001b;
        public static final int Toolbar_titleTextColor = 0x0000001c;
        public static final int ViewBackgroundHelper_android_background = 0x00000000;
        public static final int ViewBackgroundHelper_backgroundTint = 0x00000001;
        public static final int ViewBackgroundHelper_backgroundTintMode = 0x00000002;
        public static final int ViewStubCompat_android_id = 0x00000000;
        public static final int ViewStubCompat_android_inflatedId = 0x00000002;
        public static final int ViewStubCompat_android_layout = 0x00000001;
        public static final int View_android_focusable = 0x00000001;
        public static final int View_android_theme = 0x00000000;
        public static final int View_paddingEnd = 0x00000002;
        public static final int View_paddingStart = 0x00000003;
        public static final int View_theme = 0x00000004;
        public static final int[] ActionBar = {R.attr.background, R.attr.backgroundSplit, R.attr.backgroundStacked, R.attr.contentInsetEnd, R.attr.contentInsetEndWithActions, R.attr.contentInsetLeft, R.attr.contentInsetRight, R.attr.contentInsetStart, R.attr.contentInsetStartWithNavigation, R.attr.customNavigationLayout, R.attr.displayOptions, R.attr.divider, R.attr.elevation, R.attr.height, R.attr.hideOnContentScroll, R.attr.homeAsUpIndicator, R.attr.homeLayout, R.attr.icon, R.attr.indeterminateProgressStyle, R.attr.itemPadding, R.attr.logo, R.attr.navigationMode, R.attr.popupTheme, R.attr.progressBarPadding, R.attr.progressBarStyle, R.attr.subtitle, R.attr.subtitleTextStyle, R.attr.title, R.attr.titleTextStyle};
        public static final int[] ActionBarLayout = {android.R.attr.layout_gravity};
        public static final int[] ActionMenuItemView = {android.R.attr.minWidth};
        public static final int[] ActionMenuView = new int[0];
        public static final int[] ActionMode = {R.attr.background, R.attr.backgroundSplit, R.attr.closeItemLayout, R.attr.height, R.attr.subtitleTextStyle, R.attr.titleTextStyle};
        public static final int[] ActivityChooserView = {R.attr.expandActivityOverflowButtonDrawable, R.attr.initialActivityCount};
        public static final int[] AlertDialog = {android.R.attr.layout, R.attr.buttonIconDimen, R.attr.buttonPanelSideLayout, R.attr.listItemLayout, R.attr.listLayout, R.attr.multiChoiceItemLayout, R.attr.showTitle, R.attr.singleChoiceItemLayout};
        public static final int[] AnimatedStateListDrawableCompat = {android.R.attr.dither, android.R.attr.visible, android.R.attr.variablePadding, android.R.attr.constantSize, android.R.attr.enterFadeDuration, android.R.attr.exitFadeDuration};
        public static final int[] AnimatedStateListDrawableItem = {android.R.attr.id, android.R.attr.drawable};
        public static final int[] AnimatedStateListDrawableTransition = {android.R.attr.drawable, android.R.attr.toId, android.R.attr.fromId, android.R.attr.reversible};
        public static final int[] AppBarLayout = {android.R.attr.background, android.R.attr.touchscreenBlocksFocus, android.R.attr.keyboardNavigationCluster, R.attr.elevation, R.attr.expanded, R.attr.liftOnScroll};
        public static final int[] AppBarLayoutStates = {R.attr.state_collapsed, R.attr.state_collapsible, R.attr.state_liftable, R.attr.state_lifted};
        public static final int[] AppBarLayout_Layout = {R.attr.layout_scrollFlags, R.attr.layout_scrollInterpolator};
        public static final int[] AppCompatImageView = {android.R.attr.src, R.attr.srcCompat, R.attr.tint, R.attr.tintMode};
        public static final int[] AppCompatSeekBar = {android.R.attr.thumb, R.attr.tickMark, R.attr.tickMarkTint, R.attr.tickMarkTintMode};
        public static final int[] AppCompatTextHelper = {android.R.attr.textAppearance, android.R.attr.drawableTop, android.R.attr.drawableBottom, android.R.attr.drawableLeft, android.R.attr.drawableRight, android.R.attr.drawableStart, android.R.attr.drawableEnd};
        public static final int[] AppCompatTextView = {android.R.attr.textAppearance, R.attr.autoSizeMaxTextSize, R.attr.autoSizeMinTextSize, R.attr.autoSizePresetSizes, R.attr.autoSizeStepGranularity, R.attr.autoSizeTextType, R.attr.firstBaselineToTopHeight, R.attr.fontFamily, R.attr.lastBaselineToBottomHeight, R.attr.lineHeight, R.attr.textAllCaps};
        public static final int[] AppCompatTheme = {android.R.attr.windowIsFloating, android.R.attr.windowAnimationStyle, R.attr.actionBarDivider, R.attr.actionBarItemBackground, R.attr.actionBarPopupTheme, R.attr.actionBarSize, R.attr.actionBarSplitStyle, R.attr.actionBarStyle, R.attr.actionBarTabBarStyle, R.attr.actionBarTabStyle, R.attr.actionBarTabTextStyle, R.attr.actionBarTheme, R.attr.actionBarWidgetTheme, R.attr.actionButtonStyle, R.attr.actionDropDownStyle, R.attr.actionMenuTextAppearance, R.attr.actionMenuTextColor, R.attr.actionModeBackground, R.attr.actionModeCloseButtonStyle, R.attr.actionModeCloseDrawable, R.attr.actionModeCopyDrawable, R.attr.actionModeCutDrawable, R.attr.actionModeFindDrawable, R.attr.actionModePasteDrawable, R.attr.actionModePopupWindowStyle, R.attr.actionModeSelectAllDrawable, R.attr.actionModeShareDrawable, R.attr.actionModeSplitBackground, R.attr.actionModeStyle, R.attr.actionModeWebSearchDrawable, R.attr.actionOverflowButtonStyle, R.attr.actionOverflowMenuStyle, R.attr.activityChooserViewStyle, R.attr.alertDialogButtonGroupStyle, R.attr.alertDialogCenterButtons, R.attr.alertDialogStyle, R.attr.alertDialogTheme, R.attr.autoCompleteTextViewStyle, R.attr.borderlessButtonStyle, R.attr.buttonBarButtonStyle, R.attr.buttonBarNegativeButtonStyle, R.attr.buttonBarNeutralButtonStyle, R.attr.buttonBarPositiveButtonStyle, R.attr.buttonBarStyle, R.attr.buttonStyle, R.attr.buttonStyleSmall, R.attr.checkboxStyle, R.attr.checkedTextViewStyle, R.attr.colorAccent, R.attr.colorBackgroundFloating, R.attr.colorButtonNormal, R.attr.colorControlActivated, R.attr.colorControlHighlight, R.attr.colorControlNormal, R.attr.colorError, R.attr.colorPrimary, R.attr.colorPrimaryDark, R.attr.colorSwitchThumbNormal, R.attr.controlBackground, R.attr.dialogCornerRadius, R.attr.dialogPreferredPadding, R.attr.dialogTheme, R.attr.dividerHorizontal, R.attr.dividerVertical, R.attr.dropDownListViewStyle, R.attr.dropdownListPreferredItemHeight, R.attr.editTextBackground, R.attr.editTextColor, R.attr.editTextStyle, R.attr.homeAsUpIndicator, R.attr.imageButtonStyle, R.attr.listChoiceBackgroundIndicator, R.attr.listDividerAlertDialog, R.attr.listMenuViewStyle, R.attr.listPopupWindowStyle, R.attr.listPreferredItemHeight, R.attr.listPreferredItemHeightLarge, R.attr.listPreferredItemHeightSmall, R.attr.listPreferredItemPaddingLeft, R.attr.listPreferredItemPaddingRight, R.attr.panelBackground, R.attr.panelMenuListTheme, R.attr.panelMenuListWidth, R.attr.popupMenuStyle, R.attr.popupWindowStyle, R.attr.radioButtonStyle, R.attr.ratingBarStyle, R.attr.ratingBarStyleIndicator, R.attr.ratingBarStyleSmall, R.attr.searchViewStyle, R.attr.seekBarStyle, R.attr.selectableItemBackground, R.attr.selectableItemBackgroundBorderless, R.attr.spinnerDropDownItemStyle, R.attr.spinnerStyle, R.attr.switchStyle, R.attr.textAppearanceLargePopupMenu, R.attr.textAppearanceListItem, R.attr.textAppearanceListItemSecondary, R.attr.textAppearanceListItemSmall, R.attr.textAppearancePopupMenuHeader, R.attr.textAppearanceSearchResultSubtitle, R.attr.textAppearanceSearchResultTitle, R.attr.textAppearanceSmallPopupMenu, R.attr.textColorAlertDialogListItem, R.attr.textColorSearchUrl, R.attr.toolbarNavigationButtonStyle, R.attr.toolbarStyle, R.attr.tooltipForegroundColor, R.attr.tooltipFrameBackground, R.attr.viewInflaterClass, R.attr.windowActionBar, R.attr.windowActionBarOverlay, R.attr.windowActionModeOverlay, R.attr.windowFixedHeightMajor, R.attr.windowFixedHeightMinor, R.attr.windowFixedWidthMajor, R.attr.windowFixedWidthMinor, R.attr.windowMinWidthMajor, R.attr.windowMinWidthMinor, R.attr.windowNoTitle};
        public static final int[] BottomAppBar = {R.attr.backgroundTint, R.attr.fabAlignmentMode, R.attr.fabCradleMargin, R.attr.fabCradleRoundedCornerRadius, R.attr.fabCradleVerticalOffset, R.attr.hideOnScroll};
        public static final int[] BottomNavigationView = {R.attr.elevation, R.attr.itemBackground, R.attr.itemHorizontalTranslationEnabled, R.attr.itemIconSize, R.attr.itemIconTint, R.attr.itemTextAppearanceActive, R.attr.itemTextAppearanceInactive, R.attr.itemTextColor, R.attr.labelVisibilityMode, R.attr.menu};
        public static final int[] BottomSheetBehavior_Layout = {R.attr.behavior_fitToContents, R.attr.behavior_hideable, R.attr.behavior_peekHeight, R.attr.behavior_skipCollapsed};
        public static final int[] ButtonBarLayout = {R.attr.allowStacking};
        public static final int[] CardView = {android.R.attr.minWidth, android.R.attr.minHeight, R.attr.cardBackgroundColor, R.attr.cardCornerRadius, R.attr.cardElevation, R.attr.cardMaxElevation, R.attr.cardPreventCornerOverlap, R.attr.cardUseCompatPadding, R.attr.contentPadding, R.attr.contentPaddingBottom, R.attr.contentPaddingLeft, R.attr.contentPaddingRight, R.attr.contentPaddingTop};
        public static final int[] Chip = {android.R.attr.textAppearance, android.R.attr.ellipsize, android.R.attr.maxWidth, android.R.attr.text, android.R.attr.checkable, R.attr.checkedIcon, R.attr.checkedIconEnabled, R.attr.checkedIconVisible, R.attr.chipBackgroundColor, R.attr.chipCornerRadius, R.attr.chipEndPadding, R.attr.chipIcon, R.attr.chipIconEnabled, R.attr.chipIconSize, R.attr.chipIconTint, R.attr.chipIconVisible, R.attr.chipMinHeight, R.attr.chipStartPadding, R.attr.chipStrokeColor, R.attr.chipStrokeWidth, R.attr.closeIcon, R.attr.closeIconEnabled, R.attr.closeIconEndPadding, R.attr.closeIconSize, R.attr.closeIconStartPadding, R.attr.closeIconTint, R.attr.closeIconVisible, R.attr.hideMotionSpec, R.attr.iconEndPadding, R.attr.iconStartPadding, R.attr.rippleColor, R.attr.showMotionSpec, R.attr.textEndPadding, R.attr.textStartPadding};
        public static final int[] ChipGroup = {R.attr.checkedChip, R.attr.chipSpacing, R.attr.chipSpacingHorizontal, R.attr.chipSpacingVertical, R.attr.singleLine, R.attr.singleSelection};
        public static final int[] CollapsingToolbarLayout = {R.attr.collapsedTitleGravity, R.attr.collapsedTitleTextAppearance, R.attr.contentScrim, R.attr.expandedTitleGravity, R.attr.expandedTitleMargin, R.attr.expandedTitleMarginBottom, R.attr.expandedTitleMarginEnd, R.attr.expandedTitleMarginStart, R.attr.expandedTitleMarginTop, R.attr.expandedTitleTextAppearance, R.attr.scrimAnimationDuration, R.attr.scrimVisibleHeightTrigger, R.attr.statusBarScrim, R.attr.title, R.attr.titleEnabled, R.attr.toolbarId};
        public static final int[] CollapsingToolbarLayout_Layout = {R.attr.layout_collapseMode, R.attr.layout_collapseParallaxMultiplier};
        public static final int[] ColorStateListItem = {android.R.attr.color, android.R.attr.alpha, R.attr.alpha};
        public static final int[] CompoundButton = {android.R.attr.button, R.attr.buttonTint, R.attr.buttonTintMode};
        public static final int[] CoordinatorLayout = {R.attr.keylines, R.attr.statusBarBackground};
        public static final int[] CoordinatorLayout_Layout = {android.R.attr.layout_gravity, R.attr.layout_anchor, R.attr.layout_anchorGravity, R.attr.layout_behavior, R.attr.layout_dodgeInsetEdges, R.attr.layout_insetEdge, R.attr.layout_keyline};
        public static final int[] DesignTheme = {R.attr.bottomSheetDialogTheme, R.attr.bottomSheetStyle};
        public static final int[] DrawerArrowToggle = {R.attr.arrowHeadLength, R.attr.arrowShaftLength, R.attr.barLength, R.attr.color, R.attr.drawableSize, R.attr.gapBetweenBars, R.attr.spinBars, R.attr.thickness};
        public static final int[] FloatingActionButton = {R.attr.backgroundTint, R.attr.backgroundTintMode, R.attr.borderWidth, R.attr.elevation, R.attr.fabCustomSize, R.attr.fabSize, R.attr.hideMotionSpec, R.attr.hoveredFocusedTranslationZ, R.attr.maxImageSize, R.attr.pressedTranslationZ, R.attr.rippleColor, R.attr.showMotionSpec, R.attr.useCompatPadding};
        public static final int[] FloatingActionButton_Behavior_Layout = {R.attr.behavior_autoHide};
        public static final int[] FlowLayout = {R.attr.itemSpacing, R.attr.lineSpacing};
        public static final int[] FontFamily = {R.attr.fontProviderAuthority, R.attr.fontProviderCerts, R.attr.fontProviderFetchStrategy, R.attr.fontProviderFetchTimeout, R.attr.fontProviderPackage, R.attr.fontProviderQuery};
        public static final int[] FontFamilyFont = {android.R.attr.font, android.R.attr.fontWeight, android.R.attr.fontStyle, android.R.attr.ttcIndex, android.R.attr.fontVariationSettings, R.attr.font, R.attr.fontStyle, R.attr.fontVariationSettings, R.attr.fontWeight, R.attr.ttcIndex};
        public static final int[] ForegroundLinearLayout = {android.R.attr.foreground, android.R.attr.foregroundGravity, R.attr.foregroundInsidePadding};
        public static final int[] GradientColor = {android.R.attr.startColor, android.R.attr.endColor, android.R.attr.type, android.R.attr.centerX, android.R.attr.centerY, android.R.attr.gradientRadius, android.R.attr.tileMode, android.R.attr.centerColor, android.R.attr.startX, android.R.attr.startY, android.R.attr.endX, android.R.attr.endY};
        public static final int[] GradientColorItem = {android.R.attr.color, android.R.attr.offset};
        public static final int[] LinearLayoutCompat = {android.R.attr.gravity, android.R.attr.orientation, android.R.attr.baselineAligned, android.R.attr.baselineAlignedChildIndex, android.R.attr.weightSum, R.attr.divider, R.attr.dividerPadding, R.attr.measureWithLargestChild, R.attr.showDividers};
        public static final int[] LinearLayoutCompat_Layout = {android.R.attr.layout_gravity, android.R.attr.layout_width, android.R.attr.layout_height, android.R.attr.layout_weight};
        public static final int[] ListPopupWindow = {android.R.attr.dropDownHorizontalOffset, android.R.attr.dropDownVerticalOffset};
        public static final int[] MaterialButton = {android.R.attr.insetLeft, android.R.attr.insetRight, android.R.attr.insetTop, android.R.attr.insetBottom, R.attr.backgroundTint, R.attr.backgroundTintMode, R.attr.cornerRadius, R.attr.icon, R.attr.iconGravity, R.attr.iconPadding, R.attr.iconSize, R.attr.iconTint, R.attr.iconTintMode, R.attr.rippleColor, R.attr.strokeColor, R.attr.strokeWidth};
        public static final int[] MaterialCardView = {R.attr.strokeColor, R.attr.strokeWidth};
        public static final int[] MaterialComponentsTheme = {R.attr.bottomSheetDialogTheme, R.attr.bottomSheetStyle, R.attr.chipGroupStyle, R.attr.chipStandaloneStyle, R.attr.chipStyle, R.attr.colorAccent, R.attr.colorBackgroundFloating, R.attr.colorPrimary, R.attr.colorPrimaryDark, R.attr.colorSecondary, R.attr.editTextStyle, R.attr.floatingActionButtonStyle, R.attr.materialButtonStyle, R.attr.materialCardViewStyle, R.attr.navigationViewStyle, R.attr.scrimBackground, R.attr.snackbarButtonStyle, R.attr.tabStyle, R.attr.textAppearanceBody1, R.attr.textAppearanceBody2, R.attr.textAppearanceButton, R.attr.textAppearanceCaption, R.attr.textAppearanceHeadline1, R.attr.textAppearanceHeadline2, R.attr.textAppearanceHeadline3, R.attr.textAppearanceHeadline4, R.attr.textAppearanceHeadline5, R.attr.textAppearanceHeadline6, R.attr.textAppearanceOverline, R.attr.textAppearanceSubtitle1, R.attr.textAppearanceSubtitle2, R.attr.textInputStyle};
        public static final int[] MenuGroup = {android.R.attr.enabled, android.R.attr.id, android.R.attr.visible, android.R.attr.menuCategory, android.R.attr.orderInCategory, android.R.attr.checkableBehavior};
        public static final int[] MenuItem = {android.R.attr.icon, android.R.attr.enabled, android.R.attr.id, android.R.attr.checked, android.R.attr.visible, android.R.attr.menuCategory, android.R.attr.orderInCategory, android.R.attr.title, android.R.attr.titleCondensed, android.R.attr.alphabeticShortcut, android.R.attr.numericShortcut, android.R.attr.checkable, android.R.attr.onClick, R.attr.actionLayout, R.attr.actionProviderClass, R.attr.actionViewClass, R.attr.alphabeticModifiers, R.attr.contentDescription, R.attr.iconTint, R.attr.iconTintMode, R.attr.numericModifiers, R.attr.showAsAction, R.attr.tooltipText};
        public static final int[] MenuView = {android.R.attr.windowAnimationStyle, android.R.attr.itemTextAppearance, android.R.attr.horizontalDivider, android.R.attr.verticalDivider, android.R.attr.headerBackground, android.R.attr.itemBackground, android.R.attr.itemIconDisabledAlpha, R.attr.preserveIconSpacing, R.attr.subMenuArrow};
        public static final int[] NavigationView = {android.R.attr.background, android.R.attr.fitsSystemWindows, android.R.attr.maxWidth, R.attr.elevation, R.attr.headerLayout, R.attr.itemBackground, R.attr.itemHorizontalPadding, R.attr.itemIconPadding, R.attr.itemIconTint, R.attr.itemTextAppearance, R.attr.itemTextColor, R.attr.menu};
        public static final int[] PopupWindow = {android.R.attr.popupBackground, android.R.attr.popupAnimationStyle, R.attr.overlapAnchor};
        public static final int[] PopupWindowBackgroundState = {R.attr.state_above_anchor};
        public static final int[] RecycleListView = {R.attr.paddingBottomNoButtons, R.attr.paddingTopNoTitle};
        public static final int[] RecyclerView = {android.R.attr.orientation, android.R.attr.descendantFocusability, R.attr.fastScrollEnabled, R.attr.fastScrollHorizontalThumbDrawable, R.attr.fastScrollHorizontalTrackDrawable, R.attr.fastScrollVerticalThumbDrawable, R.attr.fastScrollVerticalTrackDrawable, R.attr.layoutManager, R.attr.reverseLayout, R.attr.spanCount, R.attr.stackFromEnd};
        public static final int[] ScrimInsetsFrameLayout = {R.attr.insetForeground};
        public static final int[] ScrollingViewBehavior_Layout = {R.attr.behavior_overlapTop};
        public static final int[] SearchView = {android.R.attr.focusable, android.R.attr.maxWidth, android.R.attr.inputType, android.R.attr.imeOptions, R.attr.closeIcon, R.attr.commitIcon, R.attr.defaultQueryHint, R.attr.goIcon, R.attr.iconifiedByDefault, R.attr.layout, R.attr.queryBackground, R.attr.queryHint, R.attr.searchHintIcon, R.attr.searchIcon, R.attr.submitBackground, R.attr.suggestionRowLayout, R.attr.voiceIcon};
        public static final int[] Snackbar = {R.attr.snackbarButtonStyle, R.attr.snackbarStyle};
        public static final int[] SnackbarLayout = {android.R.attr.maxWidth, R.attr.elevation, R.attr.maxActionInlineWidth};
        public static final int[] Spinner = {android.R.attr.entries, android.R.attr.popupBackground, android.R.attr.prompt, android.R.attr.dropDownWidth, R.attr.popupTheme};
        public static final int[] StateListDrawable = {android.R.attr.dither, android.R.attr.visible, android.R.attr.variablePadding, android.R.attr.constantSize, android.R.attr.enterFadeDuration, android.R.attr.exitFadeDuration};
        public static final int[] StateListDrawableItem = {android.R.attr.drawable};
        public static final int[] SwitchCompat = {android.R.attr.textOn, android.R.attr.textOff, android.R.attr.thumb, R.attr.showText, R.attr.splitTrack, R.attr.switchMinWidth, R.attr.switchPadding, R.attr.switchTextAppearance, R.attr.thumbTextPadding, R.attr.thumbTint, R.attr.thumbTintMode, R.attr.track, R.attr.trackTint, R.attr.trackTintMode};
        public static final int[] TabItem = {android.R.attr.icon, android.R.attr.layout, android.R.attr.text};
        public static final int[] TabLayout = {R.attr.tabBackground, R.attr.tabContentStart, R.attr.tabGravity, R.attr.tabIconTint, R.attr.tabIconTintMode, R.attr.tabIndicator, R.attr.tabIndicatorAnimationDuration, R.attr.tabIndicatorColor, R.attr.tabIndicatorFullWidth, R.attr.tabIndicatorGravity, R.attr.tabIndicatorHeight, R.attr.tabInlineLabel, R.attr.tabMaxWidth, R.attr.tabMinWidth, R.attr.tabMode, R.attr.tabPadding, R.attr.tabPaddingBottom, R.attr.tabPaddingEnd, R.attr.tabPaddingStart, R.attr.tabPaddingTop, R.attr.tabRippleColor, R.attr.tabSelectedTextColor, R.attr.tabTextAppearance, R.attr.tabTextColor, R.attr.tabUnboundedRipple};
        public static final int[] TextAppearance = {android.R.attr.textSize, android.R.attr.typeface, android.R.attr.textStyle, android.R.attr.textColor, android.R.attr.textColorHint, android.R.attr.textColorLink, android.R.attr.shadowColor, android.R.attr.shadowDx, android.R.attr.shadowDy, android.R.attr.shadowRadius, android.R.attr.fontFamily, R.attr.fontFamily, R.attr.textAllCaps};
        public static final int[] TextInputLayout = {android.R.attr.textColorHint, android.R.attr.hint, R.attr.boxBackgroundColor, R.attr.boxBackgroundMode, R.attr.boxCollapsedPaddingTop, R.attr.boxCornerRadiusBottomEnd, R.attr.boxCornerRadiusBottomStart, R.attr.boxCornerRadiusTopEnd, R.attr.boxCornerRadiusTopStart, R.attr.boxStrokeColor, R.attr.boxStrokeWidth, R.attr.counterEnabled, R.attr.counterMaxLength, R.attr.counterOverflowTextAppearance, R.attr.counterTextAppearance, R.attr.errorEnabled, R.attr.errorTextAppearance, R.attr.helperText, R.attr.helperTextEnabled, R.attr.helperTextTextAppearance, R.attr.hintAnimationEnabled, R.attr.hintEnabled, R.attr.hintTextAppearance, R.attr.passwordToggleContentDescription, R.attr.passwordToggleDrawable, R.attr.passwordToggleEnabled, R.attr.passwordToggleTint, R.attr.passwordToggleTintMode};
        public static final int[] ThemeEnforcement = {android.R.attr.textAppearance, R.attr.enforceMaterialTheme, R.attr.enforceTextAppearance};

        @Deprecated
        public static final int[] Toolbar = {android.R.attr.gravity, android.R.attr.minHeight, R.attr.buttonGravity, R.attr.collapseContentDescription, R.attr.collapseIcon, R.attr.contentInsetEnd, R.attr.contentInsetEndWithActions, R.attr.contentInsetLeft, R.attr.contentInsetRight, R.attr.contentInsetStart, R.attr.contentInsetStartWithNavigation, R.attr.logo, R.attr.logoDescription, R.attr.maxButtonHeight, R.attr.navigationContentDescription, R.attr.navigationIcon, R.attr.popupTheme, R.attr.subtitle, R.attr.subtitleTextAppearance, R.attr.subtitleTextColor, R.attr.title, R.attr.titleMargin, R.attr.titleMarginBottom, R.attr.titleMarginEnd, R.attr.titleMarginStart, R.attr.titleMarginTop, R.attr.titleMargins, R.attr.titleTextAppearance, R.attr.titleTextColor};
        public static final int[] View = {android.R.attr.theme, android.R.attr.focusable, R.attr.paddingEnd, R.attr.paddingStart, R.attr.theme};
        public static final int[] ViewBackgroundHelper = {android.R.attr.background, R.attr.backgroundTint, R.attr.backgroundTintMode};
        public static final int[] ViewStubCompat = {android.R.attr.id, android.R.attr.layout, android.R.attr.inflatedId};
    }

    public static final class xml {
        public static final int provider_paths = 0x7f120000;
    }
}
